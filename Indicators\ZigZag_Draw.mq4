//+------------------------------------------------------------------+
//|                                                       ZigZag.mq4 |
//|                             Copyright 2000-2025, MetaQuotes Ltd. |
//|                                              http://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "2000-2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property strict

#property indicator_chart_window
#property indicator_buffers 3 // Zvýšeno kvůli bufferům pro šipky
#property indicator_color1  Red   // Barva ZigZag linie
#property indicator_color2  Red   // Barva šipek nahoru (vrcholy) - červená
#property indicator_color3  Blue  // Barva šipek dolů (dna) - modrá

//---- indicator parameters
input int InpDepth=12;     // Depth
input int InpDeviation=5;  // Deviation
input int InpBackstep=3;   // Backstep
input double ArrowOffset=30; // Offset šipek od vrcholu (v bodech) - zvětšeno pro větší odsazení

//---- indicator buffers
double ExtZigzagBuffer[];
double ExtHighBuffer[];
double ExtLowBuffer[];
double ExtArrowUpBuffer[]; // Buffer pro šipky nad vrcholy
double ExtArrowDownBuffer[]; // Buffer pro šipky pod vrcholy

//--- globals
int    ExtLevel=3; // recounting's depth

//+------------------------------------------------------------------+
//| Custom indicator initialization function                          |
//+------------------------------------------------------------------+
int OnInit()
{
   if(InpBackstep>=InpDepth)
   {
      Print("Backstep cannot be greater or equal to Depth");
      return(INIT_FAILED);
   }

   //--- 5 bufferů (ZigZag + High + Low + ArrowUp + ArrowDown)
   IndicatorBuffers(5);

   //---- drawing settings
   SetIndexStyle(0,DRAW_SECTION); // ZigZag linie
   SetIndexStyle(1,DRAW_ARROW, STYLE_SOLID, 2); // Šipky nahoru
   SetIndexStyle(2,DRAW_ARROW, STYLE_SOLID, 2); // Šipky dolů
   SetIndexArrow(1, 233); // Symbol šipky nahoru
   SetIndexArrow(2, 234); // Symbol šipky dolů

   //---- indicator buffers
   SetIndexBuffer(0, ExtZigzagBuffer);
   SetIndexBuffer(1, ExtHighBuffer);
   SetIndexBuffer(2, ExtLowBuffer);
   SetIndexBuffer(3, ExtArrowUpBuffer);
   SetIndexBuffer(4, ExtArrowDownBuffer);
   SetIndexEmptyValue(0, 0.0);
   SetIndexEmptyValue(3, 0.0);
   SetIndexEmptyValue(4, 0.0);

   //---- indicator short name
   IndicatorShortName("ZigZag("+string(InpDepth)+","+string(InpDeviation)+","+string(InpBackstep)+")");

   //---- smazání starých objektů při inicializaci
   DeleteOldObjects();

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator calculation function                             |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   int i, limit, counterZ, whatlookfor=0;
   int back, pos, lasthighpos=0, lastlowpos=0;
   double extremum;
   double curlow=0.0, curhigh=0.0, lasthigh=0.0, lastlow=0.0;

   //--- check for history and inputs
   if(rates_total<InpDepth || InpBackstep>=InpDepth)
      return(0);

   //--- first calculations
   if(prev_calculated==0)
      limit=InitializeAll();
   else
   {
      i=counterZ=0;
      while(counterZ<ExtLevel && i<100)
      {
         if(ExtZigzagBuffer[i]!=0.0)
            counterZ++;
         i++;
      }
      if(counterZ==0)
         limit=InitializeAll();
      else
      {
         limit=i-1;
         if(ExtLowBuffer[i]!=0.0)
         {
            curlow=ExtLowBuffer[i];
            whatlookfor=1;
         }
         else
         {
            curhigh=ExtHighBuffer[i];
            whatlookfor=-1;
         }
         for(i=limit-1; i>=0; i--)
         {
            ExtZigzagBuffer[i]=0.0;
            ExtLowBuffer[i]=0.0;
            ExtHighBuffer[i]=0.0;
            ExtArrowUpBuffer[i]=0.0;
            ExtArrowDownBuffer[i]=0.0;
         }
      }
   }

   //--- main loop
   for(i=limit; i>=0; i--)
   {
      //--- find lowest low
      extremum=low[iLowest(NULL,0,MODE_LOW,InpDepth,i)];
      if(extremum==lastlow)
         extremum=0.0;
      else
      {
         lastlow=extremum;
         if(low[i]-extremum>InpDeviation*Point)
            extremum=0.0;
         else
         {
            for(back=1; back<=InpBackstep; back++)
            {
               pos=i+back;
               if(ExtLowBuffer[pos]!=0 && ExtLowBuffer[pos]>extremum)
                  ExtLowBuffer[pos]=0.0;
            }
         }
      }
      if(low[i]==extremum)
         ExtLowBuffer[i]=extremum;
      else
         ExtLowBuffer[i]=0.0;

      //--- find highest high
      extremum=high[iHighest(NULL,0,MODE_HIGH,InpDepth,i)];
      if(extremum==lasthigh)
         extremum=0.0;
      else
      {
         lasthigh=extremum;
         if(extremum-high[i]>InpDeviation*Point)
            extremum=0.0;
         else
         {
            for(back=1; back<=InpBackstep; back++)
            {
               pos=i+back;
               if(ExtHighBuffer[pos]!=0 && ExtHighBuffer[pos]<extremum)
                  ExtHighBuffer[pos]=0.0;
            }
         }
      }
      if(high[i]==extremum)
         ExtHighBuffer[i]=extremum;
      else
         ExtHighBuffer[i]=0.0;
   }

   //--- final cutting
   if(whatlookfor==0)
   {
      lastlow=0.0;
      lasthigh=0.0;
   }
   else
   {
      lastlow=curlow;
      lasthigh=curhigh;
   }

   //--- smazání starých textových objektů
   DeleteOldObjects();

   //--- pole pro uchování pozic a cen vrcholů/dnů
   int zigzagPoints[];
   double zigzagPrices[];
   datetime zigzagTimes[];
   int pointCount=0;

   //--- sběr všech ZigZag bodů a přidání šipek
   for(i=limit; i>=0; i--)
   {
      switch(whatlookfor)
      {
         case 0:
            if(lastlow==0.0 && lasthigh==0.0)
            {
               if(ExtHighBuffer[i]!=0.0)
               {
                  lasthigh=high[i];
                  lasthighpos=i;
                  whatlookfor=-1;
                  ExtZigzagBuffer[i]=lasthigh;
                  ExtArrowUpBuffer[i]=high[i] + ArrowOffset*Point; // Šipka nad vrchol

                  // Uložení bodu
                  ArrayResize(zigzagPoints, pointCount+1);
                  ArrayResize(zigzagPrices, pointCount+1);
                  ArrayResize(zigzagTimes, pointCount+1);
                  zigzagPoints[pointCount]=i;
                  zigzagPrices[pointCount]=lasthigh;
                  zigzagTimes[pointCount]=time[i];
                  pointCount++;
               }
               if(ExtLowBuffer[i]!=0.0)
               {
                  lastlow=low[i];
                  lastlowpos=i;
                  whatlookfor=1;
                  ExtZigzagBuffer[i]=lastlow;
                  ExtArrowDownBuffer[i]=low[i] - ArrowOffset*Point; // Šipka pod dno

                  // Uložení bodu
                  ArrayResize(zigzagPoints, pointCount+1);
                  ArrayResize(zigzagPrices, pointCount+1);
                  ArrayResize(zigzagTimes, pointCount+1);
                  zigzagPoints[pointCount]=i;
                  zigzagPrices[pointCount]=lastlow;
                  zigzagTimes[pointCount]=time[i];
                  pointCount++;
               }
            }
            break;
         case 1:
            if(ExtLowBuffer[i]!=0.0 && ExtLowBuffer[i]<lastlow && ExtHighBuffer[i]==0.0)
            {
               ExtZigzagBuffer[lastlowpos]=0.0;
               ExtArrowDownBuffer[lastlowpos]=0.0;
               lastlowpos=i;
               lastlow=ExtLowBuffer[i];
               ExtZigzagBuffer[i]=lastlow;
               ExtArrowDownBuffer[i]=low[i] - ArrowOffset*Point; // Šipka pod dno

               // Uložení bodu
               ArrayResize(zigzagPoints, pointCount+1);
               ArrayResize(zigzagPrices, pointCount+1);
               ArrayResize(zigzagTimes, pointCount+1);
               zigzagPoints[pointCount]=i;
               zigzagPrices[pointCount]=lastlow;
               zigzagTimes[pointCount]=time[i];
               pointCount++;
            }
            if(ExtHighBuffer[i]!=0.0 && ExtLowBuffer[i]==0.0)
            {
               lasthigh=ExtHighBuffer[i];
               lasthighpos=i;
               ExtZigzagBuffer[i]=lasthigh;
               ExtArrowUpBuffer[i]=high[i] + ArrowOffset*Point; // Šipka nad vrchol
               whatlookfor=-1;

               // Uložení bodu
               ArrayResize(zigzagPoints, pointCount+1);
               ArrayResize(zigzagPrices, pointCount+1);
               ArrayResize(zigzagTimes, pointCount+1);
               zigzagPoints[pointCount]=i;
               zigzagPrices[pointCount]=lasthigh;
               zigzagTimes[pointCount]=time[i];
               pointCount++;
            }
            break;
         case -1:
            if(ExtHighBuffer[i]!=0.0 && ExtHighBuffer[i]>lasthigh && ExtLowBuffer[i]==0.0)
            {
               ExtZigzagBuffer[lasthighpos]=0.0;
               ExtArrowUpBuffer[lasthighpos]=0.0;
               lasthighpos=i;
               lasthigh=ExtHighBuffer[i];
               ExtZigzagBuffer[i]=lasthigh;
               ExtArrowUpBuffer[i]=high[i] + ArrowOffset*Point; // Šipka nad vrchol

               // Uložení bodu
               ArrayResize(zigzagPoints, pointCount+1);
               ArrayResize(zigzagPrices, pointCount+1);
               ArrayResize(zigzagTimes, pointCount+1);
               zigzagPoints[pointCount]=i;
               zigzagPrices[pointCount]=lasthigh;
               zigzagTimes[pointCount]=time[i];
               pointCount++;
            }
            if(ExtLowBuffer[i]!=0.0 && ExtHighBuffer[i]==0.0)
            {
               lastlow=ExtLowBuffer[i];
               lastlowpos=i;
               ExtZigzagBuffer[i]=lastlow;
               ExtArrowDownBuffer[i]=low[i] - ArrowOffset*Point; // Šipka pod dno
               whatlookfor=1;

               // Uložení bodu
               ArrayResize(zigzagPoints, pointCount+1);
               ArrayResize(zigzagPrices, pointCount+1);
               ArrayResize(zigzagTimes, pointCount+1);
               zigzagPoints[pointCount]=i;
               zigzagPrices[pointCount]=lastlow;
               zigzagTimes[pointCount]=time[i];
               pointCount++;
            }
            break;
      }
   }

   //--- vytvoření textů pro všechny ZigZag segmenty
   for(int j=0; j<pointCount-1; j++)
   {
      CreateDistanceText(zigzagPoints[j], zigzagPoints[j+1], zigzagPrices[j], zigzagPrices[j+1], zigzagTimes[j], zigzagTimes[j+1], j);
   }

   return(rates_total);
}

//+------------------------------------------------------------------+
//| Initialize all buffers                                           |
//+------------------------------------------------------------------+
int InitializeAll()
{
   ArrayInitialize(ExtZigzagBuffer, 0.0);
   ArrayInitialize(ExtHighBuffer, 0.0);
   ArrayInitialize(ExtLowBuffer, 0.0);
   ArrayInitialize(ExtArrowUpBuffer, 0.0);
   ArrayInitialize(ExtArrowDownBuffer, 0.0);
   return(Bars-InpDepth);
}

//+------------------------------------------------------------------+
//| Delete old text objects                                          |
//+------------------------------------------------------------------+
void DeleteOldObjects()
{
   for(int i=ObjectsTotal()-1; i>=0; i--)
   {
      string name=ObjectName(i);
      if(StringFind(name, "Distance")>=0)
         ObjectDelete(name);
   }
}

//+------------------------------------------------------------------+
//| Create text object with distance between points                  |
//+------------------------------------------------------------------+
void CreateDistanceText(int pos1, int pos2, double price1, double price2, datetime time1, datetime time2, int index)
{
   // Výpočet vzdálenosti v pips (bodech)
   double distance = MathAbs(price1 - price2) / Point;

   // Výpočet středního času a ceny
   datetime midTime = time1 + (time2 - time1) / 2;
   double midPrice = (price1 + price2) / 2;

   // Ověření, že střední hodnoty jsou platné
   if (midTime <= 0 || midPrice <= 0)
   {
      Print("Chyba: Neplatné střední hodnoty pro text popisku.");
      return;
   }

   // Název textového objektu
   string textName = "Distance" + IntegerToString(index);

   // Vytvoření textového objektu
   ObjectCreate(0, textName, OBJ_TEXT, 0, midTime, midPrice);
   ObjectSetString(0, textName, OBJPROP_TEXT, StringFormat("%.0f pips", distance));
   ObjectSetInteger(0, textName, OBJPROP_COLOR, clrOrange);
   ObjectSetInteger(0, textName, OBJPROP_ANCHOR, ANCHOR_CENTER);

   // Jemné doladění pozice textu (posun nahoru nebo dolů podle směru čáry)
   double priceOffset = 50 * Point; // Posun textu o 50 bodů
   if (price2 > price1) // Pokud čára stoupá, posuneme text nahoru
      ObjectSetDouble(0, textName, OBJPROP_PRICE, midPrice + priceOffset);
   else // Pokud čára klesá, posuneme text dolů
      ObjectSetDouble(0, textName, OBJPROP_PRICE, midPrice - priceOffset);
}

//+------------------------------------------------------------------+