A - zdrojovy soubor = oDMĚNY
B - cilový soubor = dENÍK UZAVŘENÝCH SMLUV

2a. V případě že číslo v B nenajde, doplni do posledního řádku následující údaje: (NOVÁ SMLOUVA)
  z D do B (číslo smlouvy),
  z B do E (jméno klienta), 
  u J do D (Počátek smlouvy)
  z L do H (Roční pojistné)
  z S do I (Hrubá provize)
  z bunky fix M4, do K

2X: Hled<PERSON> šíslo smlouvy ve sloupci A - pokud nenajde (nová smlouva) tak:
  z A do B (číslo smlouvy), 
  z C do E (jméno klienta), 
  u F do D (Počátek smlouvy)
  z H do H (Roční pojistné)
  z I do I (Hrubá provize)
      do J (Čistá provize - 2. sloupec) - dle 1. nebo druhé tabulky 2. tabulky - tam kde neni na konci v K nebo L neni Bieronski je jen 1 smlouva za 40 nebo 30
  z bunky fix M4, do K


2b. V případě že číslo smlouvy v B najde (existující smlouva) tak zkopíruj

  z S do I (Hrubá provize)
  z U do J (čistá provize)
  z bunky fix M4, do K

2X: (stejně jako u 1. verze
  z H do H (Roční pojistné)
  z I do I (Hrubá provize)
  Mesíc přenosu odpovídá názvu souboru
  

   V případě že v I nebo J nebo K je již nějaká hodnota, tak se přeskočí a hledá další hodnota ve sloupci 
   O, P, Q a dále po 3 sloupcích (R, S, T) dokud nenajde prázdně bunky.



