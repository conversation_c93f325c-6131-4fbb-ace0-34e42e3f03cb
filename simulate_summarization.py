import pandas as pd
import numpy as np

def simulate_ft_provize_summarization():
    try:
        # Načti Excel soubor
        file_path = 'FT provize old.xlsx'
        df = pd.read_excel(file_path, sheet_name='All-In-One')
        
        print('=== SIMULACE SUMARIZACE PRO OBDOBÍ 01-2025 ===')
        print(f'Celkový počet řádků: {len(df)}')
        
        # 1. FILTROVÁNÍ podle období "01-2025" (sloupec L)
        selected_period = '01-2025'
        filtered_df = df[df['Období'] == selected_period].copy()
        
        print(f'Po filtrování podle období "{selected_period}": {len(filtered_df)} řádků')
        print()
        
        # Zobraz ukázku dat před sumarizací
        print('=== UKÁZKA DAT PŘED SUMARIZACÍ ===')
        sample_data = filtered_df[['<PERSON><PERSON><PERSON> smlouvy', 'Pod<PERSON><PERSON> na provizi', 'Odměna', 'Období']].head(10)
        print(sample_data.to_string(index=False))
        print()
        
        # 2. SUMARIZACE podle čísla smlouvy (sloupec A)
        print('=== PROCES SUMARIZACE ===')
        
        # Najdi duplicitní smlouvy
        duplicate_contracts = filtered_df['Číslo smlouvy'].value_counts()
        duplicates = duplicate_contracts[duplicate_contracts > 1]
        
        print(f'Smlouvy s více záznamy ({len(duplicates)}):')
        for contract, count in duplicates.head(10).items():
            print(f'  {contract}: {count} záznamů')
        print()
        
        # Proveď sumarizaci
        summarized = filtered_df.groupby('Číslo smlouvy').agg({
            'Podíl na provizi': 'sum',  # Sečti procenta (J)
            'Odměna': 'sum',           # Sečti odměny (K)
            'pojistitel': 'first',     # Zachovej první hodnotu
            'Příjmení z.': 'first',
            'Jméno z.': 'first',
            'Období': 'first',
            'Kod ziskatel': 'first'
        }).reset_index()
        
        # Přidej počet sumarizovaných položek
        item_counts = filtered_df['Číslo smlouvy'].value_counts()
        summarized['Počet_položek'] = summarized['Číslo smlouvy'].map(item_counts)
        
        print(f'Po sumarizaci: {len(summarized)} řádků')
        print()
        
        # 3. VÝSLEDKY SUMARIZACE
        print('=== VÝSLEDKY SUMARIZACE ===')
        
        # Zobraz sumarizované záznamy (pouze ty, které byly skutečně sloučeny)
        merged_records = summarized[summarized['Počet_položek'] > 1]
        print(f'Počet sloučených záznamů: {len(merged_records)}')
        print()
        
        if len(merged_records) > 0:
            print('Příklady sloučených záznamů:')
            display_cols = ['Číslo smlouvy', 'Podíl na provizi', 'Odměna', 'Počet_položek']
            print(merged_records[display_cols].head(10).to_string(index=False))
            print()
        
        # 4. STATISTIKY
        print('=== STATISTIKY ===')
        print(f'Původní počet řádků: {len(filtered_df)}')
        print(f'Finální počet řádků: {len(summarized)}')
        print(f'Ušetřeno řádků: {len(filtered_df) - len(summarized)}')
        print(f'Počet sloučených smluv: {len(merged_records)}')
        print()
        
        # Kontrola součtů
        original_sum_j = filtered_df['Podíl na provizi'].sum()
        original_sum_k = filtered_df['Odměna'].sum()
        summarized_sum_j = summarized['Podíl na provizi'].sum()
        summarized_sum_k = summarized['Odměna'].sum()
        
        print('Kontrola součtů:')
        print(f'  Podíl na provizi (J): {original_sum_j:.2f} → {summarized_sum_j:.2f} (rozdíl: {abs(original_sum_j - summarized_sum_j):.6f})')
        print(f'  Odměna (K): {original_sum_k:.2f} → {summarized_sum_k:.2f} (rozdíl: {abs(original_sum_k - summarized_sum_k):.6f})')
        print()
        
        # 5. DETAILNÍ PŘÍKLAD
        if len(merged_records) > 0:
            print('=== DETAILNÍ PŘÍKLAD SUMARIZACE ===')
            example_contract = merged_records.iloc[0]['Číslo smlouvy']
            
            print(f'Příklad pro smlouvu: {example_contract}')
            original_records = filtered_df[filtered_df['Číslo smlouvy'] == example_contract]
            summarized_record = summarized[summarized['Číslo smlouvy'] == example_contract]
            
            print('Původní záznamy:')
            cols_to_show = ['Číslo smlouvy', 'Podíl na provizi', 'Odměna', 'pojistitel']
            print(original_records[cols_to_show].to_string(index=False))
            print()
            
            print('Sumarizovaný záznam:')
            print(summarized_record[cols_to_show + ['Počet_položek']].to_string(index=False))
            print()
            
            # Ukázka výpočtu
            sum_j = original_records['Podíl na provizi'].sum()
            sum_k = original_records['Odměna'].sum()
            print(f'Výpočet: {len(original_records)} záznamů → J={sum_j:.3f}, K={sum_k:.2f}')
        
        return summarized
        
    except Exception as e:
        print(f'Chyba při simulaci: {e}')
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = simulate_ft_provize_summarization()
