# Použití MQL Tools jako hlavní kompilátor pro MQL

Tento dokument popisuje, jak používat rozšíření MQL Tools ve Visual Studio Code jako hlavní kompilátor pro MQL4/MQL5 soubory.

## Konfigurace

MQL Tools je již nakonfigurováno jako hlavní kompilátor pro MQL soubory. Konfigurace zahrnuje:

1. Automatickou kompilaci při uložení souboru
2. Zobrazení zprávy o kompilaci
3. Zobrazení průběhu kompilace
4. Kompilaci při sestavení (Ctrl+Shift+B)
5. Použití skriptu pro kompilaci (otevře MetaEditor a klikne na tlačítko "Compile")

## Klávesové zkratky

Pro usnadnění práce s MQL Tools byly nakonfigurovány následující klávesové zkratky:

- `Ctrl+F7` - Kompilace aktuálního souboru
- `Ctrl+Shift+F7` - Kompilace aktuálního souboru pomocí skriptu (otevře MetaEditor)
- `Ctrl+Alt+M` - Otevření aktuálního souboru v MetaEditoru
- `Ctrl+Alt+T` - Otevření obchodního terminálu MetaTrader

## Použití z příkazové palety

Příkazy MQL Tools jsou dostupné také z příkazové palety (Ctrl+Shift+P):

1. `MQL: Compile File` - Kompilace aktuálního souboru
2. `MQL: Compile with Script` - Kompilace aktuálního souboru pomocí skriptu
3. `MQL: Open in MetaEditor` - Otevření aktuálního souboru v MetaEditoru
4. `MQL: Open Terminal` - Otevření obchodního terminálu MetaTrader
5. `MQL: Create Configuration` - Vytvoření konfiguračního souboru
6. `MQL: Add Icons to the Theme` - Přidání ikon do motivu

## Použití z kontextového menu

V průzkumníku souborů jsou dostupné následující položky kontextového menu:

- `New MQL File` - Vytvoření nového MQL souboru
- `Compile` - Kompilace vybraného souboru
- `Compile with Script` - Kompilace vybraného souboru pomocí skriptu
- `Open in 'MetaEditor'` - Otevření vybraného souboru v MetaEditoru
- `Show/hide ex4/ex5 files` - Zobrazení nebo skrytí zkompilovaných ex4/ex5 souborů
- `Insert MQH as #include` - Vložení vybraného mqh souboru jako #include direktivy

## Použití z úloh (Tasks)

Pro spuštění úloh stiskněte `Ctrl+Shift+B` nebo použijte menu `Terminal > Run Task`:

- `MQL: Compile Current File` - Kompilace aktuálního souboru
- `MQL: Compile Current File with Script` - Kompilace aktuálního souboru pomocí skriptu
- `MQL: Open in MetaEditor` - Otevření aktuálního souboru v MetaEditoru
- `MQL: Open Terminal` - Otevření obchodního terminálu MetaTrader

## Další funkce MQL Tools

- **Kontrola syntaxe** - MQL Tools kontroluje syntaxi MQL souborů v reálném čase
- **Zvýraznění syntaxe** - MQL Tools poskytuje zvýraznění syntaxe pro MQL soubory
- **IntelliSense** - MQL Tools poskytuje IntelliSense pro MQL soubory
- **Rychlý Print()** - Pro usnadnění ladění kódu pomocí funkce Print()
- **Styler** - Formátování kódu bez přidání mezer do řetězců data a barvy
- **Skládání regionů** - Možnost ručně označit oblasti kódu, které lze pak sbalit a rozbalit

## Poznámky

- MQL Tools není plnohodnotnou náhradou MetaEditoru. Hlavní zpracování MQL kódu provádí rozšíření "C/C++".
- Pokud některé funkce přestanou fungovat s MQL kódem, ale rozšíření "MQL Tools" nebylo aktualizováno, zkontrolujte, zda bylo aktualizováno rozšíření "C/C++".
