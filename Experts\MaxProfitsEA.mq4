//+------------------------------------------------------------------+
//|                                                    MaxProfitsEA |
//|                      Copyright 2024, Custom Scripts              |
//+------------------------------------------------------------------+
#property strict

// Globální proměnná pro prefix objektů
string EA_Tag = "MaxProfits";   // Prefix pro všechny objekty na grafu

// Vstupní proměnné
input int    MagicNo           = 777;        // Magické číslo pro sledování obchodů
input int    MaxOrdersToTrack  = 5;          // Maximální počet obchodů k sledování
input bool   FileExport        = true;       // Povolit export do souboru
input string FileName          = "MaxProfits.txt"; // Název souboru pro export
input bool   DebugMode         = false;      // Zapnout debug výpisy
input color  ProfitTextColor   = clrWhite;   // Barva textu pro zobrazení profitů
input int    FontSize          = 10;         // Velikost písma pro zobrazení profitů
input string FontName          = "Arial";    // Název písma pro zobrazení profitů

// Definice indexů pro pole MaxProfit
#define MP_MAX_PROFIT  0  // Maximální profit
#define MP_TICKET      1  // Číslo obchodu (ticket)
#define MP_TIME        2  // Čas otevření obchodu (jako timestamp)
#define MP_OPEN_PRICE  3  // Otevírací cena obchodu
#define MP_TYPE        4  // Typ obchodu (0=BUY, 1=SELL)

// Globální proměnné pro sledování profitu
double   MaxProfit[][5];          // 2D pole pro ukládání informací o obchodech
double   LastSavedProfit[];       // Pole pro ukládání posledních zapsaných hodnot MaxProfit
int      SessionOrderCount = 0;   // Počet obchodů v aktuální session
datetime EAStartTime;             // Čas spuštění EA

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    // Inicializace času spuštění EA
    EAStartTime = TimeCurrent();
    
    // Inicializace polí MaxProfit a LastSavedProfit
    ArrayResize(MaxProfit, MaxOrdersToTrack);
    ArrayResize(LastSavedProfit, MaxOrdersToTrack);
    
    // Inicializace pole MaxProfit
    for(int i = 0; i < MaxOrdersToTrack; i++) {
        MaxProfit[i][MP_MAX_PROFIT] = 0.0;  // Maximální profit
        MaxProfit[i][MP_TICKET] = 0.0;      // Číslo obchodu (ticket)
        MaxProfit[i][MP_TIME] = 0.0;        // Čas otevření obchodu (jako timestamp)
        MaxProfit[i][MP_OPEN_PRICE] = 0.0;  // Otevírací cena obchodu
        MaxProfit[i][MP_TYPE] = -1.0;       // Typ obchodu (-1=neinicializovaný, 0=BUY, 1=SELL)
        
        // Inicializace pole LastSavedProfit
        LastSavedProfit[i] = 0.0;
    }
    
    SessionOrderCount = 0;
    
    Print("MaxProfitsEA spuštěn v čase: ", TimeToString(EAStartTime));
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // Odstranění všech objektů s prefixem EA_Tag
    for(int i = ObjectsTotal() - 1; i >= 0; i--) {
        string name = ObjectName(0, i);
        if(StringFind(name, EA_Tag) == 0) {
            ObjectDelete(0, name);
        }
    }
    
    Print("MaxProfitsEA deinicializován.");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    static datetime Last_M1_Open = 0;
    
    // Sledování maximálních profitů v reálném čase
    TrackRealTimeMaxProfits();
    
    // Aktualizace komentáře s informacemi o MaxProfitech
    UpdateMaxProfitComment();
    
    // Blok pro provedení při každém otevření nové M1 svíčky
    datetime currentM1BarTime = iTime(_Symbol, PERIOD_M1, 0);
    
    if(Last_M1_Open != currentM1BarTime) {
        Last_M1_Open = currentM1BarTime;
        
        // Aktualizace MaxProfit na základě High/Low svíčky
        UpdateMaxProfitOnNewCandle();
    }
}

//+------------------------------------------------------------------+
//| Funkce pro sledování maximálních profitů v reálném čase          |
//+------------------------------------------------------------------+
void TrackRealTimeMaxProfits() {
    // Nejprve aktualizujeme seznam obchodů (přidáme nové obchody)
    TrackSessionOrders();
    
    // Procházíme všechny obchody v poli MaxProfit
    for(int i = 0; i < SessionOrderCount; i++) {
        int ticket = (int)MaxProfit[i][MP_TICKET];
        double openPrice = MaxProfit[i][MP_OPEN_PRICE];
        int orderType = (int)MaxProfit[i][MP_TYPE];
        
        // Pokud nemáme platný ticket nebo otevírací cenu, přeskočíme
        if(ticket <= 0 || openPrice <= 0.0) continue;
        
        // Získáme aktuální cenu
        double currentBid = MarketInfo(_Symbol, MODE_BID);
        double currentAsk = MarketInfo(_Symbol, MODE_ASK);
        
        // Vypočítáme aktuální profit v bodech
        double currentProfit = 0.0;
        
        if(orderType == OP_BUY) {
            // Pro BUY obchod je aktuální profit rozdíl mezi Bid a otevírací cenou
            currentProfit = (currentBid - openPrice) / Point;
        } else if(orderType == OP_SELL) {
            // Pro SELL obchod je aktuální profit rozdíl mezi otevírací cenou a Ask
            currentProfit = (openPrice - currentAsk) / Point;
        }
        
        // Aktualizujeme maximální profit, pokud je aktuální profit větší
        if(currentProfit > MaxProfit[i][MP_MAX_PROFIT]) {
            MaxProfit[i][MP_MAX_PROFIT] = currentProfit;
            
            if(DebugMode) {
                string typeStr = (orderType == OP_BUY) ? "BUY" : "SELL";
                Print("Aktualizován maximální profit v reálném čase pro ticket #", ticket,
                      ": ", MaxProfit[i][MP_MAX_PROFIT],
                      " bodů (Bid=", currentBid, ", Ask=", currentAsk, ", OpenPrice=", openPrice, ", Type=", typeStr, ")");
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Funkce pro aktualizaci MaxProfit na základě High/Low svíčky      |
//+------------------------------------------------------------------+
void UpdateMaxProfitOnNewCandle() {
    // Získáme High a Low poslední svíčky
    double high = High[1]; // Hodnota High předchozí svíčky
    double low = Low[1];   // Hodnota Low předchozí svíčky
    
    // Procházíme všechny obchody v poli MaxProfit
    for(int i = 0; i < SessionOrderCount; i++) {
        int ticket = (int)MaxProfit[i][MP_TICKET];
        double openPrice = MaxProfit[i][MP_OPEN_PRICE];
        int orderType = (int)MaxProfit[i][MP_TYPE];
        
        // Pokud nemáme platný ticket nebo otevírací cenu, přeskočíme
        if(ticket <= 0 || openPrice <= 0.0) continue;
        
        // Použití pomocné funkce pro výpočet potenciálního profitu
        double potentialProfit = CalculateOrderProfit(openPrice, orderType, high, low);
        
        // Aktualizujeme maximální profit, pokud je potenciální profit větší
        if(potentialProfit > MaxProfit[i][MP_MAX_PROFIT]) {
            MaxProfit[i][MP_MAX_PROFIT] = potentialProfit;
            
            if(DebugMode) {
                string typeStr = (orderType == OP_BUY) ? "BUY" : "SELL";
                Print("Aktualizován maximální profit pro ticket #", ticket,
                      " na základě High/Low svíčky: ", MaxProfit[i][MP_MAX_PROFIT],
                      " bodů (High=", high, ", Low=", low, ", OpenPrice=", openPrice, ", Type=", typeStr, ")");
            }
        }
    }
    
    // Zápis MaxProfitů do souboru po aktualizaci
    if(FileExport) {
        WriteMaxProfitsToFile();
    }
}

//+------------------------------------------------------------------+
//| Funkce pro zápis MaxProfitů do souboru                           |
//+------------------------------------------------------------------+
void WriteMaxProfitsToFile() {
    // Kontrola, zda máme nějaké nové nebo změněné záznamy
    bool hasNewOrChangedRecords = false;
    
    for(int i = 0; i < SessionOrderCount; i++) {
        int ticket = (int)MaxProfit[i][MP_TICKET];
        double profit = MaxProfit[i][MP_MAX_PROFIT];
        
        // Kontrola, zda je záznam nový nebo změněný
        if(ticket > 0 && profit > 0.0 && profit != LastSavedProfit[i]) {
            hasNewOrChangedRecords = true;
            break;
        }
    }
    
    // Pokud nemáme žádné nové nebo změněné záznamy, ukončíme funkci
    if(!hasNewOrChangedRecords) {
        return;
    }
    
    // Otevření souboru pro přidání textu (FILE_WRITE = přepsat, FILE_READ|FILE_WRITE = přidat)
    int fileHandle = FileOpen(FileName, FILE_READ|FILE_WRITE|FILE_TXT);
    
    // Kontrola, zda se soubor podařilo otevřít
    if(fileHandle != INVALID_HANDLE) {
        // Přesun na konec souboru
        FileSeek(fileHandle, 0, SEEK_END);
        
        // Aktuální datum a čas pro záznam
        string currentDateTime = TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES);
        
        // Zápis hlavičky s aktuálním datem a časem
        FileWrite(fileHandle, "--- Záznam MaxProfitů z " + currentDateTime + " ---");
        
        // Procházení všech obchodů v poli MaxProfit
        for(int i = 0; i < SessionOrderCount; i++) {
            int ticket = (int)MaxProfit[i][MP_TICKET];
            double profit = MaxProfit[i][MP_MAX_PROFIT];
            
            // Zapisujeme pouze obchody, které mají platný ticket, nenulový profit a jsou nové nebo změněné
            if(ticket > 0 && profit > 0.0 && profit != LastSavedProfit[i]) {
                // Formátování profitu v bodech
                string profitText = DoubleToString(profit, 1);
                
                // Formátování data a času ve formátu DD.MM.RRRR HH:MM
                datetime orderTime = (datetime)MaxProfit[i][MP_TIME];
                string dateStr = StringFormat("%02d.%02d.%04d %02d:%02d",
                                             TimeDay(orderTime),
                                             TimeMonth(orderTime),
                                             TimeYear(orderTime),
                                             TimeHour(orderTime),
                                             TimeMinute(orderTime));
                
                // Získání otevírací ceny a typu obchodu
                double openPrice = MaxProfit[i][MP_OPEN_PRICE];
                int orderType = (int)MaxProfit[i][MP_TYPE];
                string typeStr = (orderType == OP_BUY) ? "BUY" : "SELL";
                
                // Formátování řádku ve stejném formátu jako v komentářích
                string line = StringFormat("#%d - %s - %s - %s - MaxProfit: %s bodů",
                                          ticket,
                                          dateStr,
                                          typeStr,
                                          DoubleToString(openPrice, Digits), // Formátování otevírací ceny na správný počet desetinných míst
                                          profitText);
                
                // Zápis řádku do souboru
                FileWrite(fileHandle, line);
                
                // Aktualizace posledního zapsaného profitu
                LastSavedProfit[i] = profit;
            }
        }
        
        // Přidání prázdného řádku pro oddělení záznamů
        FileWrite(fileHandle, "");
        
        // Zavření souboru
        FileClose(fileHandle);
        
        if(DebugMode) {
            Print("Nové MaxProfity byly zapsány do souboru ", FileName);
        }
    } else {
        Print("Chyba při otevírání souboru ", FileName, ": ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Funkce pro sledování obchodů od spuštění EA                      |
//+------------------------------------------------------------------+
void TrackSessionOrders() {
    // Procházení všech otevřených pozic
    int total = OrdersTotal();
    
    // Procházení všech otevřených pozic
    for(int i = 0; i < total; i++) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            // Sledujeme pouze obchody na aktuálním symbolu s naším Magic Number
            if(OrderSymbol() == _Symbol && OrderMagicNumber() == MagicNo) {
                // Pouze market obchody (BUY/SELL)
                if(OrderType() <= OP_SELL) {
                    // Kontrola, zda byl obchod otevřen po spuštění EA
                    if(OrderOpenTime() >= EAStartTime) {
                        // Výpočet aktuálního profitu včetně swapů a komisí
                        double currentProfit = (OrderProfit() + OrderSwap() + OrderCommission()) / 10.0; // Dělíme 10 pro správnou hodnotu
                        int ticket = OrderTicket();
                        
                        // Kontrola, zda je obchod již v seznamu
                        if(!IsOrderInMaxProfitArray(ticket) && SessionOrderCount < MaxOrdersToTrack) {
                            // Pokud obchod není v seznamu a máme místo, přidáme ho
                            datetime orderTime = OrderOpenTime();
                            double openPrice = NormalizeDouble(OrderOpenPrice(), Digits); // Normalizace na počet desetinných míst Digits
                            int orderType = OrderType();
                            
                            AddOrderToMaxProfitArray(ticket, orderTime, openPrice, orderType, currentProfit);
                        }
                    }
                }
            }
        }
    }
    
    // Procházení uzavřených obchodů v historii
    int historyTotal = OrdersHistoryTotal();
    
    for(int i = 0; i < historyTotal; i++) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_HISTORY)) {
            // Sledujeme pouze obchody na aktuálním symbolu s naším Magic Number
            if(OrderSymbol() == _Symbol && OrderMagicNumber() == MagicNo) {
                // Pouze market obchody (BUY/SELL)
                if(OrderType() <= OP_SELL) {
                    // Kontrola, zda byl obchod otevřen po spuštění EA
                    if(OrderOpenTime() >= EAStartTime) {
                        int ticket = OrderTicket();
                        double profit = (OrderProfit() + OrderSwap() + OrderCommission()) / 10.0; // Dělíme 10 pro správnou hodnotu
                        
                        // Kontrola, zda je obchod již v seznamu
                        if(!IsOrderInMaxProfitArray(ticket)) {
                            // Pokud obchod není v seznamu, přidáme ho
                            datetime orderTime = OrderOpenTime();
                            double openPrice = NormalizeDouble(OrderOpenPrice(), Digits); // Normalizace na počet desetinných míst Digits
                            int orderType = OrderType();
                            
                            if(SessionOrderCount >= MaxOrdersToTrack) {
                                // Posun starších obchodů
                                for(int j = 0; j < MaxOrdersToTrack-1; j++) {
                                    for(int k = 0; k < 5; k++) {
                                        MaxProfit[j][k] = MaxProfit[j+1][k];
                                    }
                                }
                                
                                // Přidání nového obchodu na poslední pozici
                                AddOrderToMaxProfitArray(ticket, orderTime, openPrice, orderType, profit, MaxOrdersToTrack-1);
                            } else {
                                // Přidání nového obchodu na konec pole
                                AddOrderToMaxProfitArray(ticket, orderTime, openPrice, orderType, profit);
                            }
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Pomocná funkce pro kontrolu, zda je obchod již v seznamu          |
//+------------------------------------------------------------------+
bool IsOrderInMaxProfitArray(int ticket) {
    for(int j = 0; j < SessionOrderCount; j++) {
        if((int)MaxProfit[j][MP_TICKET] == ticket) {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Pomocná funkce pro přidání obchodu do pole MaxProfit             |
//+------------------------------------------------------------------+
void AddOrderToMaxProfitArray(int ticket, datetime orderTime, double openPrice, int orderType, double currentProfit = 0.0, int index = -1) {
    // Pokud index není zadán, použijeme SessionOrderCount a zvýšíme ho
    int arrayIndex = (index >= 0) ? index : SessionOrderCount;
    
    MaxProfit[arrayIndex][MP_MAX_PROFIT] = 0.0;
    MaxProfit[arrayIndex][MP_TICKET] = (double)ticket;
    MaxProfit[arrayIndex][MP_TIME] = (double)orderTime;
    MaxProfit[arrayIndex][MP_OPEN_PRICE] = openPrice;
    MaxProfit[arrayIndex][MP_TYPE] = (double)orderType;
    
    if(index < 0) SessionOrderCount++;
    
    if(DebugMode) {
        string typeStr = (orderType == OP_BUY) ? "BUY" : "SELL";
        string orderStatus = (index < 0) ? "nový" : "uzavřený";
        Print("Přidán ", orderStatus, " obchod do session: Ticket=", ticket,
              ", Profit=", currentProfit,
              ", Čas=", TimeToString(orderTime),
              ", OpenPrice=", openPrice,
              ", Type=", typeStr);
    }
}

//+------------------------------------------------------------------+
//| Pomocná funkce pro výpočet profitu obchodu                       |
//+------------------------------------------------------------------+
double CalculateOrderProfit(double openPrice, int orderType, double high, double low) {
    if(orderType == OP_BUY) {
        return (high - openPrice) / Point;
    } else if(orderType == OP_SELL) {
        return (openPrice - low) / Point;
    }
    return 0.0;
}

//+------------------------------------------------------------------+
//| Funkce pro aktualizaci komentáře s informacemi o MaxProfitech    |
//+------------------------------------------------------------------+
void UpdateMaxProfitComment() {
    string commentText = "";
    
    // Přidání informací o obchodech od spuštění EA
    commentText += StringFormat("MaxProfity obchodů s Magic Number %d od spuštění EA:\n", MagicNo);
    
    if(SessionOrderCount > 0) {
        // Máme nějaké obchody od spuštění EA
        for(int i = 0; i < SessionOrderCount; i++) {
            // Formátování profitu v bodech
            string profitText = DoubleToString(MaxProfit[i][MP_MAX_PROFIT], 1);
            
            // Formátování data a času ve formátu DD.MM.RRRR HH:MM
            datetime orderTime = (datetime)MaxProfit[i][MP_TIME];
            string dateStr = StringFormat("%02d.%02d.%04d %02d:%02d",
                                         TimeDay(orderTime),
                                         TimeMonth(orderTime),
                                         TimeYear(orderTime),
                                         TimeHour(orderTime),
                                         TimeMinute(orderTime));
            
            // Získání otevírací ceny a typu obchodu
            double openPrice = MaxProfit[i][MP_OPEN_PRICE];
            int orderType = (int)MaxProfit[i][MP_TYPE];
            string typeStr = (orderType == OP_BUY) ? "BUY" : "SELL";
            
            // Přidání informace o obchodu ve formátu "číslo obchodu - Datum a čas otevření - Typ obchodu - OpenPrice - MaxProfit v bodech"
            commentText += StringFormat("#%d - %s - %s - %s - MaxProfit: %s bodů\n",
                                       (int)MaxProfit[i][MP_TICKET],
                                       dateStr,
                                       typeStr,
                                       DoubleToString(openPrice, Digits), // Formátování otevírací ceny na správný počet desetinných míst
                                       profitText);
            
            // Vytvoření textového objektu pro každý obchod
            string objName = EA_Tag + "_MaxProfit_" + IntegerToString(i);
            
            if(ObjectFind(0, objName) < 0) {
                ObjectCreate(0, objName, OBJ_LABEL, 0, 0, 0);
                ObjectSetInteger(0, objName, OBJPROP_CORNER, 1); // Pravý horní roh
                ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, 10);
                ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, 20 + i * (FontSize + 5));
                ObjectSetInteger(0, objName, OBJPROP_FONTSIZE, FontSize);
                ObjectSetInteger(0, objName, OBJPROP_COLOR, ProfitTextColor);
            }
            
            // Aktualizace textu objektu
            string displayText = StringFormat("#%d - %s - MaxProfit: %s bodů",
                                            (int)MaxProfit[i][MP_TICKET],
                                            typeStr,
                                            profitText);
            ObjectSetString(0, objName, OBJPROP_TEXT, displayText);
        }
    } else {
        // Nemáme žádné obchody od spuštění EA, zobrazíme nulové hodnoty
        string zeroText = DoubleToString(0.0, Digits); // Formátování nuly podle počtu desetinných míst instrumentu
        commentText += "Žádné obchody - " + zeroText + "b\n";
    }
    
    // Zobrazení komentáře
    Comment(commentText);
}
