//+------------------------------------------------------------------+
//|                                Volatility Super Trader (VST) 1.0 |
//|                                 Copyright 2024, 2025, <PERSON><PERSON>  |
//|                                     email: <EMAIL> |
//+------------------------------------------------------------------+

#property description "Volatility Super Trader (VST)"
#property version     "1.0"
#property copyright   "Copyright 2024–2025, Al<PERSON>"
#property link        "mailto:<EMAIL>"

#property strict

// Globální proměnná pro prefix objektů
string EA_Tag = "Level_Trader";     // Prefix pro všechny objekty na grafu
string EA_Trade_Tag = "VST ";        // Prefix pro všechny objekty na grafu

//+------------------------------------------------------------------+
//| Pomocná funkce pro podmíněný Print (pouze pokud není StrategyTester) |
//+------------------------------------------------------------------+
void SafePrint(string message) {
    if(!StrategyTester) {
        Print(message);
    }
}

void SafePrint(string message1, string message2) {
    if(!StrategyTester) {
        Print(message1, message2);
    }
}

void SafePrint(string message1, string message2, string message3) {
    if(!StrategyTester) {
        Print(message1, message2, message3);
    }
}

void SafePrint(string message1, string message2, string message3, string message4) {
    if(!StrategyTester) {
        Print(message1, message2, message3, message4);
    }
}

void SafePrint(string message1, string message2, string message3, string message4, string message5) {
    if(!StrategyTester) {
        Print(message1, message2, message3, message4, message5);
    }
}

void SafePrint(string message1, string message2, string message3, string message4, string message5, string message6) {
    if(!StrategyTester) {
        Print(message1, message2, message3, message4, message5, message6);
    }
}

void SafePrint(string message1, string message2, string message3, string message4, string message5, string message6, string message7) {
    if(!StrategyTester) {
        Print(message1, message2, message3, message4, message5, message6, message7);
    }
}

void SafePrint(string message1, string message2, string message3, string message4, string message5, string message6, string message7, string message8) {
    if(!StrategyTester) {
        Print(message1, message2, message3, message4, message5, message6, message7, message8);
    }
}

void SafePrint(string message1, string message2, string message3, string message4, string message5, string message6, string message7, string message8, string message9) {
    if(!StrategyTester) {
        Print(message1, message2, message3, message4, message5, message6, message7, message8, message9);
    }
}

void SafePrint(string message1, string message2, string message3, string message4, string message5, string message6, string message7, string message8, string message9, string message10) {
    if(!StrategyTester) {
        Print(message1, message2, message3, message4, message5, message6, message7, message8, message9, message10);
    }
}

//+------------------------------------------------------------------+
//| Pomocné funkce pro podmíněné vykreslování objektů               |
//+------------------------------------------------------------------+
bool SafeObjectCreate(long chart_id, string name, ENUM_OBJECT type, int sub_window, datetime time1, double price1) {
    if(StrategyTester) return true; // V testovacím módu simulujeme úspěch
    return ObjectCreate(chart_id, name, type, sub_window, time1, price1);
}

bool SafeObjectDelete(long chart_id, string name) {
    if(StrategyTester) return true; // V testovacím módu simulujeme úspěch
    return ObjectDelete(chart_id, name);
}

void SafeComment(string text) {
    if(!StrategyTester) {
        Comment(text);
    }
}

// Přetížené SafePrint funkce pro různé typy parametrů
void SafePrint(string message1, double value1) {
    if(!StrategyTester) {
        Print(message1, value1);
    }
}

void SafePrint(string message1, int value1) {
    if(!StrategyTester) {
        Print(message1, value1);
    }
}

void SafePrint(string message1, double value1, string message2, double value2, string message3, bool value3) {
    if(!StrategyTester) {
        Print(message1, value1, message2, value2, message3, value3 ? "ANO" : "NE");
    }
}

void SafePrint(string message1, double value1, string message2, string value2, string message3, string value3, string message4, bool value4) {
    if(!StrategyTester) {
        Print(message1, value1, message2, value2, message3, value3, message4, value4 ? "ANO" : "NE");
    }
}

void SafePrint(string message1, bool value1, string message2, bool value2) {
    if(!StrategyTester) {
        Print(message1, value1 ? "YES" : "NO", message2, value2 ? "YES" : "NO");
    }
}

void SafePrint(string message1, bool value1, string message2, bool value2, string message3, double value3) {
    if(!StrategyTester) {
        Print(message1, value1 ? "YES" : "NO", message2, value2 ? "YES" : "NO", message3, value3);
    }
}

void SafePrint(string message1, bool value1) {
    if(!StrategyTester) {
        Print(message1, value1 ? "YES" : "NO");
    }
}

//#import "kernel32.dll"
//int WinExec(const uchar &cmd[], uint mode);
//#import

#import "shell32.dll"
int ShellExecuteW(int hwnd, string lpOperation, string lpFile, string lpParameters, string lpDirectory, int nShowCmd);
#import

// ZZ Setup
extern string sep1 = "=== ZZ Setup ===";   //--------------------- ZZ Setup -------------------------------

extern double StopLoss   = 50;    // Stop Loss v bodech
extern double TakeProfit = 100;   // Take Profit v bodech

extern int ZZ_Depth     = 12;   // Depth
extern int ZZ_Deviation = 5;    // Deviation
extern int ZZ_Backstep  = 3;    // Backstep

extern int    Stoch_K           = 13;     // Perioda %K pro Stochastic
extern int    Stoch_D           = 3;      // Perioda %D pro Stochastic
extern int    Stoch_Slowing     = 8;      // Zpomalení Stochastic
extern double StochOverbought   = 80;     // Úvěrově překoupení
extern double StochOversold     = 20;     // Úvěrově přeprodanosti
extern bool   BandCheck         = true;   // Podmínka pro kontrolu Envelopes
extern double EnvelopeDeviation = 0.5;    // Odchylka Envelopes
extern int    EnvelopePeriod    = 20;     // Perioda Envelopes
extern int    EALastValues      = 5;
extern int    FileGenLastValues = 200;
extern bool   FileExport        = true;

enum ChartCornerZZ {
    LeftUpperZZ  = 0,
    RightUpperZZ = 1,
    LeftLowerZZ  = 2,
    RightLowerZZ = 3
};

// Enum pro výběr zvuku
enum AlertSoundType {
    Alert_None,
    Alert_Alert,
    Alert_Alert2,
    Alert_Tick,
    Alert_OK,
    Alert_Timeout,
    Alert_News,
    Alert_Stops
};

// Enum pro výběr minimální ZZ vzdálenosti pro obchodování
enum ZZDistanceLimit {
    ZZ_Green,    // Zelená (ATR/3) - největší vzdálenost
    ZZ_Orange,   // Oranžová (ATR/4) - střední vzdálenost
    ZZ_Red       // Červená (ATR/5) - nejmenší vzdálenost
};

// Enum pro typ obchodu
enum Trade_Type {
    NORMAL,      // Normální obchody
    REVERSE      // Obrácené obchody
};

// Enum pro typ pokynů
enum Order_Type {
    MARKET_LIMIT,  // Market/Limit podle podmínek
    MARKET_ONLY,   // Pouze Market pokyny
    LIMIT_ONLY     // Pouze Limit pokyny
};

input AlertSoundType  alertSound        = Alert_None;   // Výběr zvuku
input ChartCornerZZ   SelectedCorner    = RightUpperZZ;
input double          minZZDistance1    = 10.0;       // hranice pro červenou/oranžovou
input double          minZZDistance2    = 20.0;       // hranice pro oranžovou/zelenou
input ZZDistanceLimit MinZZDistForTrade = ZZ_Green;   // Minimální ZZ vzdálenost pro obchod
input double          RiskCapital       = 1.0;        // Procento kapitálu riskované na jeden obchod
input int             MagicNo1          = 770;        // Magické číslo pro 1. obchod
input bool            MA100_dist        = false;      // Kontrola vzdálenosti od MA100 na M5
input bool            MA100_1_dist      = false;      // Kontrola vzdálenosti od MA100 na M1
input bool            StrategyTester    = false;      // Mód pro testování (bez vykreslování)

// === Nové obchodní nastavení ===
input string          sep2              = "=== Obchodní nastavení ===";   //----- Obchodní nastavení -----
input int             D_Max_Loss        = 2;          // Max počet ztrátových obchodů MG1 v rámci dne
input Trade_Type      Trade_Type_Mode   = NORMAL;     // Výběr typu obchodu (Normal/Reverse)
input int             Reverse_Loss      = 2;          // Počet ztrát po kterých se provede 1 reverzní obchod - pouze v módu NORMAL!
input bool            TradeNo1          = true;       // Nastavení TradeNo1 - ON/OFF
input bool            TradeNo2          = true;       // Nastavení TradeNo2 - ON/OFF
input int             MagicNo2          = 880;        // Magické číslo pro 2. obchod
input double          TP1_RRR           = 2.0;        // RRR pro 1. obchod
input double          TP2_RRR           = 3.9;        // RRR pro 2. obchod
input double          MG2_Distance_Mult = 0.6;        // Multiplikátor pro MG2 vzdálenostní podmínku (ATR14_D1 * hodnota)
input Order_Type      OrderType         = MARKET_LIMIT; // Typ pokynů (Market/Limit, Market, Limit)

// ZigZag buffer
double zzHigh[], zzLow[];
int    zzHandle;

double zzHighValue, zzLowValue, zzPeakPrice, zzLastL, zzLastH, zzLastPeak;
double zzHighValue2, zzLowValue2, zzPeakPrice2;
double StochK, StochD;
double UpperEnvelope, LowerEnvelope;

double zzValues[3][10], zzDistanceM5, zzLastDistanceM5;  // [period][index] - 3 periody, max 10 ZZ hodnot
int    zzIndexes[3][10];                                // [period][index] - indexy ZZ hodnot
string zzMessages[3][10];                               // [period][index] - zprávy ZZ hodnot
string zzTypes[];
string zzInfos[];
string zzOutputs[];

// Globální proměnné pro MA100 vzdálenost na M5
double ema100_dist = 0.0;
double ema100_dist_abs = 0.0;

// Globální proměnné pro MA100 vzdálenost na M1
double ema100_1_dist = 0.0;
double ema100_1_dist_abs = 0.0;

// Globální proměnné pro sledování obchodů
int    Trades_Mg1_Count = 0;        // Celkový počet provedených obchodů - MagicNo1 v aktuálním dni
int    Trades_Mg1_Loss = 0;         // Celkový počet ztrátových obchodů - MagicNo1 v aktuálním dni
int    Consecutive_BUY_Loss = 0;    // Počet po sobě jdoucích BUY ztrát
int    Consecutive_SELL_Loss = 0;   // Počet po sobě jdoucích SELL ztrát
bool   Reverse_Trade_Pending = false; // Flag pro čekající reverzní obchod
bool   Reverse_Trade_Active = false;  // Flag pro aktivní reverzní obchod (pro end-of-day close)
double Dist_D0_High = 0.0;          // Vzdálenost Close(1) od D0 High
double Dist_D0_Low = 0.0;           // Vzdálenost Close(1) od D0 Low
double mg2DistanceLimit = 0.0;      // Globální proměnná pro MG2 vzdálenostní limit (ATR14_D1 * MG2_Distance_Mult)

// Globální proměnné pro ATR a limity barev
double ATR14_D1       = 0.0;   // Hodnota ATR(14) na D1
double greenATRLimit  = 0.0;   // Limit pro zelenou barvu (ATR14_D1/3)
double orangeATRLimit = 0.0;   // Limit pro oranžovou barvu (ATR14_D1/4)
double redATRLimit    = 0.0;   // Limit pro červenou barvu (ATR14_D1/5)

// Formátovací řetězce pro ATR hodnoty
string formatDigits      = "";   // Formátovací řetězec podle počtu desetinných míst
string formatWithStar    = "";   // Formát pro vybranou hodnotu s hvězdičkou
string formatWithoutStar = "";   // Formát pro nevybranou hodnotu
string baseFormat        = "";   // Formát pro základní ATR text

// Globální proměnné pro obchodní logiku
int    pendingBuyTicket   = 0;       // Ticket čekajícího Buy pokynu MG1
int    pendingSellTicket  = 0;       // Ticket čekajícího Sell pokynu MG1
double pendingBuyPrice    = 0.0;     // Cena čekajícího Buy pokynu MG1
double pendingSellPrice   = 0.0;     // Cena čekajícího Sell pokynu MG1
int    pendingBuyTicket_MG2   = 0;   // Ticket čekajícího Buy pokynu MG2
int    pendingSellTicket_MG2  = 0;   // Ticket čekajícího Sell pokynu MG2
double pendingBuyPrice_MG2    = 0.0; // Cena čekajícího Buy pokynu MG2
double pendingSellPrice_MG2   = 0.0; // Cena čekajícího Sell pokynu MG2
bool   orderConditionsMet = false;   // Indikátor, zda jsou splněny podmínky pro obchodování

// Globální proměnné pro sledování profitu
double OrderProfits[];             // Pole pro ukládání profitů jednotlivých obchodů
double MaxOrderProfit = 0.0;       // Maximální dosažený profit

// Globální proměnné pro informace o nejbližším levelu
string NearestLevelInfo = "";      // Informace o nejbližším levelu ve formátu "EMA100-H1"

// Globální proměnné pro sledování obchodů od spuštění EA
datetime EAStartTime;              // Čas spuštění EA

// Definice indexů pro pole MaxProfit
#define MP_MAX_PROFIT  0  // Maximální profit
#define MP_TICKET      1  // Číslo obchodu (ticket)
#define MP_TIME        2  // Čas otevření obchodu (jako timestamp)
#define MP_OPEN_PRICE  3  // Otevírací cena obchodu
#define MP_TYPE        4  // Typ obchodu (0=BUY, 1=SELL)

double   MaxProfit[5][5];          // 2D pole pro ukládání informací o obchodech
double   LastSavedProfit[5];       // Pole pro ukládání posledních zapsaných hodnot MaxProfit
int      SessionOrderCount = 0;    // Počet obchodů v aktuální session

// Globální proměnné pro historii obchodů
int    LastOrderTickets[5];        // Tickety posledních 5 obchodů
double LastOrderMaxProfits[5];     // Maximální profity posledních 5 obchodů
int    LastOrderCount = 0;         // Počet uložených obchodů v historii

string     maName, HLineName;
static int lastPeriod = 0;
datetime   lastUpdate = 0;

// Enum pro volbu rohu
enum ChartCorner {
    LeftUpper  = 0,   // Levý horní roh
    RightUpper = 1,   // Pravý horní roh
    LeftLower  = 2,   // Levý dolní roh
    RightLower = 3    // Pravý dolní roh
};

// --- Vstupní proměnné pro pozice a vzhled ---
input ChartCorner Corner         = LeftUpper;   // Umístění v rohu
input int         CellWidth      = 60;          // Šířka buňky
input int         CellHeight     = 16;          // Výška buňky
input color       GripColor      = clrYellow;   // Barva GripPointu
input int         TableMargin    = 20;          // Okraj od okraje grafu
input bool        AutoAdjustSize = true;        // Automaticky přizpůsobit velikost rozlišení

// --- Vstupní proměnné pro zvýraznění buněk ---
input double HighlightDistance = 1.5;   // Minimální vzdálenost od ceny pro zvýraznění

// --- Vstupní proměnné pro obchodní hodiny ---
input bool UseTradeHours   = true;   // Povolit obchodování pouze v určitých hodinách
input int  TradeHoursStart = 2;      // Začátek obchodních hodin (0-23)
input int  TradeHoursEnd   = 22;     // Konec obchodních hodin (0-23)

// Aktuální hodnoty velikosti buněk (mohou být změněny podle rozlišení)
int currentCellWidth;
int currentCellHeight;

string MA_labels_Y[3] = {"M5", "H1", "H4"};
string D_labels_Y[3]  = {"D0", "D1", "W1"};

// Proměnné pro stav zobrazení tabulek
bool showMATable = true;
bool showDTable  = true;
bool showZZTable = true;

// Proměnné pro pozice
int    cornerX       = 20;                      // Výchozí X pozice řídícího bodu
int    cornerY       = 30;                      // Výchozí Y pozice řídícího bodu (posunuto výše)
string gripPointName = EA_Tag + "_GripPoint";   // Název GripPointu
int    chartWidth, chartHeight;                 // Rozměry grafu

// Proměnné pro blikání a zvýraznění buněk
datetime    lastBlinkTime       = 0;
bool        blinkState          = false;
double      LastNearestDistance = 0;          // Nejmenší vzdálenost od Close(1) z funkce FindClosestToCloseAndColorMap
int         lastBestRow         = -1;         // Index řádku nejbližší buňky
int         lastBestCol         = -1;         // Index sloupce nejbližší buňky
bool        lastIsMA            = true;       // True, pokud je nejbližší buňka v MA tabulce, False pro D tabulku
int         lastTableType       = 0;          // 0 = MA tabulka, 1 = D tabulka, 2 = ZZ tabulka
input color DefaultColor        = clrWhite;   // Výchozí barva buňky
input color CloseRangeColor     = clrGreen;   // Barva blízké hodnoty
input color BlinkColor          = clrRed;     // Barva blikající hodnoty
input color BlinkAltColor       = clrWhite;   // Alternativní barva při blikání
input int   DefaultFontSize     = 10;         // Výchozí velikost písma v tabulce
input int   HighlightFontSize   = 12;         // Zvýrazněná velikost písma
input int   BlinkIntervalSec    = 1;          // Interval blikání v sekundách
input bool  DebugMode           = false;      // Zapnout debug výpisy

// Globální proměnné pro velikosti polí
int maRows, maCols;   // Počet řádků a sloupců MA tabulky
int dRows, dCols;     // Počet řádků a sloupců D tabulky
int zzRows, zzCols;   // Počet řádků a sloupců ZZ tabulky

struct LabelInfo {
    string text;
    color  clr;
    int    period;
    int    maType;
};

LabelInfo MA_labels_X[4] =
    {
        {"EMA21",  clrBlue,       21,  MODE_EMA},
        {"EMA100", clrViolet,     100, MODE_EMA},
        {"EMA200", clrOrangeRed,  200, MODE_EMA},
        {"SMA200", clrDarkOrange, 200, MODE_SMA}
};

LabelInfo D_labels_X[3] =
    {
        {"High", clrLightSalmon},
        {"Low",  clrLightSalmon},
        {"HL50", clrViolet     }
};

string ZZ_labels_Y[3]  = {"M5", "H4", "D1"};

LabelInfo ZZ_labels_X[3] =
    {
        {"zzHigh", clrLightSalmon},
        {"zzLow",  clrLightSalmon},
        {"zzHL50", clrViolet     }
};

double MA_Values[4][3];
color  MA_Colors[4][3];
int    MA_FontSizes[4][3];

double D_Values[3][3];
color  D_Colors[3][3];
int    D_FontSizes[3][3];

double ZZ_Table_Values[3][3];  // [zzTypeIndex][periodIndex] - zzHigh/zzLow/zzHL50 x M5/H4/D1
color  ZZ_Colors[3][3];       // [zzTypeIndex][periodIndex]
int    ZZ_FontSizes[3][3];    // [zzTypeIndex][periodIndex]

string TF_labels[3][3] =
    {
        {"M5",  "H1", "H4"},
        {"M15", "H4", "D1"},
        {"H1",  "H4", "W1"}
};
int currentTFIndex = 0;

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
int OnInit() {
    // Inicializace aktuálních hodnot velikosti buněk z vstupních proměnných
    currentCellWidth  = CellWidth;
    currentCellHeight = CellHeight;

    // Automatické přizpůsobení velikosti buněk podle rozlišení
    AdjustCellSizeForResolution();

    // Inicializace globálních proměnných pro velikosti polí
    maRows = ArraySize(MA_labels_Y);
    maCols = ArraySize(MA_labels_X);
    dRows  = ArraySize(D_labels_Y);
    dCols  = ArraySize(D_labels_X);

    // Inicializace velikostí hodnot v tabulkách
    int maValRows = ArrayRange(MA_Values, 0);
    int maValCols = ArrayRange(MA_Values, 1);
    int dValRows  = ArrayRange(D_Values, 0);
    int dValCols  = ArrayRange(D_Values, 1);

    // Inicializace barev a velikostí písma s použitím globálních proměnných
    for(int i = 0; i < maCols; i++)
        for(int j = 0; j < maRows; j++) {
            MA_Colors[i][j]    = DefaultColor;
            MA_FontSizes[i][j] = DefaultFontSize;
        }

    for(int i = 0; i < dCols; i++)
        for(int j = 0; j < dRows; j++) {
            D_Colors[i][j]    = DefaultColor;
            D_FontSizes[i][j] = DefaultFontSize;
        }

    DeleteTable("MACell");
    DeleteTable("DCell");
    DeleteTable("ZZCell");

    // Nastavení výchozích souřadnic podle zvoleného rohu
    SetInitialCoordinates();

    // Vytvoření GripPointu
    CreateGripPoint();

    // Vytvoření tlačítek s výchozí zelenou barvou (tabulky jsou zobrazeny)
    // Všechny pozice jsou relativní k cornerX a cornerY

    // Výpočet vertikálního odsazení pro tlačítka a tabulky
    int buttonY  = cornerY + 10;                              // Tlačítka jsou výše (zmenšeno z 20)
    int maTableY = buttonY + 20;                              // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)
    int dButtonY = maTableY + (currentCellHeight * 4) + 10;   // D tlačítko je pod MA tabulkou s mezerou
    int dTableY  = dButtonY + 20;                             // D tabulka začíná 20 pixelů pod D tlačítkem (zvětšeno z 15)

    if(!StrategyTester) {
        // Vytvoření tlačítek pro MA tabulku
        CreateToggleButton("ToggleMA", cornerX + 0, buttonY, "MA Tabulka", clrForestGreen);
        CreateToggleButton("TFNext", cornerX + 90, buttonY, "TF >>", clrSlateGray);    // Posunuto blíže (z 100)
        CreateToggleButton("TFPrev", cornerX + 180, buttonY, "TF <<", clrSlateGray);   // Posunuto blíže (z 180)

        UpdateMAValues();
        UpdateDValues();
        // ZZ hodnoty se aktualizují v ZigZagInfo funkci

        // Inicializace barev a zvýraznění buněk
        FindClosestToCloseAndColorMap();
    }

    // Inicializace proměnné pro blikání bez časovače
    lastBlinkTime = TimeCurrent();

    // Inicializace formátovacích řetězců pro ATR hodnoty
    formatDigits      = StringFormat("%%.%df", Digits);
    formatWithStar    = StringFormat("* %s: %s  ", "%s", formatDigits);
    formatWithoutStar = StringFormat("%s: %s  ", "%s", formatDigits);
    baseFormat        = StringFormat("ATR(14) D1: %s - ", formatDigits);

    // Inicializace ATR hodnot s zaokrouhlením na počet desetinných míst
    ATR14_D1       = NormalizeDouble(iATR(NULL, PERIOD_D1, 14, 1), Digits);
    greenATRLimit  = NormalizeDouble(ATR14_D1 / 3, Digits);
    orangeATRLimit = NormalizeDouble(ATR14_D1 / 4, Digits);
    redATRLimit    = NormalizeDouble(ATR14_D1 / 5, Digits);
    mg2DistanceLimit = NormalizeDouble(ATR14_D1 * MG2_Distance_Mult, Digits);  // Inicializace MG2 vzdálenostního limitu

    // Inicializace času spuštění EA a resetování polí pro sledování obchodů
    EAStartTime = TimeCurrent();

    // Inicializace pole MaxProfit
    for(int i = 0; i < 5; i++) {
        MaxProfit[i][MP_MAX_PROFIT] = 0.0;  // Maximální profit
        MaxProfit[i][MP_TICKET] = 0.0;      // Číslo obchodu (ticket)
        MaxProfit[i][MP_TIME] = 0.0;        // Čas otevření obchodu (jako timestamp)
        MaxProfit[i][MP_OPEN_PRICE] = 0.0;  // Otevírací cena obchodu
        MaxProfit[i][MP_TYPE] = -1.0;       // Typ obchodu (-1=neinicializovaný, 0=BUY, 1=SELL)

        // Inicializace pole LastSavedProfit
        LastSavedProfit[i] = 0.0;
    }

    SessionOrderCount = 0;

    SafePrint("EA spuštěn v čase: ", TimeToString(EAStartTime));

    // Inicializace ZigZag hodnot a zobrazení pro M5
    ZigZagInfo(PERIOD_M5);
    zzDistanceM5 = NormalizeDouble((zzValues[0][0] - zzValues[0][1]), Digits);

    if(!StrategyTester) {
        UpdateZZDistanceLabel(zzDistanceM5);

        // Zobrazení MA tabulky (výchozí stav je zobrazeno)
        if(showMATable) {
            DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, maRows, maCols, cornerX, maTableY, clrWhite);
        }

        // Vytvoření tlačítka pro D tabulku s výchozí zelenou barvou
        CreateToggleButton("ToggleD", cornerX, dButtonY, "D Tabulka", clrForestGreen);

        // Vytvoření tlačítka pro ZZ tabulku - posunuto níže
        int zzButtonY = dTableY + (currentCellHeight * 3) + 30;  // Zvětšeno z 10 na 30 pro větší mezeru
        CreateToggleButton("ToggleZZ", cornerX, zzButtonY, "ZZ Tabulka", clrForestGreen);

        // Zobrazení D tabulky (výchozí stav je zobrazeno)
        if(showDTable) {
            DrawTable("DCell", D_labels_X, D_labels_Y, D_Values, D_Colors, D_FontSizes, dRows, dCols, cornerX, dTableY, clrWhite);
        }

        // Inicializace ZZ tabulky
        zzRows = ArraySize(ZZ_labels_Y);
        zzCols = ArraySize(ZZ_labels_X);
        int zzTableY = zzButtonY + 20;  // Pod ZZ tlačítkem

        // Zobrazení ZZ tabulky (výchozí stav je zobrazeno)
        if(showZZTable) {
            DrawTable("ZZCell", ZZ_labels_X, ZZ_labels_Y, ZZ_Table_Values, ZZ_Colors, ZZ_FontSizes, zzRows, zzCols, cornerX, zzTableY, clrWhite);
        }

        UpdateTFLabels();
    }

    // Inicializace počítadel obchodů z historie při spuštění EA
    InitializeTradingCountersFromHistory();

    // Nastavení události pro změnu velikosti grafu
    ChartSetInteger(0, CHART_EVENT_OBJECT_CREATE, true);
    ChartSetInteger(0, CHART_EVENT_OBJECT_DELETE, true);

    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // Odstranění všech objektů s prefixem EA_Tag
    for(int i = ObjectsTotal() - 1; i >= 0; i--) {
        string name = ObjectName(0, i);
        if(StringFind(name, EA_Tag) == 0) {
            ObjectDelete(0, name);
        }
    }

    SafePrint("Expert Advisor deinicializován.");   // Výpis při deinicializaci EA
}

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void OnTick() {
    static datetime Last_M1_Open = 0;
    static datetime Last_M5_Open = 0;
    static datetime Last_TF_Open = 0;

    if(Period() != lastPeriod) {
        SafePrint("Timeframe se změnil!");
        lastPeriod = Period();
    }

    // Kontrola, zda je čas na přepnutí stavu blikání
    datetime currentTime = TimeCurrent();
    if(currentTime - lastBlinkTime >= BlinkIntervalSec) {
        // Přepnutí stavu blikání
        blinkState    = !blinkState;
        lastBlinkTime = currentTime;

        // Přehrání zvuku, pokud je povoleno
        if(blinkState && alertSound != Alert_None) {
            string soundFile = "";
            switch(alertSound) {
                case Alert_Alert:
                    soundFile = "alert.wav";
                    break;
                case Alert_Alert2:
                    soundFile = "alert2.wav";
                    break;
                case Alert_Tick:
                    soundFile = "tick.wav";
                    break;
                case Alert_OK:
                    soundFile = "ok.wav";
                    break;
                case Alert_Timeout:
                    soundFile = "timeout.wav";
                    break;
                case Alert_News:
                    soundFile = "news.wav";
                    break;
                case Alert_Stops:
                    soundFile = "stops.wav";
                    break;
                default:
                    break;
            }
            if(soundFile != "") {
                PlaySound(soundFile);
            }
        }

        // Aktualizace blikání nejbližší buňky v tabulce
        if(lastBestRow != -1 && lastBestCol != -1) {
            // Získáme aktuální hodnotu a cenu pro kontrolu, zda je v rozsahu pro blikání
            double closePrice = Close[0];
            double cellValue = 0.0;

            if(lastTableType == 0) {  // MA tabulka
                cellValue = MA_Values[lastBestRow][lastBestCol];
            } else if(lastTableType == 1) {  // D tabulka
                cellValue = D_Values[lastBestRow][lastBestCol];
            } else if(lastTableType == 2) {  // ZZ tabulka
                cellValue = ZZ_Table_Values[lastBestRow][lastBestCol];
            }

            double diff = MathAbs(closePrice - cellValue);

            // Blikáme pouze pokud je rozdíl v rozsahu HighlightDistance
            if(diff <= HighlightDistance) {
                string objName;
                if(lastTableType == 0 && showMATable) {  // MA tabulka
                    objName = EA_Tag + "_MACell_" + (string)((lastBestCol + 1) * (maCols + 1) + (lastBestRow + 1));
                    if(ObjectFind(0, objName) >= 0) {
                        color blinkColor   = blinkState ? clrRed : clrWhite;
                        color currentColor = (color)ObjectGetInteger(0, objName, OBJPROP_COLOR);

                        // Aktualizace pouze pokud se barva změnila
                        if(currentColor != blinkColor) {
                            ObjectSetInteger(0, objName, OBJPROP_COLOR, blinkColor);
                        }
                    }
                } else if(lastTableType == 1 && showDTable) {  // D tabulka
                    objName = EA_Tag + "_DCell_" + (string)((lastBestCol + 1) * (dCols + 1) + (lastBestRow + 1));
                    if(ObjectFind(0, objName) >= 0) {
                        color blinkColor   = blinkState ? clrRed : clrWhite;
                        color currentColor = (color)ObjectGetInteger(0, objName, OBJPROP_COLOR);

                        // Aktualizace pouze pokud se barva změnila
                        if(currentColor != blinkColor) {
                            ObjectSetInteger(0, objName, OBJPROP_COLOR, blinkColor);
                        }
                    }
                } else if(lastTableType == 2 && showZZTable) {  // ZZ tabulka
                    objName = EA_Tag + "_ZZCell_" + (string)((lastBestCol + 1) * (zzCols + 1) + (lastBestRow + 1));
                    if(ObjectFind(0, objName) >= 0) {
                        color blinkColor   = blinkState ? clrRed : clrWhite;
                        color currentColor = (color)ObjectGetInteger(0, objName, OBJPROP_COLOR);

                        // Aktualizace pouze pokud se barva změnila
                        if(currentColor != blinkColor) {
                            ObjectSetInteger(0, objName, OBJPROP_COLOR, blinkColor);
                        }
                    }
                }
            }
        }
    }

    // Aktualizace pouze hodnoty vzdálenosti (LD) při každém tiku
    if(showMATable && lastBestRow != -1 && lastBestCol != -1) {
        // Získání aktuální ceny Close
        double closePrice = Close[0];

        // Výpočet vzdálenosti od nejbližší hodnoty
        double bestValue = 0.0;
        if(lastTableType == 0) {  // MA tabulka
            bestValue = MA_Values[lastBestRow][lastBestCol];
        } else if(lastTableType == 1) {  // D tabulka
            bestValue = D_Values[lastBestRow][lastBestCol];
        } else if(lastTableType == 2) {  // ZZ tabulka
            bestValue = ZZ_Table_Values[lastBestRow][lastBestCol];
        }

        // Aktualizace pouze hodnoty LastNearestDistance
        LastNearestDistance = closePrice - bestValue;

        // Aktualizace zobrazení vzdálenosti
        if(!StrategyTester) {
            UpdateDistanceLabel();

            // Aktualizace textu "Searching for..." s aktuální hodnotou LD
            UpdateSearchingText();
        }
    }

    // Sledování maximálních profitů v reálném čase
    TrackRealTimeMaxProfits();

    // Aktualizace historie obchodů
    UpdateOrderHistory();

    // Kontrola uzavření reverse obchodů na konci obchodního dne
    CheckEndOfDayCloseForReverseOrders();

    // 1. Blok pro provedení při každém otevření nové M1 svíčky
    datetime currentM1BarTime = iTime(_Symbol, PERIOD_M1, 0);

    if(Last_M1_Open != currentM1BarTime) {
        Last_M1_Open = currentM1BarTime;

        // Volání funkce ZigZag pro všechny periody při otevření nové svíčky M1
        ZigZagInfo(PERIOD_M5);
        ZigZagInfo(PERIOD_H4);
        ZigZagInfo(PERIOD_D1);

        // Výpočet ZZ vzdálenosti M5
        zzDistanceM5 = NormalizeDouble((zzValues[0][0] - zzValues[0][1]), Digits);

        // Tisk hodnot ZZ Values pouze pokud je zapnutý debug režim
        if(DebugMode) {
            SafePrint("Nová ZZ vzdálenost M5 vypočtena: ", zzDistanceM5);
        }

        // Aktualizace zobrazení ZZ vzdálenosti při každé nové svíčce M1
        if(!StrategyTester) {
            UpdateZZDistanceLabel(zzDistanceM5);
        }

        // Aktualizace vzdáleností od D0 High/Low
        UpdateD0Distances();

        // Aktualizace počítadel obchodů pro aktuální den
        UpdateDailyTradeCounters();

        // Aktualizace MaxProfit na základě High/Low svíčky
        UpdateMaxProfitOnNewCandle();

        // Aktualizace tabulek s každou novou M1 svíčkou
        if(!StrategyTester) {
            UpdateTablesWithNewValues();
        }
    }

    // 2.  Blok pro provedení při každém otevření nové M5 svíčky
    datetime currentM5BarTime = iTime(_Symbol, PERIOD_M5, 0);
    if(Last_M5_Open != currentM5BarTime) {
        Last_M5_Open = currentM5BarTime;

        // 🔧 Zde můžeš přidat vlastní logiku pro M5 (např. potvrzení patternu)
        // Print("🔹 Nová M5 svíčka: ", TimeToString(currentM5BarTime, TIME_DATE | TIME_MINUTES));
    }

    // 3. Blok pro provedení při každém otevření nové svíčky podle zvoleného TF
    if(Last_TF_Open != Time[0]) {
        Last_TF_Open = Time[0];

        // Volejte funkci DrawMA po uzavření svíčky
        // string levelNames[] = {"Denní High", "Denní Low", "PD1 High", "PD1 Low", "50% PD1 H-L", "PW1 High", "PW1 Low", "50% PW1 H-L"};

        // Získání hodnot High a Low z předchozího a aktuálního dne
        double previousDayHigh      = iHigh(NULL, PERIOD_D1, 1);
        double previousDayLow       = iLow(NULL, PERIOD_D1, 1);
        double previousDayHighLow50 = NormalizeDouble(previousDayHigh - ((previousDayHigh - previousDayLow) / 2), Digits);   // Výpočet 50 % mezi High a Low - D1

        double previousWeekHigh      = iHigh(NULL, PERIOD_W1, 1);
        double previousWeekLow       = iLow(NULL, PERIOD_W1, 1);
        double previousWeekHighLow50 = NormalizeDouble(previousWeekHigh - ((previousWeekHigh - previousWeekLow) / 2), Digits);   // Výpočet 50 % mezi High a Low - W1

        if(!StrategyTester) {
            // M5
            DrawMA(Symbol(), PERIOD_M5, 100, 0, MODE_EMA, PRICE_CLOSE, 1, clrDarkViolet, STYLE_DASHDOTDOT, 1, "EMA");
            DrawMA(Symbol(), PERIOD_M5, 200, 0, MODE_EMA, PRICE_CLOSE, 1, clrRed, STYLE_DASHDOTDOT, 1, "EMA");
            DrawMA(Symbol(), PERIOD_M5, 200, 0, MODE_SMA, PRICE_CLOSE, 1, clrOrange, STYLE_DASHDOTDOT, 1, "SMA");

            // H1
            DrawMA(Symbol(), PERIOD_H1, 21, 0, MODE_EMA, PRICE_CLOSE, 1, clrBlue, STYLE_DASH, 2, "EMA");
            DrawMA(Symbol(), PERIOD_H1, 100, 0, MODE_EMA, PRICE_CLOSE, 1, clrDarkViolet, STYLE_DASH, 2, "EMA");
            DrawMA(Symbol(), PERIOD_H1, 200, 0, MODE_EMA, PRICE_CLOSE, 1, clrRed, STYLE_DASH, 2, "EMA");
            DrawMA(Symbol(), PERIOD_H1, 200, 0, MODE_SMA, PRICE_CLOSE, 1, clrOrange, STYLE_DASH, 2, "SMA");

            // H4
            DrawMA(Symbol(), PERIOD_H4, 21, 0, MODE_EMA, PRICE_CLOSE, 1, clrBlue, STYLE_DASH, 2, "EMA");
            DrawMA(Symbol(), PERIOD_H4, 100, 0, MODE_EMA, PRICE_CLOSE, 1, clrDarkViolet, STYLE_DASH, 2, "EMA");
            DrawMA(Symbol(), PERIOD_H4, 200, 0, MODE_EMA, PRICE_CLOSE, 1, clrRed, STYLE_DASH, 2, "EMA");
            DrawMA(Symbol(), PERIOD_H4, 200, 0, MODE_SMA, PRICE_CLOSE, 1, clrOrange, STYLE_DASH, 2, "SMA");

            // Zavolání funkce pro vykreslení čar pro High, Low a 50 % předchozího dne s popiskem

            DrawHorizontalLineWithLabel("PrevDayHigh", previousDayHigh, clrLightSalmon, STYLE_DOT, 1, "PD1 High");
            DrawHorizontalLineWithLabel("PrevDayLow", previousDayLow, clrLightSalmon, STYLE_DOT, 1, "PD1 Low");
            DrawHorizontalLineWithLabel("PrevDayHighLow50", previousDayHighLow50, clrDarkViolet, STYLE_DOT, 1, "PD1 50% High-Low");

            DrawHorizontalLineWithLabel("PreviousWeekHigh", previousWeekHigh, clrLightSalmon, STYLE_DOT, 1, "PW1 High");
            DrawHorizontalLineWithLabel("PreviousWeekLow", previousWeekLow, clrLightSalmon, STYLE_DOT, 1, "PW1 Low");
            DrawHorizontalLineWithLabel("PreviousWeekHighLow50", previousWeekHighLow50, clrDarkViolet, STYLE_DOT, 1, "PW1 50% High-Low");
        }

        // Získání posledních ZigZag hodnot
        int totalBars = Bars(_Symbol, PERIOD_CURRENT);
        zzPeakPrice   = iCustom(NULL, 0, "ZigZag", 0, 1);
        zzHighValue   = iCustom(NULL, 0, "ZigZag", 1, 1);
        zzLowValue    = iCustom(NULL, 0, "ZigZag", 2, 1);

        zzPeakPrice2 = iCustom(NULL, 0, "ZigZag", 0, 2);
        zzHighValue2 = iCustom(NULL, 0, "ZigZag", 1, 2);
        zzLowValue2  = iCustom(NULL, 0, "ZigZag", 2, 2);

        // Získání hodnot Stochastic Oscillator
        StochK = iStochastic(_Symbol, PERIOD_CURRENT, Stoch_K, Stoch_D, Stoch_Slowing, MODE_SMA, 1, MODE_MAIN, 1);
        StochD = iStochastic(_Symbol, PERIOD_CURRENT, Stoch_K, Stoch_D, Stoch_Slowing, MODE_SMA, 1, MODE_SIGNAL, 1);

        // Načtení hodnot z indikátoru Envelope.mq4
        UpperEnvelope = NormalizeDouble(iCustom(NULL, 0, "Envelope", 1, 0), Digits);
        LowerEnvelope = NormalizeDouble(iCustom(NULL, 0, "Envelope", 2, 0), Digits);

        double lastClose = iClose(_Symbol, PERIOD_CURRENT, 1);

        if(zzPeakPrice > 0)
            zzLastPeak = zzPeakPrice;
        if(zzHighValue > 0)
            zzLastH = zzHighValue;
        if(zzLowValue > 0)
            zzLastL = zzLowValue;

        // Volání funkce ZigZag (5 posledních hodnot)
        // ZigZagInfo(5, zzValues, zzIndexes, zzMessages, false, false);

        // Tisk hodnot ZZ Values pouze pokud je zapnutý debug režim
        if(DebugMode) {
            for(int i = 0; i < 10; i++) {
                if(zzMessages[0][i] != "") {
                    SafePrint(zzMessages[0][i]);  // Tisk M5 zpráv
                }
            }
        }

        // ===== ZAČÁTEK NOVÉ OBCHODNÍ LOGIKY =====

        // 1. Kontrola podmínky ZZ vzdálenosti podle vybraného limitu
        double absZZDistance = MathAbs(zzDistanceM5);

        // Určení minimálního limitu pro obchodování podle uživatelského nastavení
        double selectedATRLimit = 0.0;
        string limitName        = "";

        switch(MinZZDistForTrade) {
            case ZZ_Green:   // Zelená - největší vzdálenost
                selectedATRLimit = greenATRLimit;
                limitName        = "Green (ATR/3)";
                break;
            case ZZ_Orange:   // Oranžová - střední vzdálenost
                selectedATRLimit = orangeATRLimit;
                limitName        = "Orange (ATR/4)";
                break;
            case ZZ_Red:   // Červená - nejmenší vzdálenost
                selectedATRLimit = redATRLimit;
                limitName        = "Red (ATR/5)";
                break;
        }

        // Kontrola, zda je ZZ vzdálenost větší nebo rovna vybranému limitu
        bool zzDistanceCondition = (absZZDistance >= selectedATRLimit);

        if(DebugMode && !StrategyTester) {
            Print("ZZ vzdálenost: ", zzDistanceM5, ", Vybraný limit (", limitName, "): ", selectedATRLimit,
                  ", Podmínka splněna: ", zzDistanceCondition ? "ANO" : "NE");
        }

        // Kontrola, zda je nejbližší hodnota v rozsahu pro obchodování
        bool valueInRange = (lastBestRow != -1 && lastBestCol != -1 && MathAbs(LastNearestDistance) <= HighlightDistance);

        // Získání nejbližší hodnoty pro potenciální obchod
        double bestValue = 0.0;
        if(valueInRange) {
            if(lastTableType == 0) {  // MA tabulka
                bestValue = MA_Values[lastBestRow][lastBestCol];
            } else if(lastTableType == 1) {  // D tabulka
                bestValue = D_Values[lastBestRow][lastBestCol];
            } else if(lastTableType == 2) {  // ZZ tabulka
                bestValue = ZZ_Table_Values[lastBestRow][lastBestCol];
            }
        }

        // 2. Kontrola BandCheck a pozice ceny vůči obálkám
        bool bandCondition = true;   // Výchozí hodnota, pokud BandCheck je false

        if(BandCheck) {
            // Pro BUY (záporná ZZ vzdálenost) musí být cena pod spodní obálkou
            if(zzDistanceM5 < 0) {   // Potenciální BUY
                bandCondition = (lastClose < LowerEnvelope);
            }
            // Pro SELL (kladná ZZ vzdálenost) musí být cena nad horní obálkou
            else if(zzDistanceM5 > 0) {   // Potenciální SELL
                bandCondition = (lastClose > UpperEnvelope);
            }
        }

        if(DebugMode) {
            SafePrint("BandCheck: ", BandCheck ? "ANO" : "NE",
                      ", Cena: ", DoubleToString(lastClose, Digits),
                      ", Horní obálka: ", DoubleToString(UpperEnvelope, Digits),
                      ", Spodní obálka: ", DoubleToString(LowerEnvelope, Digits),
                      ", Podmínka splněna: ", bandCondition ? "ANO" : "NE");
        }

        // Kontrola obchodních hodin
        bool isTradeTimeAllowed = IsWithinTradeHours();

        if(DebugMode) {
            SafePrint("Kontrola obchodních hodin: ", isTradeTimeAllowed ? "Obchodování povoleno" : "Obchodování zakázáno",
                      ", Aktuální hodina: ", IntegerToString(TimeHour(TimeCurrent())),
                      ", Povolené hodiny: ", IntegerToString(TradeHoursStart), "-", IntegerToString(TradeHoursEnd));
        }

        // Kontrola vzdálenosti od MA100 na M5
        bool ma100DistanceCondition = true; // Výchozí hodnota, pokud MA100_dist je false

        if(MA100_dist) {
            ma100DistanceCondition = (ema100_dist_abs > greenATRLimit);

            if(DebugMode && !StrategyTester) {
                Print("MA100 M5 vzdálenost: ", ema100_dist_abs, ", Green ATR limit: ", greenATRLimit,
                      ", Podmínka splněna: ", ma100DistanceCondition ? "ANO" : "NE");
            }
        }

        // Kontrola vzdálenosti od MA100 na M1
        bool ma100_1_DistanceCondition = true; // Výchozí hodnota, pokud MA100_1_dist je false

        if(MA100_1_dist) {
            ma100_1_DistanceCondition = (ema100_1_dist_abs > orangeATRLimit);

            if(DebugMode && !StrategyTester) {
                Print("MA100 M1 vzdálenost: ", ema100_1_dist_abs, ", Orange ATR limit: ", orangeATRLimit,
                      ", Podmínka splněna: ", ma100_1_DistanceCondition ? "ANO" : "NE");
            }
        }

        // Kontrola stejného znaménka MA100 vzdáleností (M5 a M1)
        bool ma100SameSignCondition = true; // Výchozí hodnota

        if(MA100_dist && MA100_1_dist) {
            // Obě podmínky jsou zapnuté, kontrolujeme znaménka
            bool m5Positive = (ema100_dist >= 0);
            bool m1Positive = (ema100_1_dist >= 0);
            ma100SameSignCondition = (m5Positive == m1Positive);

            if(DebugMode) {
                SafePrint("MA100 znaménka - M5: ", DoubleToString(ema100_dist, Digits), " (", m5Positive ? "+" : "-", "), M1: ", DoubleToString(ema100_1_dist, Digits), " (", m1Positive ? "+" : "-", "), Stejné: ", ma100SameSignCondition ? "ANO" : "NE");
            }
        }

        // Aktualizace komentáře s podmínkami pro obchod
        if(!StrategyTester) {
            UpdateTradeConditionsComment(zzDistanceCondition, bandCondition, valueInRange, isTradeTimeAllowed);
        }

        // 3. Nová obchodní logika s kontrolou podmínek
        if(zzDistanceCondition && bandCondition && valueInRange && isTradeTimeAllowed && ma100DistanceCondition && ma100_1_DistanceCondition && ma100SameSignCondition) {
            // Kontrola obchodních podmínek pro MG1
            bool allowNormalTrade, allowReverseTrade;
            bool canTradeMG1 = CheckTradingConditions(allowNormalTrade, allowReverseTrade);

            // MG1 a MG2 mohou běžet nezávisle
            if(canTradeMG1 || CheckTradingConditionsMG2()) {
                // Výpočet SL a TP na základě ATR
                double stopLoss = NormalizeDouble(ATR14_D1 / 10, Digits);
                double takeProfit1 = NormalizeDouble(stopLoss * TP1_RRR, Digits);
                double takeProfit2 = NormalizeDouble(stopLoss * TP2_RRR, Digits);

                // Speciální TP pro reverzní obchod (10 * SL)
                double reverseTakeProfit = NormalizeDouble(stopLoss * 10.0, Digits);

                // Získání aktuálních cen
                double currentBid = MarketInfo(_Symbol, MODE_BID);
                double currentAsk = MarketInfo(_Symbol, MODE_ASK);

                // Určení směru obchodu podle ZZ vzdálenosti M5
                bool isBuySignal = (zzDistanceM5 < 0);  // Záporná ZZ vzdálenost = BUY signál
                bool isSellSignal = (zzDistanceM5 > 0); // Kladná ZZ vzdálenost = SELL signál

                // Uložení původních signálů pro MG2 (MG2 vždy používá původní ZZ signály)
                bool originalBuySignal = isBuySignal;
                bool originalSellSignal = isSellSignal;

                if(DebugMode) {
                    SafePrint("Trading Debug - Original signals: isBuySignal=", isBuySignal ? "YES" : "NO",
                              ", isSellSignal=", isSellSignal ? "YES" : "NO", ", zzDistanceM5=", DoubleToString(zzDistanceM5, Digits));
                    SafePrint("Trading Debug - allowNormalTrade=", allowNormalTrade ? "YES" : "NO",
                              ", allowReverseTrade=", allowReverseTrade ? "YES" : "NO");
                    SafePrint("Trading Debug - Reverse_Trade_Pending=", Reverse_Trade_Pending ? "YES" : "NO");
                }

                // Aplikace REVERSE logiky pokud je potřeba
                if(allowReverseTrade && !allowNormalTrade) {
                    // Reverzní obchod je vždy opačný k normálnímu obchodu (podle ZZ signálu)
                    // ZZ signál určuje co by byl normální obchod, reverzní je pak opačný
                    isBuySignal = !isBuySignal;   // Obrátí BUY → SELL
                    isSellSignal = !isSellSignal; // Obrátí SELL → BUY

                    if(DebugMode) {
                        string triggerReason = "";
                        if(Consecutive_BUY_Loss >= Reverse_Loss) {
                            triggerReason = StringFormat("po %d BUY ztrátách", Consecutive_BUY_Loss);
                        } else if(Consecutive_SELL_Loss >= Reverse_Loss) {
                            triggerReason = StringFormat("po %d SELL ztrátách", Consecutive_SELL_Loss);
                        }

                        SafePrint("Trading Debug - REVERZNÍ OBCHOD ", triggerReason);
                        SafePrint("Trading Debug - ZZ signál obrácen: isBuySignal=", isBuySignal ? "YES" : "NO",
                                  ", isSellSignal=", isSellSignal ? "YES" : "NO");
                    }
                }

                // Provedení obchodu s MagicNo1 (pouze pokud jsou splněny MG1 podmínky)
                if(canTradeMG1) {
                    double selectedTP = (allowReverseTrade && !allowNormalTrade) ? reverseTakeProfit : takeProfit1;
                    bool isReverseTradeMode = (allowReverseTrade && !allowNormalTrade);
                    ExecuteTrade(isBuySignal, isSellSignal, bestValue, currentBid, currentAsk, stopLoss, selectedTP, MagicNo1, "MG1", isReverseTradeMode);
                }

                // Kontrola podmínek pro MagicNo2 - NEZÁVISLÉ NA MG1!
                if(CheckTradingConditionsMG2()) {
                    bool executeMagicNo2 = false;
                    // Použití globální proměnné mg2DistanceLimit

                    if(DebugMode) {
                        SafePrint("MG2 Debug - MG2 je NEZÁVISLÉ na MG1");
                        SafePrint("MG2 Debug - originalBuySignal: ", originalBuySignal ? "YES" : "NO", ", originalSellSignal: ", originalSellSignal ? "YES" : "NO");
                        SafePrint("MG2 Debug - Dist_D0_High: ", DoubleToString(Dist_D0_High, Digits), ", Dist_D0_Low: ", DoubleToString(Dist_D0_Low, Digits), ", mg2DistanceLimit: ", DoubleToString(mg2DistanceLimit, Digits));
                    }

                    // MG2 používá PŮVODNÍ ZZ signály (ne obrácené pro reverzní obchody)
                    if(originalBuySignal && Dist_D0_High >= mg2DistanceLimit) {
                        executeMagicNo2 = true;
                        if(DebugMode) SafePrint("MG2 Debug - BUY podmínka splněna: ", DoubleToString(Dist_D0_High, Digits), " >= ", DoubleToString(mg2DistanceLimit, Digits));
                    } else if(originalSellSignal && Dist_D0_Low >= mg2DistanceLimit) {
                        executeMagicNo2 = true;
                        if(DebugMode) SafePrint("MG2 Debug - SELL podmínka splněna: ", DoubleToString(Dist_D0_Low, Digits), " >= ", DoubleToString(mg2DistanceLimit, Digits));
                    } else {
                        if(DebugMode) {
                            if(originalBuySignal) {
                                SafePrint("MG2 Debug - BUY podmínka NESPLNĚNA: ", DoubleToString(Dist_D0_High, Digits), " < ", DoubleToString(mg2DistanceLimit, Digits));
                            } else if(originalSellSignal) {
                                SafePrint("MG2 Debug - SELL podmínka NESPLNĚNA: ", DoubleToString(Dist_D0_Low, Digits), " < ", DoubleToString(mg2DistanceLimit, Digits));
                            } else {
                                SafePrint("MG2 Debug - Žádný signál (originalBuySignal=false, originalSellSignal=false)");
                            }
                        }
                    }

                    if(executeMagicNo2) {
                        if(DebugMode) SafePrint("MG2 Debug - Provádím MG2 obchod s původními signály");
                        // MG2 používá původní signály, ne upravené pro MG1
                        ExecuteTrade(originalBuySignal, originalSellSignal, bestValue, currentBid, currentAsk, stopLoss, takeProfit2, MagicNo2, "MG2", false);
                    } else {
                        if(DebugMode) SafePrint("MG2 Debug - MG2 obchod se NEPROVEDE");
                    }
                } else {
                    if(DebugMode) SafePrint("MG2 Debug - allowMagicNo2 = FALSE, MG2 blokováno");
                }
            }
        }
        // 4. Pokud nejsou splněny podmínky nebo jsme mimo obchodní hodiny, zrušíme čekající pokyny
        else {
            // Kontrola, zda máme aktivní čekající pokyny (MG1 nebo MG2)
            if(IsPendingOrderValid(pendingBuyTicket) || IsPendingOrderValid(pendingSellTicket) ||
               IsPendingOrderValid(pendingBuyTicket_MG2) || IsPendingOrderValid(pendingSellTicket_MG2)) {
                string reason = "";
                if(!zzDistanceCondition)
                    reason = "ZZ vzdálenost není dostatečná";
                else if(!bandCondition)
                    reason = "Podmínka BandCheck není splněna";
                else if(!valueInRange)
                    reason = "Hodnota není v obchodním rozsahu";
                else if(!isTradeTimeAllowed)
                    reason = "Mimo obchodní hodiny";
                else if(!ma100DistanceCondition)
                    reason = "MA100 M5 vzdálenost není dostatečná";
                else if(!ma100_1_DistanceCondition)
                    reason = "MA100 M1 vzdálenost není dostatečná";
                else if(!ma100SameSignCondition)
                    reason = "MA100 M5 a M1 nemají stejné znaménko";

                if(DebugMode) {
                    SafePrint("Ruším čekající pokyny - podmínky nejsou splněny. Důvod: ", reason);
                }
                CancelPendingOrders();
            }
        }

        // ===== KONEC NOVÉ OBCHODNÍ LOGIKY =====

        // Aktualizace času poslední aktualizace
        lastUpdate = Time[0];
    }
}

/**
 * Aktualizuje hodnoty v tabulkách a překreslí je.
 * Tato funkce je volána při každé nové M1 svíčce.
 * Optimalizovaná verze, která minimalizuje překreslování.
 *
 * @param forceRedraw Pokud je true, vždy překreslí tabulky bez ohledu na podmínky
 */
void UpdateTablesWithNewValues(bool forceRedraw = false) {
    // Uložení starých hodnot pro porovnání
    static datetime lastRedrawTime = 0;
    static int      lastCornerX    = 0;
    static int      lastCornerY    = 0;

    // Aktualizace hodnot
    UpdateMAValues();
    UpdateDValues();
    // ZZ hodnoty se aktualizují v ZigZagInfo funkci

    // Volání FindClosestToCloseAndColorMap() pouze pokud je potřeba překreslit tabulky
    // nebo pokud je vyžadováno vynucené překreslení
    bool needColorUpdate = forceRedraw || TimeCurrent() - lastRedrawTime > 5 || lastCornerX != cornerX || lastCornerY != cornerY;

    if(needColorUpdate) {
        FindClosestToCloseAndColorMap();
    }

    // Výpočet vertikálního odsazení pro tlačítka a tabulky
    int buttonY  = cornerY + 10;
    int maTableY = buttonY + 20;
    int dButtonY = maTableY + (currentCellHeight * 4) + 10;
    int dTableY  = dButtonY + 20;
    int zzButtonY = dTableY + (currentCellHeight * 3) + 30;  // ZZ tlačítko pod D tabulkou s větší mezerou
    int zzTableY = zzButtonY + 20;  // ZZ tabulka pod ZZ tlačítkem

    // Kontrola, zda je potřeba překreslit tabulky
    // Překreslíme tabulky pouze pokud:
    // 1. Je vyžadováno vynucené překreslení (forceRedraw)
    // 2. Uplynul určitý čas od posledního překreslení (např. 5 sekund)
    // 3. Změnila se pozice tabulek (cornerX nebo cornerY)
    bool needRedraw = forceRedraw || TimeCurrent() - lastRedrawTime > 5 || lastCornerX != cornerX || lastCornerY != cornerY;

    // Aktualizace hodnot pro příští kontrolu
    if(needRedraw) {
        lastRedrawTime = TimeCurrent();
        lastCornerX    = cornerX;
        lastCornerY    = cornerY;
    }

    // Překreslení tabulek s novými hodnotami pouze pokud je to potřeba
    if(needRedraw) {
        if(showMATable) {
            DeleteTable("MACell");
            DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, maRows, maCols, cornerX, maTableY, clrWhite);
        }

        if(showDTable) {
            DeleteTable("DCell");
            DrawTable("DCell", D_labels_X, D_labels_Y, D_Values, D_Colors, D_FontSizes, dRows, dCols, cornerX, dTableY, clrWhite);
        }

        // ZZ tabulka - pouze pokud je zapnutá
        if(showZZTable) {
            DeleteTable("ZZCell");
            DrawTable("ZZCell", ZZ_labels_X, ZZ_labels_Y, ZZ_Table_Values, ZZ_Colors, ZZ_FontSizes, zzRows, zzCols, cornerX, zzTableY, clrWhite);
        }
    } else {
        // Pokud nepřekreslujeme celé tabulky, aktualizujeme pouze hodnoty a barvy
        // Použijeme existující funkci pro aktualizaci barev buněk
        UpdateCellColors();
    }
}

/**
 * Aktualizuje pouze hodnotu vzdálenosti (LD) bez překreslování celé tabulky.
 * Tato funkce je mnohem efektivnější než překreslování celé tabulky.
 */
void UpdateDistanceLabel() {
    if(!showMATable || LastNearestDistance == 0.0)
        return;

    // Výpočet vertikálního odsazení pro tabulku
    int buttonY  = cornerY + 10;
    int maTableY = buttonY + 20;

    string distLabelName = EA_Tag + "_MACell_NearestDistanceTopRight";

    // Kontrola, zda je vzdálenost v rozsahu
    bool isInRange = (MathAbs(LastNearestDistance) <= HighlightDistance);

    if(isInRange) {
        // Formátování textu se znaménkem + nebo -
        string sign     = LastNearestDistance > 0 ? "+" : "";
        string distText = "LD: " + sign + DoubleToString(LastNearestDistance, 2) + " b";

        // Pokud objekt již existuje, pouze aktualizujeme jeho text a barvu
        if(ObjectFind(0, distLabelName) >= 0) {
            ObjectSetString(0, distLabelName, OBJPROP_TEXT, distText);
            ObjectSetInteger(0, distLabelName, OBJPROP_COLOR, clrLimeGreen);
        } else {
            // Pokud objekt neexistuje, vytvoříme ho
            ObjectCreate(0, distLabelName, OBJ_LABEL, 0, 0, 0);
            ObjectSetInteger(0, distLabelName, OBJPROP_CORNER, 0);
            ObjectSetInteger(0, distLabelName, OBJPROP_XDISTANCE, cornerX + currentCellWidth * maCols);
            ObjectSetInteger(0, distLabelName, OBJPROP_YDISTANCE, maTableY - currentCellHeight - 4);
            ObjectSetInteger(0, distLabelName, OBJPROP_FONTSIZE, 12);
            ObjectSetInteger(0, distLabelName, OBJPROP_COLOR, clrLimeGreen);
            ObjectSetString(0, distLabelName, OBJPROP_TEXT, distText);
        }
    } else {
        // Pokud vzdálenost není v rozsahu, skryjeme objekt (nastavíme prázdný text)
        if(ObjectFind(0, distLabelName) >= 0) {
            ObjectSetString(0, distLabelName, OBJPROP_TEXT, "");
        }
    }
}

/**
 * Aktualizuje buňky v tabulce podle aktuálních hodnot.
 * @param prefix Prefix pro názvy objektů tabulky (např. "MACell" nebo "DCell")
 * @param values Dvourozměrné pole hodnot tabulky
 * @param colors Dvourozměrné pole barev buněk
 * @param fontSizes Dvourozměrné pole velikostí písma
 * @param rows Počet řádků tabulky
 * @param cols Počet sloupců tabulky
 * @param isCurrentTable True, pokud je tato tabulka aktuálně nejbližší k ceně
 * @param bestRow Index řádku nejbližší buňky (pokud isCurrentTable je true)
 * @param bestCol Index sloupce nejbližší buňky (pokud isCurrentTable je true)
 */
void UpdateTableCells(
    string  prefix,
    double &values[][],
    color  &colors[][],
    int    &fontSizes[][],
    int     rows,
    int     cols,
    bool    isCurrentTable,
    int     bestRow,
    int     bestCol) {
    for(int row = 1; row <= rows; row++) {
        for(int col = 1; col <= cols; col++) {
            string objName = EA_Tag + "_" + prefix + "_" + (string)((row * (cols + 1)) + col);
            if(ObjectFind(0, objName) >= 0) {
                // Aktualizace textu buňky pouze pokud se hodnota změnila
                string currentText = ObjectGetString(0, objName, OBJPROP_TEXT);
                string newText     = DoubleToString(values[col - 1][row - 1], 2);

                // Aktualizace textu pouze pokud se změnil
                if(currentText != newText) {
                    ObjectSetString(0, objName, OBJPROP_TEXT, newText);
                }

                // Aktualizace barvy buňky pouze pokud je to nejbližší hodnota nebo v rozsahu
                color newColor;
                int   newFontSize;

                if((col - 1) == bestRow && (row - 1) == bestCol && isCurrentTable) {
                    // Nejbližší hodnota - použijeme aktuální blikající barvu
                    // Kontrola, zda je v rozsahu pro blikání
                    double closePrice = Close[0];
                    double cellValue  = values[col - 1][row - 1];
                    double diff       = MathAbs(closePrice - cellValue);

                    if(diff <= HighlightDistance) {
                        newColor    = blinkState ? clrRed : clrWhite;
                        newFontSize = HighlightFontSize;
                    } else {
                        newColor    = CloseRangeColor;
                        newFontSize = DefaultFontSize;
                    }
                } else if(colors[col - 1][row - 1] == CloseRangeColor) {
                    // Hodnota v rozsahu - použijeme zelenou barvu
                    newColor    = CloseRangeColor;
                    newFontSize = DefaultFontSize;
                } else {
                    // Ostatní hodnoty - použijeme výchozí barvu
                    newColor    = DefaultColor;
                    newFontSize = DefaultFontSize;
                }

                // Získání aktuální barvy a velikosti písma
                color currentColor    = (color)ObjectGetInteger(0, objName, OBJPROP_COLOR);
                int   currentFontSize = (int)ObjectGetInteger(0, objName, OBJPROP_FONTSIZE);

                // Aktualizace pouze pokud se barva nebo velikost písma změnila
                if(currentColor != newColor) {
                    ObjectSetInteger(0, objName, OBJPROP_COLOR, newColor);
                }

                if(currentFontSize != newFontSize) {
                    ObjectSetInteger(0, objName, OBJPROP_FONTSIZE, newFontSize);
                }
            }
        }
    }
}

/**
 * Aktualizuje barvy buněk podle aktuálních hodnot bez překreslování celé tabulky.
 * Tato funkce je mnohem efektivnější než překreslování celé tabulky.
 * Optimalizovaná verze, která aktualizuje pouze hodnoty v buňkách, ne jejich barvy.
 */
void UpdateCellColors() {
    if(!showMATable && !showDTable)
        return;

    // Použijeme již vypočtené hodnoty z globálních proměnných
    // Není potřeba volat FindClosestToCloseAndColorMap(), protože hodnoty již máme

    // Optimalizace: Aktualizujeme pouze tabulku, která je aktuálně zobrazena
    // a pouze pokud má smysl aktualizovat (existují platné indexy nejbližší buňky)
    bool hasValidCell = (lastBestRow != -1 && lastBestCol != -1);

    // Aktualizace hodnot v buňkách MA tabulky
    if(showMATable) {
        UpdateTableCells("MACell", MA_Values, MA_Colors, MA_FontSizes, maRows, maCols, (lastTableType == 0), lastBestRow, lastBestCol);
    }

    // Aktualizace hodnot v buňkách D tabulky
    if(showDTable) {
        UpdateTableCells("DCell", D_Values, D_Colors, D_FontSizes, dRows, dCols, (lastTableType == 1), lastBestRow, lastBestCol);
    }

    // Aktualizace hodnot v buňkách ZZ tabulky
    if(showZZTable) {
        UpdateTableCells("ZZCell", ZZ_Table_Values, ZZ_Colors, ZZ_FontSizes, zzRows, zzCols, (lastTableType == 2), lastBestRow, lastBestCol);
    }

    // Aktualizace hodnoty vzdálenosti pouze pokud máme platnou nejbližší buňku
    if(hasValidCell) {
        UpdateDistanceLabel();
    }
}

// Funkce OnTimer() byla odstraněna a její funkcionalita byla přesunuta do OnTick()

/**
 * Aktualizuje popisky časových rámců v MA tabulce podle aktuálně vybraného indexu časového rámce.
 * Kopíruje hodnoty z aktuálního řádku TF_labels do MA_labels_Y.
 * Používá již inicializovanou globální proměnnou maRows.
 */
void UpdateTFLabels() {
    // Dynamicky procházíme všechny řádky
    for(int i = 0; i < maRows; i++) {
        // Kopírování hodnot z aktuálního řádku TF_labels do MA_labels_Y
        MA_labels_Y[i] = TF_labels[currentTFIndex][i];
    }
}

/**
 * Aktualizuje hodnoty klouzavých průměrů v MA tabulce.
 * Vypočítá hodnoty klouzavých průměrů pro všechny časové rámce
 * definované v aktuálním řádku TF_labels.
 * Používá dynamické indexy podle velikosti polí a hodnoty z MA_labels_X.
 */
void UpdateMAValues() {
    // Dynamicky procházíme všechny sloupce a řádky
    for(int i = 0; i < maCols; i++) {
        for(int j = 0; j < maRows; j++) {
            // Získání časového rámce z aktuálního řádku TF_labels
            int tf = TFStrToPeriod(TF_labels[currentTFIndex][j]);

            // Získání periody a typu klouzavého průměru z MA_labels_X
            int period = MA_labels_X[i].period;
            int maType = MA_labels_X[i].maType;

            // Výpočet hodnoty klouzavého průměru
            MA_Values[i][j] = iMA(NULL, tf, period, 0, maType, PRICE_CLOSE, 1);
        }
    }
}

/**
 * Aktualizuje hodnoty v D tabulce (High, Low, HL50).
 * Vypočítá hodnoty pro D0, D1 a W1 podle názvů řádků v D_labels_Y.
 * Používá dynamické indexy podle velikosti polí a efektivnější výpočty.
 *
 * Názvy řádků jsou interpretovány následovně:
 * - D0, D1, D2, atd. - PERIOD_D1 s příslušným shiftem (0, 1, 2, ...)
 * - W0, W1, W2, atd. - PERIOD_W1 s příslušným shiftem (0, 1, 2, ...)
 */
void UpdateDValues() {
    // Dynamicky procházíme všechny řádky
    for(int j = 0; j < dRows; j++) {
        // Získání časového rámce a shiftu z názvu řádku
        int timeframe = PERIOD_D1;   // Výchozí hodnota
        int shift     = 0;           // Výchozí hodnota

        string rowLabel = D_labels_Y[j];

        // Analýza názvu řádku pro určení časového rámce a shiftu
        if(StringLen(rowLabel) >= 2) {
            // První znak určuje časový rámec
            if(StringSubstr(rowLabel, 0, 1) == "D") {
                timeframe = PERIOD_D1;
            } else if(StringSubstr(rowLabel, 0, 1) == "W") {
                timeframe = PERIOD_W1;
            }

            // Druhý a další znaky určují shift
            string shiftStr = StringSubstr(rowLabel, 1);
            shift           = (int)StringToInteger(shiftStr);
        }

        // Pro každý řádek získáme High a Low
        double highValue = iHigh(NULL, timeframe, shift);
        double lowValue  = iLow(NULL, timeframe, shift);

        // Uložení hodnot do pole
        D_Values[0][j] = highValue;   // High
        D_Values[1][j] = lowValue;    // Low

        // Vypočítáme HL50 (střed mezi High a Low) - optimalizovaný výpočet
        D_Values[2][j] = highValue - ((highValue - lowValue) / 2);
    }
}

/**
 * Převádí textový řetězec časového rámce na odpovídající číselnou konstantu.
 * @param tf Textový řetězec časového rámce (např. "M1", "H1", "D1")
 * @return Číselná konstanta časového rámce (např. PERIOD_M1, PERIOD_H1, PERIOD_D1)
 */
int TFStrToPeriod(string tf) {
    // Použití if-else podmínek pro kompatibilitu
    if(tf == "M1")
        return PERIOD_M1;
    if(tf == "M5")
        return PERIOD_M5;
    if(tf == "M15")
        return PERIOD_M15;
    if(tf == "M30")
        return PERIOD_M30;
    if(tf == "H1")
        return PERIOD_H1;
    if(tf == "H4")
        return PERIOD_H4;
    if(tf == "D1")
        return PERIOD_D1;
    if(tf == "W1")
        return PERIOD_W1;

    // Pokud není nalezena shoda, vrátíme aktuální časový rámec
    SafePrint("Neznámý časový rámec: ", tf, ", použit aktuální časový rámec");
    return Period();
}

// Nastavení výchozích souřadnic podle zvoleného rohu
/**
 * Nastaví výchozí souřadnice pro umístění tabulek a ovládacích prvků podle zvoleného rohu grafu.
 * Zajišťuje, aby tabulky byly vždy viditelné a nepřesahovaly okraje grafu.
 */
void SetInitialCoordinates() {
    // Zjištění rozměrů grafu
    chartWidth  = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
    chartHeight = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);

    // Výpočet celkové šířky tabulky (pro MA tabulku, která je větší)
    int totalTableWidth  = currentCellWidth * 5 + 10;   // 5 sloupců pro MA tabulku (4 + 1 pro popisky) + malá rezerva
    int totalTableHeight = 110 + (currentCellHeight * 10);  // skutečná celková výška: mezery + tlačítka + všechny tabulky

    // Použití uživatelského nastavení okraje
    int offsetX = TableMargin;
    int offsetY = TableMargin;

    // Minimální vzdálenost od horního okraje grafu (aby tabulka nebyla příliš vysoko)
    int minTopMargin = (int)(chartHeight * 0.05);   // 5% výšky grafu
    if(minTopMargin < 30)
        minTopMargin = 30;   // Minimálně 30 pixelů od horního okraje

    // Minimální vzdálenost od spodního okraje grafu (aby tabulka nebyla příliš nízko)
    int minBottomMargin = (int)(chartHeight * 0.05);   // 5% výšky grafu
    if(minBottomMargin < 30)
        minBottomMargin = 30;   // Minimálně 30 pixelů od spodního okraje

    // Nastavení pozice GripPointu podle zvoleného rohu
    switch(Corner) {
        case LeftUpper:
            cornerX = offsetX;
            cornerY = minTopMargin;   // Použití minimální vzdálenosti od horního okraje
            break;
        case RightUpper:
            // Zajistíme, aby tabulka nebyla mimo viditelnou oblast
            cornerX = chartWidth - totalTableWidth - 5;   // Posunuto téměř k pravému okraji (pouze 5 pixelů od okraje)
            cornerY = minTopMargin;                       // Použití minimální vzdálenosti od horního okraje
            break;
        case LeftLower:
            cornerX = offsetX;
            cornerY = chartHeight - totalTableHeight - minBottomMargin;
            break;
        case RightLower:
            // Zajistíme, aby tabulka nebyla mimo viditelnou oblast
            cornerX = chartWidth - totalTableWidth - 5;   // Posunuto téměř k pravému okraji (pouze 5 pixelů od okraje)
            cornerY = chartHeight - totalTableHeight - minBottomMargin;
            break;
    }

    // Kontrola, aby tabulka nebyla mimo viditelnou oblast
    if(cornerX < 0)
        cornerX = 0;
    if(cornerY < 0)
        cornerY = 0;

    // Zajistíme, aby tabulka byla vždy alespoň částečně viditelná
    int minVisibleWidth  = MathMin(100, totalTableWidth / 2);
    int minVisibleHeight = MathMin(50, totalTableHeight / 2);

    if(cornerX > chartWidth - minVisibleWidth)
        cornerX = chartWidth - minVisibleWidth;
    if(cornerY > chartHeight - minVisibleHeight)
        cornerY = chartHeight - minVisibleHeight;
}

// Vytvoření GripPointu pro přesouvání
/**
 * Vytvoří GripPoint - malé tlačítko, které slouží k přesouvání tabulek a ovládacích prvků.
 * GripPoint je umístěn před tlačítkem MA Tabulka a má stejnou Y-pozici.
 */
void CreateGripPoint() {
    if(ObjectFind(0, gripPointName) >= 0)
        ObjectDelete(0, gripPointName);

    // Výpočet pozice GripPointu - před tlačítkem MA Tabulka
    int buttonY = cornerY + 20;

    ObjectCreate(0, gripPointName, OBJ_BUTTON, 0, 0, 0);
    ObjectSetInteger(0, gripPointName, OBJPROP_CORNER, 0);
    ObjectSetInteger(0, gripPointName, OBJPROP_XDISTANCE, cornerX - 15);   // 15 pixelů před tlačítkem MA Tabulka
    ObjectSetInteger(0, gripPointName, OBJPROP_YDISTANCE, buttonY - 10);   // Stejná Y-pozice jako tlačítko MA Tabulka
    ObjectSetInteger(0, gripPointName, OBJPROP_XSIZE, 10);
    ObjectSetInteger(0, gripPointName, OBJPROP_YSIZE, 10);
    ObjectSetInteger(0, gripPointName, OBJPROP_BGCOLOR, GripColor);
    ObjectSetInteger(0, gripPointName, OBJPROP_BORDER_COLOR, clrBlack);
    ObjectSetInteger(0, gripPointName, OBJPROP_HIDDEN, false);
    ObjectSetInteger(0, gripPointName, OBJPROP_SELECTABLE, true);
    ObjectSetString(0, gripPointName, OBJPROP_TEXT, "");
}

/**
 * Automaticky přizpůsobí velikost buněk a pozici tabulek podle rozlišení grafu.
 * Menší grafy mají menší buňky, větší grafy mají větší buňky.
 * Také zajišťuje, aby tabulky byly vždy viditelné a nepřesahovaly okraje grafu.
 */
void AdjustCellSizeForResolution() {
    if(AutoAdjustSize) {
        // Zjištění rozměrů grafu
        int width  = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
        int height = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);

        // Přizpůsobení velikosti buněk podle šířky grafu
        if(width < 800) {
            // Pro malé rozlišení
            currentCellWidth  = 50;   // Zmenšeno z 60
            currentCellHeight = 14;
        } else if(width < 1200) {
            // Pro střední rozlišení
            currentCellWidth  = 60;   // Zmenšeno z 70
            currentCellHeight = 16;
        } else {
            // Pro vysoké rozlišení
            currentCellWidth  = 70;   // Zmenšeno z 80
            currentCellHeight = 18;
        }

        // Zajištění, aby tabulka nebyla příliš nízko na grafu
        // Pokud je tabulka v horní části grafu, posuneme ji níže od horního okraje
        if(Corner == LeftUpper || Corner == RightUpper) {
            // Pro horní rohy - posuneme tabulku níže od horního okraje
            int minTopMargin = (int)(height * 0.05);   // 5% výšky grafu jako minimální horní okraj
            if(cornerY < minTopMargin) {
                cornerY = minTopMargin;
            }
        } else {
            // Pro dolní rohy - zajistíme, aby tabulka nebyla příliš blízko spodního okraje
            int totalTableHeight = 110 + (currentCellHeight * 10);  // skutečná celková výška: mezery + tlačítka + všechny tabulky
            int minBottomMargin  = (int)(height * 0.05);     // 5% výšky grafu jako minimální spodní okraj

            if(cornerY > height - totalTableHeight - minBottomMargin) {
                cornerY = height - totalTableHeight - minBottomMargin;
            }
        }
    }
}

/**
 * Vytvoří přepínací tlačítko s daným názvem, pozicí a textem.
 * @param name Název objektu tlačítka
 * @param x X-souřadnice tlačítka
 * @param y Y-souřadnice tlačítka
 * @param label Text zobrazený na tlačítku
 * @param bg Barva pozadí tlačítka
 */
void CreateToggleButton(string name, int x, int y, string label, color bg) {
    string objName = EA_Tag + "_" + name;
    ObjectCreate(0, objName, OBJ_BUTTON, 0, 0, 0);
    ObjectSetInteger(0, objName, OBJPROP_CORNER, 0);
    ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, objName, OBJPROP_FONTSIZE, 9);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, objName, OBJPROP_BORDER_COLOR, clrGray);
    ObjectSetInteger(0, objName, OBJPROP_BGCOLOR, bg);
    ObjectSetInteger(0, objName, OBJPROP_XSIZE, 70);   // Zmenšeno z 80
    ObjectSetInteger(0, objName, OBJPROP_YSIZE, 16);
    ObjectSetInteger(0, objName, OBJPROP_HIDDEN, false);
    ObjectSetInteger(0, objName, OBJPROP_STATE, true);
    ObjectSetString(0, objName, OBJPROP_TEXT, label);
}

/**
 * Aktualizuje barvu tlačítka podle jeho stavu (zapnuto/vypnuto).
 * @param buttonName Název tlačítka, jehož barva se má aktualizovat
 * @param isOn True, pokud je tlačítko zapnuto (zelená barva), False pro vypnuto (červená barva)
 */
void UpdateButtonColor(string buttonName, bool isOn) {
    string objName     = EA_Tag + "_" + buttonName;
    color  buttonColor = isOn ? clrForestGreen : clrFireBrick;
    ObjectSetInteger(0, objName, OBJPROP_BGCOLOR, buttonColor);
}

/**
 * Odstraní všechny objekty tabulky se zadaným prefixem.
 * @param prefix Prefix názvů objektů, které mají být odstraněny
 */
void DeleteTable(string prefix) {
    string fullPrefix = EA_Tag + "_" + prefix;
    for(int i = ObjectsTotal() - 1; i >= 0; i--) {
        string name = ObjectName(0, i);
        if(StringFind(name, fullPrefix) == 0)
            ObjectDelete(0, name);
    }
}

/**
 * Vykreslí tabulku s hodnotami a popisky.
 * @param prefix Prefix pro názvy objektů tabulky
 * @param labels_X Pole popisků sloupců
 * @param labels_Y Pole popisků řádků
 * @param data Dvourozměrné pole hodnot
 * @param colorMap Dvourozměrné pole barev pro jednotlivé buňky
 * @param fontSizes Dvourozměrné pole velikostí písma pro jednotlivé buňky
 * @param rows Počet řádků tabulky
 * @param cols Počet sloupců tabulky
 * @param xOffset X-souřadnice levého horního rohu tabulky
 * @param yOffset Y-souřadnice levého horního rohu tabulky
 * @param colDefault Výchozí barva textu
 */
void DrawTable(string     prefix,
               LabelInfo &labels_X[],
               string    &labels_Y[],
               double    &data[][],
               color     &colorMap[][],
               int       &fontSizes[][],
               int        rows,
               int        cols,
               int        xOffset,
               int        yOffset,
               color      colDefault) {
    int index = 0;

    // --- Zobrazení hodnoty DIST v pravém horním rohu ---
    if(prefix == "MACell") {
        // Použijeme existující funkci pro aktualizaci hodnoty vzdálenosti
        // Tím zajistíme konzistentní zobrazení při prvním vykreslení i při aktualizacích
        UpdateDistanceLabel();
    }

    // --- Vykreslení celé tabulky ---
    for(int row = 0; row <= rows; row++) {
        for(int col_i = 0; col_i <= cols; col_i++) {
            string text;
            color  useColor = colDefault;
            int    fontSize = 10;

            if(row == 0 && col_i == 0)
                text = "";
            else if(row == 0) {
                text     = labels_X[col_i - 1].text;
                useColor = labels_X[col_i - 1].clr;
            } else if(col_i == 0)
                text = labels_Y[row - 1];
            else {
                text     = DoubleToString(data[col_i - 1][row - 1], 2);
                useColor = colorMap[col_i - 1][row - 1];
                fontSize = fontSizes[col_i - 1][row - 1];
            }

            string objName = EA_Tag + "_" + prefix + "_" + (string)index;
            ObjectCreate(0, objName, OBJ_LABEL, 0, 0, 0);
            ObjectSetInteger(0, objName, OBJPROP_CORNER, 0);
            ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, xOffset + currentCellWidth * col_i);
            ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, yOffset + currentCellHeight * row);
            ObjectSetInteger(0, objName, OBJPROP_FONTSIZE, fontSize);
            ObjectSetInteger(0, objName, OBJPROP_COLOR, useColor);
            ObjectSetString(0, objName, OBJPROP_TEXT, text);
            index++;
        }
    }
}

/**
 * Aktualizuje pozice a obsah všech prvků podle nové pozice GripPointu.
 * Přepočítá pozice tlačítek a tabulek, aktualizuje hodnoty a překreslí tabulky.
 * Optimalizovaná verze, která vynucuje překreslení tabulek při přesunu.
 */
void UpdateAllElements() {
    // Výpočet vertikálního odsazení pro tlačítka a tabulky
    int buttonY  = cornerY + 10;                              // Tlačítka jsou výše (zmenšeno z 20)
    int maTableY = buttonY + 20;                              // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)
    int dButtonY = maTableY + (currentCellHeight * 4) + 10;   // D tlačítko je pod MA tabulkou s mezerou
    int dTableY  = dButtonY + 20;                             // D tabulka začíná 20 pixelů pod D tlačítkem (zvětšeno z 15)
    int zzButtonY = dTableY + (currentCellHeight * 3) + 30;   // ZZ tlačítko je pod D tabulkou s větší mezerou
    int zzTableY = zzButtonY + 20;                            // ZZ tabulka začíná 20 pixelů pod ZZ tlačítkem

    // Aktualizace pozice GripPointu
    ObjectSetInteger(0, gripPointName, OBJPROP_XDISTANCE, cornerX - 15);   // 15 pixelů před tlačítkem MA Tabulka
    ObjectSetInteger(0, gripPointName, OBJPROP_YDISTANCE, buttonY);        // Stejná Y-pozice jako tlačítko MA Tabulka

    // Aktualizace pozic tlačítek
    ObjectSetInteger(0, EA_Tag + "_ToggleMA", OBJPROP_XDISTANCE, cornerX + 0);
    ObjectSetInteger(0, EA_Tag + "_ToggleMA", OBJPROP_YDISTANCE, buttonY);

    ObjectSetInteger(0, EA_Tag + "_TFNext", OBJPROP_XDISTANCE, cornerX + 80);   // Posunuto blíže (z 100)
    ObjectSetInteger(0, EA_Tag + "_TFNext", OBJPROP_YDISTANCE, buttonY);

    ObjectSetInteger(0, EA_Tag + "_TFPrev", OBJPROP_XDISTANCE, cornerX + 155);   // Posunuto blíže (z 180)
    ObjectSetInteger(0, EA_Tag + "_TFPrev", OBJPROP_YDISTANCE, buttonY);

    ObjectSetInteger(0, EA_Tag + "_ToggleD", OBJPROP_XDISTANCE, cornerX);
    ObjectSetInteger(0, EA_Tag + "_ToggleD", OBJPROP_YDISTANCE, dButtonY);

    ObjectSetInteger(0, EA_Tag + "_ToggleZZ", OBJPROP_XDISTANCE, cornerX);
    ObjectSetInteger(0, EA_Tag + "_ToggleZZ", OBJPROP_YDISTANCE, zzButtonY);

    // Aktualizace hodnot
    UpdateMAValues();
    UpdateDValues();
    // ZZ hodnoty se aktualizují v ZigZagInfo funkci
    FindClosestToCloseAndColorMap();

    // Při přesunu vždy překreslíme celé tabulky
    if(showMATable) {
        DeleteTable("MACell");
        DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, maRows, maCols, cornerX, maTableY, clrWhite);
    }

    if(showDTable) {
        DeleteTable("DCell");
        DrawTable("DCell", D_labels_X, D_labels_Y, D_Values, D_Colors, D_FontSizes, dRows, dCols, cornerX, dTableY, clrWhite);
    }

    if(showZZTable) {
        DeleteTable("ZZCell");
        DrawTable("ZZCell", ZZ_labels_X, ZZ_labels_Y, ZZ_Table_Values, ZZ_Colors, ZZ_FontSizes, zzRows, zzCols, cornerX, zzTableY, clrWhite);
    }
}

/**
 * Hledá hodnoty v tabulkách, které jsou nejblíže aktuální ceně, a zvýrazňuje je.
 * Hodnoty v rozsahu g_HighlightDistance od aktuální ceny jsou označeny zelenou barvou.
 * Hodnota, která je nejblíže aktuální ceně, je zvýrazněna blikající barvou a větším písmem.
 */
void FindClosestToCloseAndColorMap() {
    double dist       = HighlightDistance;
    double closePrice = Close[0];
    double bestDiff   = dist;
    int    bestRow = -1, bestCol = -1;
    bool   isMA = true;

    ArrayInitialize(MA_Colors, DefaultColor);
    ArrayInitialize(D_Colors, DefaultColor);
    ArrayInitialize(ZZ_Colors, DefaultColor);
    ArrayInitialize(MA_FontSizes, DefaultFontSize);
    ArrayInitialize(D_FontSizes, DefaultFontSize);
    ArrayInitialize(ZZ_FontSizes, DefaultFontSize);

    // Blikání mezi červenou a bílou barvou
    color blinkColor = blinkState ? clrRed : clrWhite;

    // --- MA tabulka ---
    for(int ma_j = 0; ma_j < maRows; ma_j++) {
        for(int ma_i = 0; ma_i < maCols; ma_i++) {
            double val  = MA_Values[ma_i][ma_j];
            double diff = MathAbs(val - closePrice);

            if(diff <= dist) {
                MA_Colors[ma_i][ma_j] = CloseRangeColor;

                if(diff < bestDiff) {
                    bestDiff = diff;
                    bestRow  = ma_i;
                    bestCol  = ma_j;
                    isMA     = true;
                    lastTableType = 0;  // MA tabulka
                }
            }
        }
    }

    // --- D tabulka ---
    for(int d_j = 0; d_j < dRows; d_j++) {
        for(int d_i = 0; d_i < dCols; d_i++) {
            double val  = D_Values[d_i][d_j];
            double diff = MathAbs(val - closePrice);

            if(diff <= dist) {
                D_Colors[d_i][d_j] = CloseRangeColor;

                if(diff < bestDiff) {
                    bestDiff = diff;
                    bestRow  = d_i;
                    bestCol  = d_j;
                    isMA     = false;
                    lastTableType = 1;  // D tabulka
                }
            }
        }
    }

    // --- ZZ tabulka ---
    for(int zz_j = 0; zz_j < zzRows; zz_j++) {
        for(int zz_i = 0; zz_i < zzCols; zz_i++) {
            double val  = ZZ_Table_Values[zz_i][zz_j];
            double diff = MathAbs(val - closePrice);

            if(diff <= dist) {
                ZZ_Colors[zz_i][zz_j] = CloseRangeColor;

                if(diff < bestDiff) {
                    bestDiff = diff;
                    bestRow  = zz_i;
                    bestCol  = zz_j;
                    isMA     = false;  // zachováno pro zpětnou kompatibilitu
                    lastTableType = 2;  // ZZ tabulka
                }
            }
        }
    }

    // --- Zvýraznění nejbližší hodnoty ---
    if(bestRow != -1 && bestCol != -1 && bestDiff < dist) {
        // Uložení hodnoty s jejím znaménkem (kladné nebo záporné)
        double bestValue = 0.0;
        if(lastTableType == 0) {
            bestValue = MA_Values[bestRow][bestCol];
        } else if(lastTableType == 1) {
            bestValue = D_Values[bestRow][bestCol];
        } else if(lastTableType == 2) {
            bestValue = ZZ_Table_Values[bestRow][bestCol];
        }

        // Výpočet rozdílu se znaménkem (cena - hodnota)
        LastNearestDistance = closePrice - bestValue;

        // Uložení informací o nejbližší buňce do globálních proměnných pro použití v OnTick()
        lastBestRow = bestRow;
        lastBestCol = bestCol;
        lastIsMA    = (lastTableType == 0);  // zachováno pro zpětnou kompatibilitu

        // Zvýraznění nejbližší buňky pouze pokud je v rozsahu HighlightDistance
        if(bestDiff <= HighlightDistance) {
            if(lastTableType == 0) {  // MA tabulka
                MA_Colors[bestRow][bestCol]    = blinkColor;
                MA_FontSizes[bestRow][bestCol] = HighlightFontSize;

                if(DebugMode && !StrategyTester)
                    Print("Nejbližší MA[", bestRow, "][", bestCol, "] = ",
                          DoubleToString(MA_Values[bestRow][bestCol], 2),
                          ", Close = ", DoubleToString(closePrice, 2),
                          ", Diff = ", DoubleToString(LastNearestDistance, 2),
                          ", V rozsahu pro blikání");
            } else if(lastTableType == 1) {  // D tabulka
                D_Colors[bestRow][bestCol]    = blinkColor;
                D_FontSizes[bestRow][bestCol] = HighlightFontSize;

                if(DebugMode && !StrategyTester)
                    Print("Nejbližší D[", bestRow, "][", bestCol, "] = ",
                          DoubleToString(D_Values[bestRow][bestCol], 2),
                          ", Close = ", DoubleToString(closePrice, 2),
                          ", Diff = ", DoubleToString(LastNearestDistance, 2),
                          ", V rozsahu pro blikání");
            } else if(lastTableType == 2) {  // ZZ tabulka
                ZZ_Colors[bestRow][bestCol]    = blinkColor;
                ZZ_FontSizes[bestRow][bestCol] = HighlightFontSize;

                if(DebugMode && !StrategyTester)
                    Print("Nejbližší ZZ[", bestRow, "][", bestCol, "] = ",
                          DoubleToString(ZZ_Table_Values[bestRow][bestCol], 2),
                          ", Close = ", DoubleToString(closePrice, 2),
                          ", Diff = ", DoubleToString(LastNearestDistance, 2),
                          ", V rozsahu pro blikání");
            }
        } else {
            // Nejbližší buňka není v rozsahu pro blikání, pouze ji označíme zelenou barvou
            if(lastTableType == 0) {  // MA tabulka
                MA_Colors[bestRow][bestCol]    = CloseRangeColor;
                MA_FontSizes[bestRow][bestCol] = DefaultFontSize;

                if(DebugMode && !StrategyTester)
                    Print("Nejbližší MA[", bestRow, "][", bestCol, "] = ",
                          DoubleToString(MA_Values[bestRow][bestCol], 2),
                          ", Close = ", DoubleToString(closePrice, 2),
                          ", Diff = ", DoubleToString(LastNearestDistance, 2),
                          ", Mimo rozsah pro blikání");
            } else if(lastTableType == 1) {  // D tabulka
                D_Colors[bestRow][bestCol]    = CloseRangeColor;
                D_FontSizes[bestRow][bestCol] = DefaultFontSize;

                if(DebugMode && !StrategyTester)
                    Print("Nejbližší D[", bestRow, "][", bestCol, "] = ",
                          DoubleToString(D_Values[bestRow][bestCol], 2),
                          ", Close = ", DoubleToString(closePrice, 2),
                          ", Diff = ", DoubleToString(LastNearestDistance, 2),
                          ", Mimo rozsah pro blikání");
            } else if(lastTableType == 2) {  // ZZ tabulka
                ZZ_Colors[bestRow][bestCol]    = CloseRangeColor;
                ZZ_FontSizes[bestRow][bestCol] = DefaultFontSize;

                if(DebugMode && !StrategyTester)
                    Print("Nejbližší ZZ[", bestRow, "][", bestCol, "] = ",
                          DoubleToString(ZZ_Table_Values[bestRow][bestCol], 2),
                          ", Close = ", DoubleToString(closePrice, 2),
                          ", Diff = ", DoubleToString(LastNearestDistance, 2),
                          ", Mimo rozsah pro blikání");
            }
        }
    } else {
        LastNearestDistance = 0.0;
        lastBestRow         = -1;   // Resetujeme indexy, protože žádná buňka není v rozsahu
        lastBestCol         = -1;
        if(DebugMode && !StrategyTester)
            Print("Žádná hodnota v rozsahu. Close = ", DoubleToString(closePrice, 2));
    }
}

/**
 * Zpracovává události grafu - přetažení GripPointu, změnu velikosti grafu a kliknutí na tlačítka.
 * @param id Identifikátor události
 * @param lparam Dlouhý parametr události
 * @param dparam Desetinný parametr události
 * @param sparam Textový parametr události
 */
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam) {
    // V testovacím módu ignorujeme všechny chart eventy
    if(StrategyTester) return;

    if(DebugMode)
        SafePrint("Event: id = ", IntegerToString(id), ", sparam = ", sparam);

    // Detekce přetažení GripPointu
    if(id == CHARTEVENT_OBJECT_DRAG && sparam == gripPointName) {
        // Získání nových souřadnic GripPointu
        int gripX = (int)ObjectGetInteger(0, gripPointName, OBJPROP_XDISTANCE);
        int gripY = (int)ObjectGetInteger(0, gripPointName, OBJPROP_YDISTANCE);

        // Aktualizace hlavních souřadnic (cornerX, cornerY)
        // GripPoint je 15 pixelů před tlačítkem MA Tabulka, proto přičteme 15
        cornerX = gripX + 15;
        cornerY = gripY + 10;

        // Aktualizace všech prvků podle nové pozice
        UpdateAllElements();
    }

    // Detekce změny velikosti grafu
    if(id == CHARTEVENT_CHART_CHANGE) {
        int newWidth  = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
        int newHeight = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);

        // Pokud se změnila velikost grafu, aktualizujeme pozice
        if(newWidth != chartWidth || newHeight != chartHeight) {
            chartWidth  = newWidth;
            chartHeight = newHeight;

            // Automatické přizpůsobení velikosti buněk podle rozlišení
            AdjustCellSizeForResolution();

            // Vždy aktualizujeme pozici tabulky při změně velikosti grafu
            // aby se zabránilo situaci, kdy je tabulka mimo viditelnou oblast
            SetInitialCoordinates();
            UpdateAllElements();
        }
    }

    // Zpracování kliknutí na objekty
    if(id == CHARTEVENT_OBJECT_CLICK) {
        // Přepínání MA tabulky
        if(sparam == EA_Tag + "_ToggleMA") {
            showMATable = !showMATable;                   // Přepnutí stavu
            UpdateButtonColor("ToggleMA", showMATable);   // Aktualizace barvy tlačítka

            // Výpočet vertikálního odsazení pro tlačítka a tabulky
            int buttonY  = cornerY + 10;   // Tlačítka jsou výše (zmenšeno z 20)
            int maTableY = buttonY + 20;   // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)

            if(showMATable) {
                // Zobrazení tabulky
                // Aktualizace globálních proměnných pro velikosti polí
                maRows = ArraySize(MA_labels_Y);
                maCols = ArraySize(MA_labels_X);
                DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, maRows, maCols, cornerX, maTableY, clrWhite);
            } else {
                // Skrytí tabulky
                DeleteTable("MACell");
            }
        }

        // Přepínání D tabulky
        if(sparam == EA_Tag + "_ToggleD") {
            showDTable = !showDTable;                   // Přepnutí stavu
            UpdateButtonColor("ToggleD", showDTable);   // Aktualizace barvy tlačítka

            // Výpočet vertikálního odsazení pro tlačítka a tabulky
            int buttonY  = cornerY + 10;                              // Tlačítka jsou výše (zmenšeno z 20)
            int maTableY = buttonY + 20;                              // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)
            int dButtonY = maTableY + (currentCellHeight * 4) + 10;   // D tlačítko je pod MA tabulkou s mezerou
            int dTableY  = dButtonY + 20;                             // D tabulka začíná 20 pixelů pod D tlačítkem (zvětšeno z 15)

            if(showDTable) {
                // Zobrazení tabulky
                // Aktualizace globálních proměnných pro velikosti polí
                dRows = ArraySize(D_labels_Y);
                dCols = ArraySize(D_labels_X);
                DrawTable("DCell", D_labels_X, D_labels_Y, D_Values, D_Colors, D_FontSizes, dRows, dCols, cornerX, dTableY, clrWhite);
            } else {
                // Skrytí tabulky
                DeleteTable("DCell");
            }
        }

        // Posun timeframe dopředu
        if(sparam == EA_Tag + "_TFNext") {
            currentTFIndex++;
            if(currentTFIndex > 2)
                currentTFIndex = 0;
            UpdateTFLabels();

            if(showMATable) {
                // Výpočet vertikálního odsazení pro tlačítka a tabulky
                int buttonY  = cornerY + 10;   // Tlačítka jsou výše (zmenšeno z 20)
                int maTableY = buttonY + 20;   // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)

                // Použijeme funkci pro aktualizaci hodnot a vynucené překreslení tabulek
                UpdateTablesWithNewValues(true);
            }
        }

        // Přepínání ZZ tabulky
        if(sparam == EA_Tag + "_ToggleZZ") {
            showZZTable = !showZZTable;                   // Přepnutí stavu
            UpdateButtonColor("ToggleZZ", showZZTable);   // Aktualizace barvy tlačítka

            // Výpočet vertikálního odsazení pro tlačítka a tabulky
            int buttonY  = cornerY + 10;
            int maTableY = buttonY + 20;
            int dButtonY = maTableY + (currentCellHeight * 4) + 10;
            int dTableY  = dButtonY + 20;
            int zzButtonY = dTableY + (currentCellHeight * 3) + 30;
            int zzTableY = zzButtonY + 20;

            if(showZZTable) {
                DrawTable("ZZCell", ZZ_labels_X, ZZ_labels_Y, ZZ_Table_Values, ZZ_Colors, ZZ_FontSizes, zzRows, zzCols, cornerX, zzTableY, clrWhite);
            } else {
                DeleteTable("ZZCell");
            }

            // Použijeme funkci pro aktualizaci hodnot a vynucené překreslení tabulek
            UpdateTablesWithNewValues(true);
        }

        // Posun timeframe zpět
        if(sparam == EA_Tag + "_TFPrev") {
            currentTFIndex--;
            if(currentTFIndex < 0)
                currentTFIndex = 2;
            UpdateTFLabels();

            if(showMATable) {
                // Výpočet vertikálního odsazení pro tlačítka a tabulky
                int buttonY  = cornerY + 10;   // Tlačítka jsou výše (zmenšeno z 20)
                int maTableY = buttonY + 20;   // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)

                // Použijeme funkci pro aktualizaci hodnot a vynucené překreslení tabulek
                UpdateTablesWithNewValues(true);
            }
        }
    }
}

// ----------------------------- ZIG ZAG -----------------------------------

/**
 * Převede časový rámec na index pro pole ZZ hodnot.
 * @param period Časový rámec (PERIOD_M5, PERIOD_H4, PERIOD_D1)
 * @return Index (0=M5, 1=H4, 2=D1) nebo -1 při chybě
 */
int GetPeriodIndex(int period) {
    switch(period) {
        case PERIOD_M5: return 0;
        case PERIOD_H4: return 1;
        case PERIOD_D1: return 2;
        default: return -1;
    }
}

/**
 * Hlavní funkce pro zpracování ZigZag hodnot.
 * Načte ZigZag body pro zadanou periodu a uloží je do globálních polí.
 *
 * @param period Časový rámec (PERIOD_M5, PERIOD_H4, PERIOD_D1)
 */
void ZigZagInfo(int period) {
    int periodIndex = GetPeriodIndex(period);
    if(periodIndex == -1) return;  // Neplatný časový rámec

    int maxPoints = 10;  // Maximálně 10 ZZ hodnot pro každou periodu

    // Vytvoření lokálních polí pro načtení dat
    double tempValues[10];
    int tempIndexes[10];
    string tempMessages[10];

    int foundPoints = LoadZigZagPoints(maxPoints, tempValues, tempIndexes, period);
    if(foundPoints == 0) {
        if(DebugMode && !StrategyTester) {
            Print("? Žádné ZigZag body nenalezeny pro periodu ", period);
        }
        return;
    }

    // Kopírování do globálních polí
    for(int i = 0; i < foundPoints && i < 10; i++) {
        zzValues[periodIndex][i] = tempValues[i];
        zzIndexes[periodIndex][i] = tempIndexes[i];
        zzMessages[periodIndex][i] = StringFormat("ZZ[%d]: %.5f", i, tempValues[i]);
    }

    // Naplnění ZZ_Table_Values tabulky pro zobrazení
    if(foundPoints >= 2) {
        double highValue = MathMax(tempValues[0], tempValues[1]);
        double lowValue = MathMin(tempValues[0], tempValues[1]);
        double midValue = (highValue + lowValue) / 2;

        // Uložení hodnot podle správného rozložení:
        // zzHigh/zzLow/zzHL50 řádky, M5/H4/D1 sloupce
        ZZ_Table_Values[0][periodIndex] = highValue;  // zzHigh řádek
        ZZ_Colors[0][periodIndex] = clrLightSalmon;
        ZZ_FontSizes[0][periodIndex] = 10;

        ZZ_Table_Values[1][periodIndex] = lowValue;   // zzLow řádek
        ZZ_Colors[1][periodIndex] = clrLightSalmon;
        ZZ_FontSizes[1][periodIndex] = 10;

        ZZ_Table_Values[2][periodIndex] = midValue;   // zzHL50 řádek
        ZZ_Colors[2][periodIndex] = clrViolet;
        ZZ_FontSizes[2][periodIndex] = 10;
    }

    if(FileExport && periodIndex == 0)  // Export pouze pro M5
        ExportZigZagData(foundPoints, tempValues, tempIndexes);

    PrintZigZagPreview(foundPoints, tempValues, tempIndexes, tempMessages);
}

/**
 * Načte ZigZag body z indikátoru.
 * Prochází svíčky a hledá nenulové hodnoty ZigZag indikátoru.
 *
 * @param maxPoints Maximální počet bodů, které se mají načíst
 * @param values Pole pro uložení hodnot ZigZag bodů
 * @param indexes Pole pro uložení indexů (pozic) ZigZag bodů
 * @param period Timeframe pro ZigZag
 * @return Počet nalezených ZigZag bodů
 */
int LoadZigZagPoints(int maxPoints, double &values[], int &indexes[], int period) {
    int found = 0, shift = 0;
    while(found < maxPoints && shift < Bars) {
        double val = iCustom(NULL, period, "ZigZag", 12, 5, 3, 0, shift);
        if(val != 0.0) {
            values[found]  = val;
            indexes[found] = shift;
            found++;
        }
        shift++;
    }

    // Debug výpis pouze pokud je zapnutý debug režim
    if(DebugMode && found > 0 && !StrategyTester) {
        Print("Načteno ", found, " ZigZag bodů");
    }

    return found;
}

/**
 * Exportuje ZigZag data do CSV a TXT souborů.
 * Vytvoří soubory s detailními informacemi o ZigZag bodech, včetně statistik.
 *
 * @param foundPoints Počet nalezených ZigZag bodů
 * @param values Pole s hodnotami ZigZag bodů
 * @param indexes Pole s indexy (pozicemi) ZigZag bodů
 */
void ExportZigZagData(int foundPoints, double &values[], int &indexes[]) {
    string dir     = "ZigZagExport\\";
    string csvPath = dir + "ZigZag_Export.csv";
    string txtPath = dir + "ZigZag_Export.txt";

    int csvHandle = FileOpen(csvPath, FILE_WRITE | FILE_CSV, ',');
    int txtHandle = FileOpen(txtPath, FILE_WRITE | FILE_TXT);
    if(csvHandle == INVALID_HANDLE || txtHandle == INVALID_HANDLE) {
        if(DebugMode && !StrategyTester) {
            Print("? Nelze otevřít výstupní soubory.");
        }
        return;
    }

    FileWrite(csvHandle, "Datum a čas", "Index", "Hodnota", "Typ", "Rozdíl v pips", "Čas mezi body", "Rozdíl %");
    FileWriteString(txtHandle, "Datum a čas        Index   Hodnota     Typ       Rozdíl v pips   Čas mezi body    Rozdíl %\r\n");

    double sumPlus = 0.0, sumMinus = 0.0;
    int    countPlus = 0, countMinus = 0;
    int    timePlus = 0, timeMinus = 0;
    double maxRise = 0.0, maxDrop = 0.0;
    string maxRiseTime = "", maxDropTime = "";
    string maxRisePct = "", maxDropPct = "";
    double pointMultiplier = MathPow(10, Digits) / 100;

    for(int i = 1; i < foundPoints; i++) {
        double diff    = (values[i - 1] - values[i]) * pointMultiplier;
        int    seconds = (int)MathAbs(Time[indexes[i - 1]] - Time[indexes[i]]);
        int    minutes = seconds / 60, hours = minutes / 60, days = hours / 24;
        hours   %= 24;
        minutes %= 60;

        string dt      = TimeToStringCustom(Time[indexes[i - 1]]);
        string valStr  = DoubleToString(NormalizeDouble(values[i - 1], Digits), Digits);
        string type    = (values[i - 1] > values[i]) ? "Maximum" : "Minimum";
        string timeStr = StringFormat("%d dní %02d:%02d", days, hours, minutes);
        string diffStr = StringFormat("%+.1f", diff);
        string pctStr  = (values[i] != 0.0) ? StringFormat("%+.2f %%", ((values[i - 1] - values[i]) / values[i]) * 100.0) : "-";

        if(diff > 0) {
            sumPlus  += diff;
            timePlus += seconds;
            countPlus++;
            if(diff > maxRise) {
                maxRise     = diff;
                maxRiseTime = dt;
                maxRisePct  = pctStr;
            }
        } else if(diff < 0) {
            double absD  = MathAbs(diff);
            sumMinus    += absD;
            timeMinus   += seconds;
            countMinus++;
            if(absD > maxDrop) {
                maxDrop     = absD;
                maxDropTime = dt;
                maxDropPct  = pctStr;
            }
        }

        FileWrite(csvHandle, dt, indexes[i - 1], valStr, type, diffStr, timeStr, pctStr);
        FileWriteString(txtHandle, StringFormat("%-18s %-7d %-10s %-9s %-15s %-16s %s\r\n", dt, indexes[i - 1], valStr, type, diffStr, timeStr, pctStr));
    }

    FileWriteString(txtHandle, "\r\n--- Sumář ---\r\n");
    FileWriteString(txtHandle, StringFormat("Suma +: %.1f\r\n", sumPlus));
    FileWriteString(txtHandle, StringFormat("Suma -: %.1f\r\n", sumMinus));
    FileWriteString(txtHandle, StringFormat("Celkový rozdíl: %.1f\r\n", sumPlus - sumMinus));

    int avgPlus  = (countPlus > 0) ? timePlus / countPlus : 0;
    int avgMinus = (countMinus > 0) ? timeMinus / countMinus : 0;

    FileWriteString(txtHandle, StringFormat("Průměrná délka trvání růstu: %s\r\n", FormatDuration(avgPlus)));
    FileWriteString(txtHandle, StringFormat("Průměrná délka trvání poklesu: %s\r\n", FormatDuration(avgMinus)));
    FileWriteString(txtHandle, StringFormat("Maximální růst: %s, %.1f bodů (%s)\r\n", maxRiseTime, maxRise, maxRisePct));
    FileWriteString(txtHandle, StringFormat("Maximální pokles: %s, %.1f bodů (%s)\r\n", maxDropTime, maxDrop, maxDropPct));

    FileClose(csvHandle);
    FileClose(txtHandle);

    RunExcelExport();
}

/**
 * Vytvoří textové zprávy s informacemi o ZigZag bodech.
 * Tyto zprávy obsahují hodnoty, indexy, typy bodů a vzdálenosti mezi nimi.
 * Hodnoty jsou vypisovány pouze pokud je zapnutý debug režim.
 *
 * @param foundPoints Počet nalezených ZigZag bodů
 * @param values Pole s hodnotami ZigZag bodů
 * @param indexes Pole s indexy (pozicemi) ZigZag bodů
 * @param messages Pole pro uložení výsledných textových zpráv
 */
void PrintZigZagPreview(int foundPoints, double &values[], int &indexes[], string &messages[]) {
    // Pokud není zapnutý debug režim, pouze připravíme zprávy, ale nebudeme je vypisovat
    double pointMultiplier = MathPow(10, Digits) / 100;
    for(int i = 0; i < MathMin(EALastValues, foundPoints); i++) {
        string zigzagType = "Neznámý";
        if(i < foundPoints - 1)
            zigzagType = (values[i] > values[i + 1]) ? "Maximum" : "Minimum";
        else if(i > 0)
            zigzagType = (values[i] > values[i - 1]) ? "Maximum" : "Minimum";

        string distanceInfo = "";
        if(i < foundPoints - 1) {
            double priceDistance = MathAbs(values[i + 1] - values[i]);
            int    barDistance   = MathAbs(indexes[i + 1] - indexes[i]);
            double pipDistance   = priceDistance * pointMultiplier;
            double speed         = (barDistance > 0) ? (pipDistance / barDistance) : 0;
            distanceInfo         = StringFormat(", Vzdál.: %.1f, %d svíček, %.2f b/s", pipDistance, barDistance, speed);
        }

        messages[i] = StringFormat("ZigZag bod č. %d: Hodnota = %s, Index = %d, Typ = %s%s",
                                   i + 1,
                                   DoubleToString(values[i], Digits),
                                   indexes[i],
                                   zigzagType,
                                   distanceInfo);
    }
}

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
string TimeToStringCustom(datetime t) {
    MqlDateTime dt;
    TimeToStruct(t, dt);
    return StringFormat("%02d.%02d.%04d %02d:%02d", dt.day, dt.mon, dt.year, dt.hour, dt.min);
}

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
string FormatDuration(int totalSec) {
    int minutes  = totalSec / 60;
    int hours    = minutes / 60;
    int days     = hours / 24;
    hours       %= 24;
    minutes     %= 60;
    return StringFormat("%d dnů, %02d hodin, %02d minut", days, hours, minutes);
}

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void RunExcelExport() {
    string batRelative  = "ZigZagExport\\export_excel.bat";
    string terminalPath = TerminalInfoString(TERMINAL_DATA_PATH);
    string batFullPath  = terminalPath + "\\MQL4\\Files\\" + batRelative;

    ushort batPathUnicode[256];
    StringToShortArray(batFullPath, batPathUnicode);

    int result = ShellExecuteW(0, "open", batFullPath, "", "", 1);

    if(DebugMode && !StrategyTester) {
        if(result <= 32)
            Print("Spuštění BAT selhalo, kód chyby: ", result);
        else
            Print("Spouštím BAT: ", batFullPath);
    }
}

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void RunPythonScript2() {
    string batPath    = TerminalInfoString(TERMINAL_DATA_PATH) + "\\MQL4\\Files\\ZigZagExport\\export_excel.bat";
    string commandStr = "cmd /c \"" + batPath + "\"";

    // Převod na char pole
    uchar commandArray[];
    StringToCharArray(commandStr, commandArray);

    if(DebugMode && !StrategyTester) {
        Print("📦 Spouštím příkaz: ", commandStr);
    }

    // Spuštění
    //WinExec(commandArray, 1);   // 1 = SW_SHOWNORMAL
}

/**
 * Aktualizuje zobrazení ZZ vzdálenosti na grafu.
 * Tato funkce je volána v OnTick a používá globální proměnnou zzDistance.
 * Zobrazuje aktuální vzdálenost mezi ZigZag body a změnu od poslední hodnoty.
 */
void UpdateZZDistanceLabel(double zzdistance) {
    string mainLabelName = EA_Tag + "_ZZ_MAIN_INFO";
    string timeLabelName = EA_Tag + "_ZZ_TIME_INFO";
    string atrLabelName  = EA_Tag + "_ZZ_ATR_INFO";

    // ❗ Vynuluj zzLastDistanceM5, pokud má jiné znaménko než zzdistance
    if(MathSign(zzdistance) != MathSign(zzLastDistanceM5)) {
        zzLastDistanceM5 = 0;
    }

    // Výpočet MA100 vzdálenosti na M5 (pokud je zapnutá)
    if(MA100_dist) {
        double ema100_M5 = iMA(NULL, PERIOD_M5, 100, 0, MODE_EMA, PRICE_CLOSE, 1);
        double lastCloseM1 = iClose(_Symbol, PERIOD_M1, 1);
        ema100_dist = lastCloseM1 - ema100_M5;
        ema100_dist_abs = MathAbs(ema100_dist);
    }

    // Výpočet MA100 vzdálenosti na M1 (pokud je zapnutá)
    if(MA100_1_dist) {
        double ema100_M1 = iMA(NULL, PERIOD_M1, 100, 0, MODE_EMA, PRICE_CLOSE, 1);
        double lastCloseM1 = iClose(_Symbol, PERIOD_M1, 1);
        ema100_1_dist = lastCloseM1 - ema100_M1;
        ema100_1_dist_abs = MathAbs(ema100_1_dist);
    }

    // Výpočet rozdílu od poslední hodnoty
    double difference = zzdistance - zzLastDistanceM5;

    // Formátovaný text: aktuální vzdálenost a rozdíl
    string mainText = StringFormat("ZZ vzdál. M5: %.1f bodů (%+.1f)", zzdistance, difference);

    // Aktualizace ATR hodnot s zaokrouhlením na počet desetinných míst
    ATR14_D1       = NormalizeDouble(iATR(NULL, PERIOD_D1, 14, 1), Digits);
    greenATRLimit  = NormalizeDouble(ATR14_D1 / 3, Digits);
    orangeATRLimit = NormalizeDouble(ATR14_D1 / 4, Digits);
    redATRLimit    = NormalizeDouble(ATR14_D1 / 5, Digits);
    mg2DistanceLimit = NormalizeDouble(ATR14_D1 * MG2_Distance_Mult, Digits);  // Aktualizace MG2 vzdálenostního limitu

    // Formátování textu pro ATR hodnoty - budeme používat jednotlivé barevné objekty
    // ale zobrazíme je na jednom řádku s oddělovači

    // Aktuální čas
    datetime now      = TimeCurrent();
    string   timeStr  = TimeToString(now, TIME_SECONDS);
    string   timeText = "Last Update: " + timeStr;

    // Nastavení pozice
    int corner  = SelectedCorner;
    int offsetX = 30;
    int offsetY = 20;

    // Výběr barvy dle vzdálenosti
    color  fontColor;
    double absDistance = MathAbs(zzdistance);

    // Opravená logika pro určení barvy
    if(absDistance >= greenATRLimit)
        fontColor = clrLime;   // ZELENÁ - pokud je vzdálenost větší nebo rovna zelené hranici
    else if(absDistance >= orangeATRLimit)
        fontColor = clrOrange;   // ORANŽOVÁ - pokud je vzdálenost mezi zelenou a oranžovou hranicí
    else
        fontColor = clrRed;   // ČERVENÁ - pokud je vzdálenost menší než oranžová hranice

    // Debug výpis pouze pokud je zapnutý debug režim
    if(DebugMode && !StrategyTester) {
        Print("Distance: ", zzdistance, ", LDist: ", zzLastDistanceM5,
              ", ATR: ", ATR14_D1, ", Green: ", greenATRLimit, ", Orange: ", orangeATRLimit, ", Red: ", redATRLimit);
    }

    // Uložení nové hodnoty
    zzLastDistanceM5 = zzdistance;

    // Fonty a velikosti
    string fontMain      = "Arial Bold";
    string fontSmall     = "Arial";
    int    fontSizeMain  = 18;
    int    fontSizeSmall = 10;

    // Vykreslení hlavního textu
    ObjectDelete(mainLabelName);
    ObjectCreate(mainLabelName, OBJ_LABEL, 0, 0, 0);
    ObjectSetText(mainLabelName, mainText, fontSizeMain, fontMain, fontColor);
    ObjectSet(mainLabelName, OBJPROP_CORNER, corner);
    ObjectSet(mainLabelName, OBJPROP_XDISTANCE, offsetX);
    ObjectSet(mainLabelName, OBJPROP_YDISTANCE, offsetY);

    // Vykreslení barevných ATR hodnot na jednom řádku
    int yPosition = offsetY + fontSizeMain + 8;
    int xPosition = offsetX + 220;
    int xOffset   = 0;

    // Vykreslení základního ATR textu
    DrawColoredText("ATR_Base", StringFormat(baseFormat, ATR14_D1), fontColor, corner, xPosition, yPosition, fontSizeSmall, fontSmall);
    xOffset = -225;   // Odsazení pro další text

    // Vykreslení zelené hodnoty s hvězdičkou, pokud je vybrána
    string greenText = (MinZZDistForTrade == ZZ_Green) ?
                           StringFormat(formatWithStar, "G", greenATRLimit) :
                           StringFormat(formatWithoutStar, "G", greenATRLimit);
    DrawColoredText("ATR_Green", greenText, clrLime, corner, xPosition + xOffset, yPosition, fontSizeSmall, fontSmall);

    xOffset += 70;   // Další odsazení

    // Vykreslení oranžové hodnoty s hvězdičkou, pokud je vybrána
    string orangeText = (MinZZDistForTrade == ZZ_Orange) ?
                            StringFormat(formatWithStar, "O", orangeATRLimit) :
                            StringFormat(formatWithoutStar, "O", orangeATRLimit);
    DrawColoredText("ATR_Orange", orangeText, clrOrange, corner, xPosition + xOffset, yPosition, fontSizeSmall, fontSmall);

    xOffset += 70;   // Další odsazení

    // Vykreslení červené hodnoty s hvězdičkou, pokud je vybrána
    string redText = (MinZZDistForTrade == ZZ_Red) ?
                         StringFormat(formatWithStar, "R", redATRLimit) :
                         StringFormat(formatWithoutStar, "R", redATRLimit);
    DrawColoredText("ATR_Red", redText, clrRed, corner, xPosition + xOffset, yPosition, fontSizeSmall, fontSmall);

    // Aktualizace textu "Searching for..." s aktuální hodnotou LD
    UpdateSearchingText();

    // Vykreslení času (posunuté o dva řádky dolů pod text o hledání obchodu)
    ObjectDelete(timeLabelName);
    ObjectCreate(timeLabelName, OBJ_LABEL, 0, 0, 0);
    ObjectSetText(timeLabelName, timeText, fontSizeSmall, fontSmall, fontColor);
    ObjectSet(timeLabelName, OBJPROP_CORNER, corner);
    ObjectSet(timeLabelName, OBJPROP_XDISTANCE, offsetX);
    ObjectSet(timeLabelName, OBJPROP_YDISTANCE, offsetY + fontSizeMain + 8 + fontSizeSmall + 5 + fontSizeSmall + 5 + fontSizeSmall + 5);

    // Vykreslení MA100 vzdálenosti pod "Last Update"
    UpdateMA100DistanceLabel(corner, offsetX, offsetY + fontSizeMain + 8 + fontSizeSmall + 5 + fontSizeSmall + 5 + fontSizeSmall + 5 + fontSizeSmall + 5, fontSizeSmall, fontSmall);

    // Vykreslení MA100 M1 vzdálenosti pod MA100 M5 vzdáleností
    UpdateMA100_1_DistanceLabel(corner, offsetX, offsetY + fontSizeMain + 8 + fontSizeSmall + 5 + fontSizeSmall + 5 + fontSizeSmall + 5 + fontSizeSmall + 5 + fontSizeSmall + 5, fontSizeSmall, fontSmall);
}

/**
 * Aktualizuje zobrazení MA100 vzdálenosti na grafu.
 * Zobrazuje vzdálenost od MA100 na M5 timeframe s barevnou logikou podle splnění podmínky.
 */
void UpdateMA100DistanceLabel(int corner, int offsetX, int offsetY, int fontSize, string font) {
    if(!MA100_dist) return; // Pokud je MA100_dist vypnuté, nezobrazujeme nic

    string ma100LabelName = EA_Tag + "_MA100_DISTANCE_INFO";

    // Formátovaný text s MA100 vzdáleností
    string ma100Text = StringFormat("MA100 M5 vzdál.: %.1f bodů", ema100_dist);

    // Určení barvy podle splnění podmínky
    color ma100Color;
    if(ema100_dist_abs > greenATRLimit) {
        ma100Color = clrLime;   // ZELENÁ - podmínka splněna
    } else {
        ma100Color = clrRed;    // ČERVENÁ - podmínka nesplněna
    }

    // Vykreslení MA100 vzdálenosti
    ObjectDelete(ma100LabelName);
    ObjectCreate(ma100LabelName, OBJ_LABEL, 0, 0, 0);
    ObjectSetText(ma100LabelName, ma100Text, fontSize, font, ma100Color);
    ObjectSet(ma100LabelName, OBJPROP_CORNER, corner);
    ObjectSet(ma100LabelName, OBJPROP_XDISTANCE, offsetX);
    ObjectSet(ma100LabelName, OBJPROP_YDISTANCE, offsetY);
}

/**
 * Aktualizuje zobrazení MA100 vzdálenosti na M1 timeframe na grafu.
 * Zobrazuje vzdálenost od MA100 na M1 timeframe s barevnou logikou podle splnění podmínky.
 */
void UpdateMA100_1_DistanceLabel(int corner, int offsetX, int offsetY, int fontSize, string font) {
    if(!MA100_1_dist) return; // Pokud je MA100_1_dist vypnuté, nezobrazujeme nic

    string ma100_1_LabelName = EA_Tag + "_MA100_1_DISTANCE_INFO";

    // Formátovaný text s MA100 vzdáleností na M1
    string ma100_1_Text = StringFormat("MA100 M1 vzdál.: %.1f bodů", ema100_1_dist);

    // Určení barvy podle splnění podmínky
    color ma100_1_Color;
    if(ema100_1_dist_abs > orangeATRLimit) {
        ma100_1_Color = clrLime;   // ZELENÁ - podmínka splněna
    } else {
        ma100_1_Color = clrRed;    // ČERVENÁ - podmínka nesplněna
    }

    // Vykreslení MA100 M1 vzdálenosti
    ObjectDelete(ma100_1_LabelName);
    ObjectCreate(ma100_1_LabelName, OBJ_LABEL, 0, 0, 0);
    ObjectSetText(ma100_1_LabelName, ma100_1_Text, fontSize, font, ma100_1_Color);
    ObjectSet(ma100_1_LabelName, OBJPROP_CORNER, corner);
    ObjectSet(ma100_1_LabelName, OBJPROP_XDISTANCE, offsetX);
    ObjectSet(ma100_1_LabelName, OBJPROP_YDISTANCE, offsetY);
}

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
int MathSign(double value) {
    if(value > 0)
        return 1;
    if(value < 0)
        return -1;
    return 0;
}

//+------------------------------------------------------------------+
//| Univerzální funkce pro otevření obchodů (market i čekající)      |
//+------------------------------------------------------------------+
int OpenOrder(ENUM_ORDER_TYPE type, double price = 0.0, double stopLoss = 0.0, double takeProfit = 0.0, double volume = 0.0, string comment = "", int magic = -1, datetime expiration = 0, color orderColor = clrNONE) {
    // Kontrola, zda již nemáme aktivní pozici nebo čekající pokyn ve stejném směru PRO KONKRÉTNÍ MAGIC NUMBER
    bool isBuyDirection = (type == OP_BUY || type == OP_BUYLIMIT || type == OP_BUYSTOP);
    bool isSellDirection = (type == OP_SELL || type == OP_SELLLIMIT || type == OP_SELLSTOP);

    // Určení magic number pro kontrolu
    int magicNumber = (magic == -1) ? MagicNo1 : magic;

    if((isBuyDirection && PositionExistsWithMagic(OP_BUY, magicNumber)) || (isSellDirection && PositionExistsWithMagic(OP_SELL, magicNumber))) {
        if(DebugMode && !StrategyTester) {
            string directionStr = isBuyDirection ? "BUY" : "SELL";
            Print("Nelze otevřít obchod typu ", GetOrderTypeText(type), " - již existuje aktivní pozice nebo čekající pokyn ve směru ", directionStr, " pro Magic: ", magicNumber);
        }
        return -1;
    }
    // Výpočet velikosti pozice na základě RiskCapital, pokud není zadána
    if(volume <= 0) {
        // Získání aktuálního zůstatku účtu
        double accountBalance = AccountBalance();

        // Výpočet částky, kterou chceme riskovat (RiskCapital % z účtu)
        double riskAmount = accountBalance * RiskCapital / 100.0;

        // Výpočet velikosti pozice na základě stop lossu
        double slPoints;

        // Nastavení výchozích hodnot pro SL, pokud nejsou zadány
        if(stopLoss <= 0)
            stopLoss = StopLoss * Point;

        // Výpočet velikosti pozice podle typu objednávky
        if(type == OP_BUY || type == OP_BUYLIMIT || type == OP_BUYSTOP) {
            // Pro BUY objednávky
            double entryPrice = (price <= 0) ? Ask : price;
            double slPrice    = entryPrice - stopLoss;
            slPoints          = entryPrice - slPrice;
        } else {
            // Pro SELL objednávky
            double entryPrice = (price <= 0) ? Bid : price;
            double slPrice    = entryPrice + stopLoss;
            slPoints          = slPrice - entryPrice;
        }

        // Výpočet hodnoty jednoho pipu
        double tickValue = MarketInfo(_Symbol, MODE_TICKVALUE);
        double tickSize  = MarketInfo(_Symbol, MODE_TICKSIZE);
        double pipValue  = tickValue * Point / tickSize;

        // Výpočet velikosti pozice
        if(slPoints > 0 && pipValue > 0) {
            volume = NormalizeDouble(riskAmount / (slPoints * pipValue / Point), 2);

            // Kontrola minimální a maximální velikosti pozice
            double minLot  = MarketInfo(_Symbol, MODE_MINLOT);
            double maxLot  = MarketInfo(_Symbol, MODE_MAXLOT);
            double lotStep = MarketInfo(_Symbol, MODE_LOTSTEP);

            // Zaokrouhlení na nejbližší platnou velikost lotu
            volume = MathFloor(volume / lotStep) * lotStep;

            // Omezení na minimální a maximální velikost lotu
            volume = MathMax(minLot, MathMin(maxLot, volume));

            if(!StrategyTester) Print("Vypočtená velikost pozice: ", volume, " lotů (Risk: ", RiskCapital, "% = ", riskAmount, " ", AccountCurrency(), ", SL: ", slPoints, " bodů)");
        } else {
            // Pokud nelze vypočítat velikost pozice, použijeme výchozí hodnotu 0.1
            volume = 0.1;
            if(!StrategyTester) Print("Nelze vypočítat velikost pozice, použita výchozí hodnota: ", volume, " lotů");
        }
    }

    // Nastavení výchozích hodnot, pokud nejsou zadány
    if(price <= 0) {
        // Pro market objednávky použijeme aktuální cenu
        if(type == OP_BUY)
            price = NormalizeDouble(Ask, Digits);
        else if(type == OP_SELL)
            price = NormalizeDouble(Bid, Digits);
        // Pro čekající objednávky je nutné zadat cenu
        else {
            if(!StrategyTester) Print("Chyba: Pro čekající objednávky je nutné zadat cenu");
            return -1;
        }
    } else {
        // Normalizace ceny na správný počet desetinných míst
        price = NormalizeDouble(price, Digits);
    }

    // Nastavení výchozích hodnot pro SL a TP, pokud nejsou zadány
    if(stopLoss <= 0)
        stopLoss = StopLoss * Point;
    if(takeProfit <= 0)
        takeProfit = TakeProfit * Point;

    // Nastavení výchozí barvy, pokud není zadána
    if(orderColor == clrNONE) {
        if(type == OP_BUY || type == OP_BUYLIMIT || type == OP_BUYSTOP)
            orderColor = clrBlue;
        else
            orderColor = clrRed;
    }

    // Nastavení výchozího komentáře, pokud není zadán
    if(comment == "") {
        // Použití globální proměnné NearestLevelInfo pro informaci o nejbližším levelu
        string levelInfo = "";
        if(NearestLevelInfo != "") {
            levelInfo = " - NL: " + NearestLevelInfo;
        }

        if(type == OP_BUY || type == OP_SELL)
            comment = EA_Trade_Tag + "Market" + levelInfo;
        else if(type == OP_BUYLIMIT)
            comment = EA_Trade_Tag + "Buy LM" + levelInfo;
        else if(type == OP_SELLLIMIT)
            comment = EA_Trade_Tag + "Sell LM" + levelInfo;
        else if(type == OP_BUYSTOP)
            comment = EA_Trade_Tag + "Buy St" + levelInfo;
        else if(type == OP_SELLSTOP)
            comment = EA_Trade_Tag + "Sell St" + levelInfo;
    }

    // Výpočet SL a TP cen podle typu objednávky
    double slPrice = 0, tpPrice = 0;

    switch(type) {
        case OP_BUY:
        case OP_BUYSTOP:
        case OP_BUYLIMIT:
            slPrice = NormalizeDouble(price - stopLoss, Digits);
            tpPrice = NormalizeDouble(price + takeProfit, Digits);
            break;
        case OP_SELL:
        case OP_SELLSTOP:
        case OP_SELLLIMIT:
            slPrice = NormalizeDouble(price + stopLoss, Digits);
            tpPrice = NormalizeDouble(price - takeProfit, Digits);
            break;
    }

    // Pro čekající objednávky nejprve zrušíme existující pokyny stejného typu PRO STEJNÝ MAGIC NUMBER
    if(type == OP_BUYLIMIT) {
        if(magicNumber == MagicNo1 && pendingBuyTicket > 0) {
            if(!OrderDelete(pendingBuyTicket)) {
                if(!StrategyTester) Print("Chyba při rušení předchozího MG1 Buy Limit pokynu: ", GetLastError());
            }
            pendingBuyTicket = 0;
        } else if(magicNumber == MagicNo2 && pendingBuyTicket_MG2 > 0) {
            if(!OrderDelete(pendingBuyTicket_MG2)) {
                if(!StrategyTester) Print("Chyba při rušení předchozího MG2 Buy Limit pokynu: ", GetLastError());
            }
            pendingBuyTicket_MG2 = 0;
        }
    } else if(type == OP_SELLLIMIT) {
        if(magicNumber == MagicNo1 && pendingSellTicket > 0) {
            if(!OrderDelete(pendingSellTicket)) {
                if(!StrategyTester) Print("Chyba při rušení předchozího MG1 Sell Limit pokynu: ", GetLastError());
            }
            pendingSellTicket = 0;
        } else if(magicNumber == MagicNo2 && pendingSellTicket_MG2 > 0) {
            if(!OrderDelete(pendingSellTicket_MG2)) {
                if(!StrategyTester) Print("Chyba při rušení předchozího MG2 Sell Limit pokynu: ", GetLastError());
            }
            pendingSellTicket_MG2 = 0;
        }
    }

    // Odeslání objednávky
    // Použití již určeného magicNumber
    int ticket = OrderSend(_Symbol, type, volume, price, 3, slPrice, tpPrice, comment, magicNumber, expiration, orderColor);

    // Zpracování výsledku
    if(ticket < 0) {
        if(!StrategyTester) Print("Chyba při otevírání obchodu typu ", GetOrderTypeText(type), ": ", GetLastError());
        return -1;
    } else {
        if(!StrategyTester) Print("Obchod typu ", GetOrderTypeText(type), " otevřen, ticket: ", ticket);

        // Aktualizace globálních proměnných pro čekající pokyny podle magic number
        if(type == OP_BUYLIMIT) {
            if(magicNumber == MagicNo1) {
                pendingBuyTicket = ticket;
                pendingBuyPrice  = price;
            } else if(magicNumber == MagicNo2) {
                pendingBuyTicket_MG2 = ticket;
                pendingBuyPrice_MG2  = price;
            }
        } else if(type == OP_SELLLIMIT) {
            if(magicNumber == MagicNo1) {
                pendingSellTicket = ticket;
                pendingSellPrice  = price;
            } else if(magicNumber == MagicNo2) {
                pendingSellTicket_MG2 = ticket;
                pendingSellPrice_MG2  = price;
            }
        }

        return ticket;
    }
}

//+------------------------------------------------------------------+
//| Pomocná funkce pro získání textového popisu typu objednávky      |
//+------------------------------------------------------------------+
string GetOrderTypeText(ENUM_ORDER_TYPE type) {
    switch(type) {
        case OP_BUY:
            return "BUY";
        case OP_SELL:
            return "SELL";
        case OP_BUYLIMIT:
            return "BUY LIMIT";
        case OP_SELLLIMIT:
            return "SELL LIMIT";
        case OP_BUYSTOP:
            return "BUY STOP";
        case OP_SELLSTOP:
            return "SELL STOP";
        default:
            return "UNKNOWN";
    }
}

//+------------------------------------------------------------------+
//| Kontrola pozic                                                  |
//+------------------------------------------------------------------+
bool PositionExists(ENUM_ORDER_TYPE type) {
    // Pro BUY a SELL kontrolujeme směr obchodu, ne konkrétní typ
    bool isBuyDirection = (type == OP_BUY || type == OP_BUYLIMIT || type == OP_BUYSTOP);

    for(int i = 0; i < OrdersTotal(); i++) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            // Kontrola symbolu a magického čísla
            if((OrderMagicNumber() == MagicNo1 || OrderMagicNumber() == MagicNo2) && OrderSymbol() == _Symbol) {
                // Kontrola směru obchodu (BUY nebo SELL)
                int orderType = OrderType();
                bool isOrderBuyDirection = (orderType == OP_BUY || orderType == OP_BUYLIMIT || orderType == OP_BUYSTOP);

                // Pokud směr obchodu odpovídá požadovanému směru, vrátíme true
                if(isBuyDirection == isOrderBuyDirection)
                    return true;
            }
        }
    }
    return false;
}

/**
 * Vykreslí ZigZag bod na grafu včetně šipky, popisku a případně středového textu.
 * Tato funkce vizuálně znázorňuje ZigZag body a informace o nich přímo na grafu.
 *
 * @param index Pořadové číslo ZigZag bodu
 * @param price Cenová hodnota ZigZag bodu
 * @param barIndex Index (pozice) svíčky, na které se nachází ZigZag bod
 * @param type Typ bodu ("Maximum" nebo "Minimum")
 * @param distanceInfo Textová informace o vzdálenosti k dalšímu bodu
 * @param drawLabel Určuje, zda se má vykreslit popisek u bodu
 * @param drawMidText Určuje, zda se má vykreslit text uprostřed mezi body
 * @param nextPrice Cenová hodnota následujícího ZigZag bodu (pro středový text)
 * @param nextBarIndex Index (pozice) následujícího ZigZag bodu (pro středový text)
 */
void DrawZigZagPoint(
    int    index,
    double price,
    int    barIndex,
    string type,
    string distanceInfo,
    bool   drawLabel    = true,
    bool   drawMidText  = false,
    double nextPrice    = 0,
    int    nextBarIndex = 0) {
    string arrowName = EA_Tag + "_ZZ_Point_" + IntegerToString(index);
    string labelName = EA_Tag + "_ZZ_Label_" + IntegerToString(index);
    string distLabel = EA_Tag + "_ZZ_Dist_" + IntegerToString(index);

    ObjectDelete(arrowName);
    ObjectDelete(labelName);
    ObjectDelete(distLabel);

    double offset          = MathMax(Point * 50, price * 0.002);
    double priceWithOffset = (type == "Maximum") ? price + offset : price - offset;
    double labelPrice      = (type == "Maximum") ? priceWithOffset + offset * 0.8 : priceWithOffset - offset * 0.8;

    int      shiftRight = 2;
    int      timeIndex  = MathMin(barIndex + shiftRight, Bars - 1);
    datetime labelTime  = Time[timeIndex];

    ObjectCreate(arrowName, OBJ_ARROW, 0, Time[barIndex], priceWithOffset);
    ObjectSet(arrowName, OBJPROP_ARROWCODE, (type == "Maximum") ? 233 : 234);
    ObjectSet(arrowName, OBJPROP_COLOR, (type == "Maximum") ? Red : Blue);
    ObjectSet(arrowName, OBJPROP_WIDTH, 2);

    if(drawLabel) {
        string labelText = "#" + IntegerToString(index);
        if(distanceInfo != "")
            labelText += "\n" + distanceInfo;

        ObjectCreate(labelName, OBJ_TEXT, 0, labelTime, labelPrice);
        ObjectSetText(labelName, labelText, 10, "Arial", clrWhite);
        ObjectSet(labelName, OBJPROP_CORNER, 0);
    }

    if(drawMidText) {
        datetime midTime  = (Time[barIndex] + Time[nextBarIndex]) / 2;
        double   midPrice = (price + nextPrice) / 2;

        ObjectCreate(distLabel, OBJ_TEXT, 0, midTime, midPrice);
        ObjectSetText(distLabel, distanceInfo, 9, "Arial", clrYellow);
        ObjectSet(distLabel, OBJPROP_CORNER, 0);
    }
}

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void DrawMA(string symbol, int timeframe, int period, int maShift, int maType, int appliedPrice, int shift, color lineColor, int lineStyle, int width, string maTypeName) {
    if(StrategyTester) return; // V testovacím módu nevykreslujeme

    // Název MA čáry bude odvozen od period, typu, symbolu a názvu typu MA s prefixem EA_Tag
    maName = EA_Tag + "_MA_" + maTypeName + IntegerToString(period) + "_" + symbol + "_" + IntegerToString(timeframe);

    // Vytvořit MA čáru, pokud již neexistuje
    if(ObjectFind(0, maName) == -1) {
        if(!ObjectCreate(0, maName, OBJ_TREND, 0, Time[0], 0)) {
            Print("Chyba při vytváření MA čáry: ", GetLastError());
            return;
        }

        // Nastavit základní vlastnosti MA čáry
        ObjectSetInteger(0, maName, OBJPROP_COLOR, lineColor);
        ObjectSetInteger(0, maName, OBJPROP_STYLE, lineStyle);
        ObjectSetInteger(0, maName, OBJPROP_WIDTH, width);
        ObjectSetInteger(0, maName, OBJPROP_RAY_RIGHT, false);
    }

    // Získání hodnoty MA pro zadaný symbol, časový rámec a další parametry
    double maValueCurrent  = iMA(symbol, timeframe, period, maShift, maType, appliedPrice, shift);
    double maValuePrevious = iMA(symbol, timeframe, period, maShift, maType, appliedPrice, shift + 1);

    // Nastavení souřadnic pro MA čáru mezi předchozí a aktuální hodnotou
    datetime previousH1BarTime = iTime(symbol, period, 1);
    datetime currentTime       = Time[maShift];
    datetime previousTime      = Time[maShift + 1];

    ObjectMove(0, maName, maShift, previousH1BarTime, maValuePrevious);
    ObjectMove(0, maName, maShift + 1, currentTime, maValueCurrent);
}

//+------------------------------------------------------------------+
//| Funkce pro vykreslení horizontální čáry s popiskem               |
//+------------------------------------------------------------------+
void DrawHorizontalLineWithLabel(string name, double price, color lineColor, ENUM_LINE_STYLE style, int width, string labelText) {
    if(StrategyTester) return; // V testovacím módu nevykreslujeme

    // Název HLine čáry s prefixem EA_Tag
    HLineName = EA_Tag + "_HLine_" + name;

    // Vytvoření čáry nebo aktualizace existující
    if(ObjectFind(0, HLineName) == -1) {
        ObjectCreate(0, HLineName, OBJ_HLINE, 0, 0, 0);
    }
    ObjectSetDouble(0, HLineName, OBJPROP_PRICE, price);        // Nastavení ceny čáry
    ObjectSetInteger(0, HLineName, OBJPROP_COLOR, lineColor);   // Nastavení barvy čáry
    ObjectSetInteger(0, HLineName, OBJPROP_STYLE, style);       // Nastavení stylu čáry
    ObjectSetInteger(0, HLineName, OBJPROP_WIDTH, width);       // Nastavení šířky čáry

    // Vytvoření nebo aktualizace popisku pro čáru s prefixem EA_Tag
    string labelName = EA_Tag + "_HLine_Label_" + name;
    if(ObjectFind(0, labelName) == -1) {
        ObjectCreate(0, labelName, OBJ_TEXT, 0, Time[0], price);   // Vytvoření nového popisku
    } else {
        ObjectMove(0, labelName, 0, Time[0], price);   // Přesunutí existujícího popisku
    }
    ObjectSetInteger(0, labelName, OBJPROP_COLOR, lineColor);            // Nastavení barvy popisku
    ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 10);                // Nastavení velikosti písma popisku
    ObjectSetString(0, labelName, OBJPROP_TEXT, labelText);              // Nastavení textu popisku
    ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_LEFT_LOWER);   // Nastavení pozice popisku
}

//+------------------------------------------------------------------+
//| Funkce pro vykreslení barevného textu na určité pozici           |
//+------------------------------------------------------------------+
void DrawColoredText(string name, string text, color textColor, int corner, int xPos, int yPos, int fontSize, string font) {
    if(StrategyTester) return; // V testovacím módu nevykreslujeme

    string objectName = EA_Tag + "_" + name;

    // Odstranění objektu, pokud již existuje
    ObjectDelete(objectName);

    // Vytvoření nového textového objektu
    ObjectCreate(objectName, OBJ_LABEL, 0, 0, 0);
    ObjectSetText(objectName, text, fontSize, font, textColor);
    ObjectSet(objectName, OBJPROP_CORNER, corner);
    ObjectSet(objectName, OBJPROP_XDISTANCE, xPos);
    ObjectSet(objectName, OBJPROP_YDISTANCE, yPos);
}

//+------------------------------------------------------------------+
//| Funkce pro umístění čekajícího Buy Limit pokynu                  |
//+------------------------------------------------------------------+
int PlaceBuyLimitOrder(double price, double stopLoss, double takeProfit, double volume = 0.0) {
    if(price <= 0 || stopLoss <= 0 || takeProfit <= 0)
        return -1;

    // Kontrola, zda již nemáme aktivní BUY pozici nebo čekající BUY pokyn
    if(PositionExists(OP_BUY)) {
        if(DebugMode && !StrategyTester) {
            Print("Nelze umístit Buy Limit pokyn - již existuje aktivní BUY pozice nebo čekající BUY pokyn");
        }
        return -1;
    }

    // Použití univerzální funkce OpenOrder pro Buy Limit
    return OpenOrder(OP_BUYLIMIT, price, stopLoss, takeProfit, volume);
}

//+------------------------------------------------------------------+
//| Funkce pro umístění čekajícího Sell Limit pokynu                 |
//+------------------------------------------------------------------+
int PlaceSellLimitOrder(double price, double stopLoss, double takeProfit, double volume = 0.0) {
    if(price <= 0 || stopLoss <= 0 || takeProfit <= 0)
        return -1;

    // Kontrola, zda již nemáme aktivní SELL pozici nebo čekající SELL pokyn
    if(PositionExists(OP_SELL)) {
        if(DebugMode && !StrategyTester) {
            Print("Nelze umístit Sell Limit pokyn - již existuje aktivní SELL pozice nebo čekající SELL pokyn");
        }
        return -1;
    }

    // Použití univerzální funkce OpenOrder pro Sell Limit
    return OpenOrder(OP_SELLLIMIT, price, stopLoss, takeProfit, volume);
}

//+------------------------------------------------------------------+
//| Funkce pro zrušení všech čekajících pokynů                       |
//+------------------------------------------------------------------+
void CancelPendingOrders() {
    // Zrušení všech čekajících pokynů pro daný symbol a magic number
    // Když se ruší MG1 pokyny, automaticky se ruší i MG2 pokyny
    for(int i = OrdersTotal() - 1; i >= 0; i--) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            // Kontrola symbolu a magického čísla
            if((OrderMagicNumber() == MagicNo1 || OrderMagicNumber() == MagicNo2) && OrderSymbol() == _Symbol) {
                // Kontrola typu obchodu - pouze čekající pokyny
                int orderType = OrderType();
                if(orderType == OP_BUYLIMIT || orderType == OP_BUYSTOP ||
                   orderType == OP_SELLLIMIT || orderType == OP_SELLSTOP) {

                    int ticket = OrderTicket();
                    if(!OrderDelete(ticket)) {
                        if(!StrategyTester) Print("Chyba při rušení čekajícího pokynu #", ticket, ": ", GetLastError());
                    } else {
                        string typeStr = GetOrderTypeText((ENUM_ORDER_TYPE)orderType);
                        if(!StrategyTester) Print(typeStr, " pokyn #", ticket, " zrušen");

                        // Reset globálních proměnných pro čekající pokyny
                        if(orderType == OP_BUYLIMIT || orderType == OP_BUYSTOP) {
                            if(ticket == pendingBuyTicket) {
                                pendingBuyTicket = 0;
                                pendingBuyPrice = 0.0;
                            } else if(ticket == pendingBuyTicket_MG2) {
                                pendingBuyTicket_MG2 = 0;
                                pendingBuyPrice_MG2 = 0.0;
                            }
                        } else {
                            if(ticket == pendingSellTicket) {
                                pendingSellTicket = 0;
                                pendingSellPrice = 0.0;
                            } else if(ticket == pendingSellTicket_MG2) {
                                pendingSellTicket_MG2 = 0;
                                pendingSellPrice_MG2 = 0.0;
                            }
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Funkce pro zrušení pouze MG2 čekajících pokynů                   |
//+------------------------------------------------------------------+
void CancelPendingOrdersMG2() {
    // Zrušení pouze MG2 čekajících pokynů
    for(int i = OrdersTotal() - 1; i >= 0; i--) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            // Kontrola symbolu a magického čísla - pouze MG2
            if(OrderMagicNumber() == MagicNo2 && OrderSymbol() == _Symbol) {
                // Kontrola typu obchodu - pouze čekající pokyny
                int orderType = OrderType();
                if(orderType == OP_BUYLIMIT || orderType == OP_BUYSTOP ||
                   orderType == OP_SELLLIMIT || orderType == OP_SELLSTOP) {

                    int ticket = OrderTicket();
                    if(!OrderDelete(ticket)) {
                        if(!StrategyTester) Print("Chyba při rušení MG2 čekajícího pokynu #", ticket, ": ", GetLastError());
                    } else {
                        string typeStr = GetOrderTypeText((ENUM_ORDER_TYPE)orderType);
                        if(!StrategyTester) Print("MG2 ", typeStr, " pokyn #", ticket, " zrušen");

                        // Reset globálních proměnných pro MG2 čekající pokyny
                        if(orderType == OP_BUYLIMIT || orderType == OP_BUYSTOP) {
                            if(ticket == pendingBuyTicket_MG2) {
                                pendingBuyTicket_MG2 = 0;
                                pendingBuyPrice_MG2 = 0.0;
                            }
                        } else {
                            if(ticket == pendingSellTicket_MG2) {
                                pendingSellTicket_MG2 = 0;
                                pendingSellPrice_MG2 = 0.0;
                            }
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Funkce pro kontrolu, zda čekající pokyn stále existuje           |
//+------------------------------------------------------------------+
bool IsPendingOrderValid(int ticket) {
    if(ticket <= 0)
        return false;

    if(OrderSelect(ticket, SELECT_BY_TICKET)) {
        // Kontrola magického čísla a symbolu
        if((OrderMagicNumber() == MagicNo1 || OrderMagicNumber() == MagicNo2) && OrderSymbol() == _Symbol) {
            // Kontrola typu obchodu
            int orderType = OrderType();

            // Vrátíme true pouze pro čekající pokyny (BUYLIMIT, SELLLIMIT, BUYSTOP, SELLSTOP)
            if(orderType == OP_BUYLIMIT || orderType == OP_SELLLIMIT ||
               orderType == OP_BUYSTOP || orderType == OP_SELLSTOP) {
                return true;
            }
        }
    }

    return false;
}

//+------------------------------------------------------------------+
//| Funkce pro kontrolu, zda je hodnota v rozsahu pro obchodování    |
//+------------------------------------------------------------------+
bool IsValueInTradingRange(double value) {
    double closePrice = Close[1];
    return (MathAbs(closePrice - value) <= HighlightDistance);
}

//+------------------------------------------------------------------+
//| Funkce pro sledování maximálních profitů v reálném čase          |
//+------------------------------------------------------------------+
void TrackRealTimeMaxProfits() {
    // Procházíme všechny obchody v poli MaxProfit
    for(int i = 0; i < SessionOrderCount; i++) {
        int ticket = (int)MaxProfit[i][MP_TICKET];
        double openPrice = MaxProfit[i][MP_OPEN_PRICE];
        int orderType = (int)MaxProfit[i][MP_TYPE];

        // Pokud nemáme platný ticket nebo otevírací cenu, přeskočíme
        if(ticket <= 0 || openPrice <= 0.0) continue;

        // Získáme aktuální cenu
        double currentBid = MarketInfo(_Symbol, MODE_BID);
        double currentAsk = MarketInfo(_Symbol, MODE_ASK);

        // Vypočítáme aktuální profit v bodech
        double currentProfit = 0.0;

        if(orderType == OP_BUY) {
            // Pro BUY obchod je aktuální profit rozdíl mezi Bid a otevírací cenou
            currentProfit = (currentBid - openPrice) / Point / 100.0; // Převod na body a dělení 100
        } else if(orderType == OP_SELL) {
            // Pro SELL obchod je aktuální profit rozdíl mezi otevírací cenou a Ask
            currentProfit = (openPrice - currentAsk) / Point / 100.0; // Převod na body a dělení 100
        }

        // Aktualizujeme maximální profit, pokud je aktuální profit větší
        if(currentProfit > MaxProfit[i][MP_MAX_PROFIT]) {
            MaxProfit[i][MP_MAX_PROFIT] = currentProfit;

            if(DebugMode && !StrategyTester) {
                string typeStr = (orderType == OP_BUY) ? "BUY" : "SELL";
                Print("Aktualizován maximální profit v reálném čase pro ticket #", ticket,
                      ": ", MaxProfit[i][MP_MAX_PROFIT],
                      " bodů (Bid=", currentBid, ", Ask=", currentAsk, ", OpenPrice=", openPrice, ", Type=", typeStr, ")");
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Funkce pro zápis MaxProfitů do souboru                           |
//+------------------------------------------------------------------+
void WriteMaxProfitsToFile() {
    // Kontrola, zda máme nějaké nové nebo změněné záznamy
    bool hasNewOrChangedRecords = false;

    for(int i = 0; i < SessionOrderCount; i++) {
        int ticket = (int)MaxProfit[i][MP_TICKET];
        double profit = MaxProfit[i][MP_MAX_PROFIT];

        // Kontrola, zda je záznam nový nebo změněný
        if(ticket > 0 && profit > 0.0 && profit != LastSavedProfit[i]) {
            hasNewOrChangedRecords = true;
            break;
        }
    }

    // Pokud nemáme žádné nové nebo změněné záznamy, ukončíme funkci
    if(!hasNewOrChangedRecords) {
        return;
    }

    // Název souboru
    string fileName = "MaxProfitsZZ.txt";

    // Otevření souboru pro přidání textu (FILE_WRITE = přepsat, FILE_READ|FILE_WRITE = přidat)
    int fileHandle = FileOpen(fileName, FILE_READ|FILE_WRITE|FILE_TXT);

    // Kontrola, zda se soubor podařilo otevřít
    if(fileHandle != INVALID_HANDLE) {
        // Přesun na konec souboru
        FileSeek(fileHandle, 0, SEEK_END);

        // Aktuální datum a čas pro záznam
        string currentDateTime = TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES);

        // Zápis hlavičky s aktuálním datem a časem
        FileWrite(fileHandle, "--- Záznam MaxProfitů z " + currentDateTime + " ---");

        // Procházení všech obchodů v poli MaxProfit
        for(int i = 0; i < SessionOrderCount; i++) {
            int ticket = (int)MaxProfit[i][MP_TICKET];
            double profit = MaxProfit[i][MP_MAX_PROFIT];

            // Zapisujeme pouze obchody, které mají platný ticket, nenulový profit a jsou nové nebo změněné
            if(ticket > 0 && profit > 0.0 && profit != LastSavedProfit[i]) {
                // Formátování profitu v bodech (s jedním desetinným místem, protože dělíme 100)
                string profitText = DoubleToString(profit, 1);

                // Formátování data a času ve formátu DD.MM.RRRR HH:MM
                datetime orderTime = (datetime)MaxProfit[i][MP_TIME];
                string dateStr = StringFormat("%02d.%02d.%04d %02d:%02d",
                                             TimeDay(orderTime),
                                             TimeMonth(orderTime),
                                             TimeYear(orderTime),
                                             TimeHour(orderTime),
                                             TimeMinute(orderTime));

                // Získání otevírací ceny a typu obchodu
                double openPrice = MaxProfit[i][MP_OPEN_PRICE];
                int orderType = (int)MaxProfit[i][MP_TYPE];
                string typeStr = (orderType == OP_BUY) ? "BUY" : "SELL";

                // Formátování řádku ve stejném formátu jako v komentářích
                string line = StringFormat("#%d - %s - %s - %s - MaxProfit: %s bodů",
                                          ticket,
                                          dateStr,
                                          typeStr,
                                          DoubleToString(openPrice, Digits), // Formátování otevírací ceny na správný počet desetinných míst
                                          profitText);

                // Zápis řádku do souboru
                FileWrite(fileHandle, line);

                // Aktualizace posledního zapsaného profitu
                LastSavedProfit[i] = profit;
            }
        }

        // Přidání prázdného řádku pro oddělení záznamů
        FileWrite(fileHandle, "");

        // Zavření souboru
        FileClose(fileHandle);

        if(DebugMode && !StrategyTester) {
            Print("Nové MaxProfity byly zapsány do souboru ", fileName);
        }
    } else {
        if(!StrategyTester) Print("Chyba při otevírání souboru ", fileName, ": ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Funkce pro aktualizaci MaxProfit na základě High/Low svíčky      |
//+------------------------------------------------------------------+
void UpdateMaxProfitOnNewCandle() {
    // Nejprve aktualizujeme seznam obchodů (přidáme nové obchody)
    TrackSessionOrders();

    // Získáme High a Low poslední svíčky
    double high = High[1]; // Hodnota High předchozí svíčky
    double low = Low[1];   // Hodnota Low předchozí svíčky

    // Procházíme všechny obchody v poli MaxProfit
    for(int i = 0; i < SessionOrderCount; i++) {
        int ticket = (int)MaxProfit[i][MP_TICKET];
        double openPrice = MaxProfit[i][MP_OPEN_PRICE];
        int orderType = (int)MaxProfit[i][MP_TYPE];

        // Pokud nemáme platný ticket nebo otevírací cenu, přeskočíme
        if(ticket <= 0 || openPrice <= 0.0) continue;

        // Použití pomocné funkce pro výpočet potenciálního profitu
        double potentialProfit = CalculateOrderProfit(openPrice, orderType, high, low);

        // Aktualizujeme maximální profit, pokud je potenciální profit větší
        if(potentialProfit > MaxProfit[i][MP_MAX_PROFIT]) {
            MaxProfit[i][MP_MAX_PROFIT] = potentialProfit;

            if(DebugMode && !StrategyTester) {
                string typeStr = (orderType == OP_BUY) ? "BUY" : "SELL";
                Print("Aktualizován maximální profit pro ticket #", ticket,
                      " na základě High/Low svíčky: ", MaxProfit[i][MP_MAX_PROFIT],
                      " bodů (High=", high, ", Low=", low, ", OpenPrice=", openPrice, ", Type=", typeStr, ")");
            }
        }
    }

    // Zápis MaxProfitů do souboru po aktualizaci
    WriteMaxProfitsToFile();
}

//+------------------------------------------------------------------+
//| Pomocná funkce pro kontrolu, zda je obchod již v seznamu          |
//+------------------------------------------------------------------+
bool IsOrderInMaxProfitArray(int ticket) {
    for(int j = 0; j < SessionOrderCount; j++) {
        if((int)MaxProfit[j][MP_TICKET] == ticket) {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Pomocná funkce pro přidání obchodu do pole MaxProfit             |
//+------------------------------------------------------------------+
void AddOrderToMaxProfitArray(int ticket, datetime orderTime, double openPrice, int orderType, double currentProfit = 0.0, int index = -1) {
    // Pokud index není zadán, použijeme SessionOrderCount a zvýšíme ho
    int arrayIndex = (index >= 0) ? index : SessionOrderCount;

    MaxProfit[arrayIndex][MP_MAX_PROFIT] = 0.0;
    MaxProfit[arrayIndex][MP_TICKET] = (double)ticket;
    MaxProfit[arrayIndex][MP_TIME] = (double)orderTime;
    MaxProfit[arrayIndex][MP_OPEN_PRICE] = openPrice;
    MaxProfit[arrayIndex][MP_TYPE] = (double)orderType;

    if(index < 0) SessionOrderCount++;

    if(DebugMode && !StrategyTester) {
        string typeStr = (orderType == OP_BUY) ? "BUY" : "SELL";
        string orderStatus = (index < 0) ? "nový" : "uzavřený";
        Print("Přidán ", orderStatus, " obchod do session: Ticket=", ticket,
              ", Profit=", currentProfit,
              ", Čas=", TimeToString(orderTime),
              ", OpenPrice=", openPrice,
              ", Type=", typeStr);
    }
}

//+------------------------------------------------------------------+
//| Pomocná funkce pro výpočet profitu obchodu                       |
//+------------------------------------------------------------------+
double CalculateOrderProfit(double openPrice, int orderType, double high, double low) {
    if(orderType == OP_BUY) {
        return (high - openPrice) / Point / 100.0;
    } else if(orderType == OP_SELL) {
        return (openPrice - low) / Point / 100.0;
    }
    return 0.0;
}

//+------------------------------------------------------------------+
//| Funkce pro sledování profitu otevřených pozic                    |
//+------------------------------------------------------------------+
void TrackOrderProfits() {
    // Tato funkce je nyní nahrazena funkcí UpdateMaxProfitOnNewCandle,
    // která se volá pouze při otevření nové svíčky
    UpdateMaxProfitOnNewCandle();
}

//+------------------------------------------------------------------+
//| Funkce pro sledování obchodů od spuštění EA                      |
//+------------------------------------------------------------------+
void TrackSessionOrders() {
    // Procházení všech otevřených pozic
    int total = OrdersTotal();

    // Procházení všech otevřených pozic
    for(int i = 0; i < total; i++) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            // Sledujeme pouze obchody na aktuálním symbolu s naším Magic Number
            if(OrderSymbol() == _Symbol && (OrderMagicNumber() == MagicNo1 || OrderMagicNumber() == MagicNo2)) {
                // Pouze market obchody (BUY/SELL)
                if(OrderType() <= OP_SELL) {
                    // Kontrola, zda byl obchod otevřen po spuštění EA
                    if(OrderOpenTime() >= EAStartTime) {
                        // Výpočet aktuálního profitu včetně swapů a komisí
                        double currentProfit = (OrderProfit() + OrderSwap() + OrderCommission()) / 10.0; // Dělíme 10 pro správnou hodnotu
                        int ticket = OrderTicket();

                        // Kontrola, zda je obchod již v seznamu
                        if(!IsOrderInMaxProfitArray(ticket) && SessionOrderCount < 5) {
                            // Pokud obchod není v seznamu a máme místo, přidáme ho
                            datetime orderTime = OrderOpenTime();
                            double openPrice = NormalizeDouble(OrderOpenPrice(), Digits); // Normalizace na počet desetinných míst Digits
                            int orderType = OrderType();

                            AddOrderToMaxProfitArray(ticket, orderTime, openPrice, orderType, currentProfit);
                        }
                    }
                }
            }
        }
    }

    // Procházení uzavřených obchodů v historii
    int historyTotal = OrdersHistoryTotal();

    for(int i = 0; i < historyTotal; i++) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_HISTORY)) {
            // Sledujeme pouze obchody na aktuálním symbolu s naším Magic Number
            if(OrderSymbol() == _Symbol && (OrderMagicNumber() == MagicNo1 || OrderMagicNumber() == MagicNo2)) {
                // Pouze market obchody (BUY/SELL)
                if(OrderType() <= OP_SELL) {
                    // Kontrola, zda byl obchod otevřen po spuštění EA
                    if(OrderOpenTime() >= EAStartTime) {
                        int ticket = OrderTicket();
                        double profit = (OrderProfit() + OrderSwap() + OrderCommission()) / 10.0; // Dělíme 10 pro správnou hodnotu

                        // Kontrola, zda je obchod již v seznamu
                        if(!IsOrderInMaxProfitArray(ticket)) {
                            // Pokud obchod není v seznamu, přidáme ho
                            datetime orderTime = OrderOpenTime();
                            double openPrice = NormalizeDouble(OrderOpenPrice(), Digits); // Normalizace na počet desetinných míst Digits
                            int orderType = OrderType();

                            if(SessionOrderCount >= 5) {
                                // Posun starších obchodů
                                for(int j = 0; j < 4; j++) {
                                    for(int k = 0; k < 5; k++) {
                                        MaxProfit[j][k] = MaxProfit[j+1][k];
                                    }
                                }

                                // Přidání nového obchodu na poslední pozici
                                AddOrderToMaxProfitArray(ticket, orderTime, openPrice, orderType, profit, 4);
                            } else {
                                // Přidání nového obchodu na konec pole
                                AddOrderToMaxProfitArray(ticket, orderTime, openPrice, orderType, profit);
                            }
                        }
                    }
                }
            }
        }
    }
}



//+------------------------------------------------------------------+
//| Funkce pro získání historie obchodů a jejich maximálních profitů |
//+------------------------------------------------------------------+
void UpdateOrderHistory() {
    // Tato funkce je ponechána pro zpětnou kompatibilitu, ale již se nepoužívá
    // pro sledování obchodů od spuštění EA. Místo toho se používá TrackSessionOrders.
    // Ponecháváme ji zde pro případné budoucí využití.
    return;
}

//+------------------------------------------------------------------+
//| Funkce pro kontrolu, zda je aktuální čas v obchodních hodinách   |
//+------------------------------------------------------------------+
bool IsWithinTradeHours() {
    // Pokud není povoleno omezení obchodních hodin, vždy vrátíme true
    if(!UseTradeHours)
        return true;

    // Získání aktuálního času serveru
    datetime serverTime  = TimeCurrent();
    int      currentHour = TimeHour(serverTime);

    // Kontrola, zda je aktuální hodina v povoleném rozmezí
    if(TradeHoursStart <= TradeHoursEnd) {
        // Standardní rozmezí (např. 2-22)
        return (currentHour >= TradeHoursStart && currentHour < TradeHoursEnd);
    } else {
        // Přes půlnoc (např. 22-2)
        return (currentHour >= TradeHoursStart || currentHour < TradeHoursEnd);
    }
}

//+------------------------------------------------------------------+
//| Funkce pro aktualizaci komentáře s podmínkami pro obchod         |
//+------------------------------------------------------------------+
void UpdateTradeConditionsComment(bool zzDistanceCondition, bool bandCondition, bool valueInRange, bool isTradeTimeAllowed) {
    string commentText = "";

    // === Nové obchodní nastavení ===
    commentText += "=== OBCHODNÍ NASTAVENÍ ===\n";
    commentText += StringFormat("D_Max_Loss: %d | Reverse_Loss: %d\n", D_Max_Loss, Reverse_Loss);
    commentText += StringFormat("Trade_Type: %s\n", Trade_Type_Mode == NORMAL ? "NORMAL" : "REVERSE");

    // Dodatečné info o reverzních obchodech
    if(Reverse_Loss == 0) {
        commentText += "Reverse trade: OFF\n";
    } else {
        commentText += StringFormat("Reverse trade: ON after %d same losses\n", Reverse_Loss);
    }
    commentText += StringFormat("TradeNo1: %s | TradeNo2: %s\n", TradeNo1 ? "ON" : "OFF", TradeNo2 ? "ON" : "OFF");
    commentText += StringFormat("MagicNo1: %d | MagicNo2: %d\n", MagicNo1, MagicNo2);
    commentText += StringFormat("TP1_RRR: %.1f | TP2_RRR: %.1f | Reverse_TP: 10.0\n", TP1_RRR, TP2_RRR);
    commentText += StringFormat("Trades_Mg1_Count: %d | Trades_Mg1_Loss: %d\n", Trades_Mg1_Count, Trades_Mg1_Loss);
    commentText += StringFormat("Consecutive_BUY_Loss: %d | Consecutive_SELL_Loss: %d\n", Consecutive_BUY_Loss, Consecutive_SELL_Loss);
    commentText += StringFormat("Reverse_Trade_Pending: %s | Reverse_Trade_Active: %s\n", Reverse_Trade_Pending ? "YES" : "NO", Reverse_Trade_Active ? "YES" : "NO");
    commentText += StringFormat("Dist_D0_High: %.1f | Dist_D0_Low: %.1f | MG2_Limit: %.1f\n", Dist_D0_High, Dist_D0_Low, mg2DistanceLimit);

    // Přidání informace o stavu obchodování
    if(D_Max_Loss > 0 && Trades_Mg1_Loss >= D_Max_Loss) {
        commentText += StringFormat("\nDaily MaxLoss dosaženo: %d. Trading stopped for today!\n\n", D_Max_Loss);
    } else {
        commentText += "\n";
    }

    // Přidání informace o obchodních hodinách
    if(UseTradeHours) {
        commentText += StringFormat("Obchodní hodiny: %s (%d-%d)\n",
                                   isTradeTimeAllowed ? "Povoleno" : "Zakázáno",
                                   TradeHoursStart,
                                   TradeHoursEnd);
    } else {
        commentText += "Obchodní hodiny: Bez omezení\n";
    }

    // Přidání podmínek pro obchod
    commentText += "\nPodmínky pro obchod:\n";

    // 1. ZZ vzdálenost
    double absZZDistance = MathAbs(zzDistanceM5);
    double selectedATRLimit = 0.0;

    switch(MinZZDistForTrade) {
        case ZZ_Green:  // Zelená - největší vzdálenost
            selectedATRLimit = greenATRLimit;
            break;
        case ZZ_Orange: // Oranžová - střední vzdálenost
            selectedATRLimit = orangeATRLimit;
            break;
        case ZZ_Red:    // Červená - nejmenší vzdálenost
            selectedATRLimit = redATRLimit;
            break;
    }

    commentText += StringFormat("ZZ vzdálenost (%.1f >= %.1f) - %s\n",
                               absZZDistance,
                               selectedATRLimit,
                               zzDistanceCondition ? "OK" : "NOT OK");

    // 2. BandCheck
    if(BandCheck) {
        double lastClose = Close[1];
        if(zzDistanceM5 < 0) { // BUY
            commentText += StringFormat("BandCheck (%.2f < %.2f) - %s\n",
                                      lastClose,
                                      LowerEnvelope,
                                      bandCondition ? "OK" : "NOT OK");
        } else { // SELL
            commentText += StringFormat("BandCheck (%.2f > %.2f) - %s\n",
                                      lastClose,
                                      UpperEnvelope,
                                      bandCondition ? "OK" : "NOT OK");
        }
    } else {
        commentText += StringFormat("BandCheck (vypnuto) - %s\n", bandCondition ? "OK" : "NOT OK");
    }

    // 3. Hodnota v rozsahu
    commentText += StringFormat("Hodnota v rozsahu (|%.2f| <= %.2f) - %s\n",
                               LastNearestDistance,
                               HighlightDistance,
                               valueInRange ? "OK" : "NOT OK");

    // 4. MA100 vzdálenost na M5
    if(MA100_dist) {
        bool ma100DistanceCondition = (ema100_dist_abs > greenATRLimit);

        commentText += StringFormat("MA100 M5 vzdálenost (%.2f > %.2f) - %s\n",
                                   ema100_dist_abs,
                                   greenATRLimit,
                                   ma100DistanceCondition ? "OK" : "NOT OK");
    } else {
        commentText += "MA100 M5 vzdálenost (vypnuto) - OK\n";
    }

    // 5. MA100 vzdálenost na M1
    if(MA100_1_dist) {
        bool ma100_1_DistanceCondition = (ema100_1_dist_abs > orangeATRLimit);

        commentText += StringFormat("MA100 M1 vzdálenost (%.2f > %.2f) - %s\n",
                                   ema100_1_dist_abs,
                                   orangeATRLimit,
                                   ma100_1_DistanceCondition ? "OK" : "NOT OK");
    } else {
        commentText += "MA100 M1 vzdálenost (vypnuto) - OK\n";
    }

    // 6. MA100 znaménka M5 a M1
    if(MA100_dist && MA100_1_dist) {
        bool m5Positive = (ema100_dist >= 0);
        bool m1Positive = (ema100_1_dist >= 0);
        bool ma100SameSignCondition = (m5Positive == m1Positive);

        string m5Sign = m5Positive ? "+" : "-";
        string m1Sign = m1Positive ? "+" : "-";

        commentText += StringFormat("MA100 znaménka M5(%s) M1(%s) - %s\n",
                                   m5Sign,
                                   m1Sign,
                                   ma100SameSignCondition ? "OK" : "NOT OK");
    } else {
        commentText += "MA100 znaménka (vypnuto) - OK\n";
    }

    // 5. Obchodní hodiny
    if(UseTradeHours) {
        commentText += StringFormat("Obchodní hodiny (%d-%d) - %s\n",
                                   TradeHoursStart,
                                   TradeHoursEnd,
                                   isTradeTimeAllowed ? "OK" : "NOT OK");
    } else {
        commentText += "Obchodní hodiny (vypnuto) - OK\n";
    }

    // 7. MG2 vzdálenostní podmínka
    if(TradeNo2) {
        bool mg2DistanceCondition = false;
        string mg2ConditionText = "";

        if(zzDistanceM5 < 0) { // BUY signál
            mg2DistanceCondition = (Dist_D0_High >= mg2DistanceLimit);
            mg2ConditionText = StringFormat("MG2 BUY vzdálenost (%.1f >= %.1f) - %s\n",
                                          Dist_D0_High,
                                          mg2DistanceLimit,
                                          mg2DistanceCondition ? "OK" : "NOT OK");
        } else if(zzDistanceM5 > 0) { // SELL signál
            mg2DistanceCondition = (Dist_D0_Low >= mg2DistanceLimit);
            mg2ConditionText = StringFormat("MG2 SELL vzdálenost (%.1f >= %.1f) - %s\n",
                                          Dist_D0_Low,
                                          mg2DistanceLimit,
                                          mg2DistanceCondition ? "OK" : "NOT OK");
        } else { // Žádný signál
            mg2ConditionText = "MG2 vzdálenost (žádný signál) - NOT OK\n";
        }

        commentText += mg2ConditionText;
    } else {
        commentText += "MG2 vzdálenost (TradeNo2 vypnuto) - NOT OK\n";
    }

    // Přidání informace o směru obchodu a nejbližší úrovni
    if(valueInRange) {
        // Získání nejbližší hodnoty z globálních proměnných
        double nearestLevel = 0.0;
        string levelType = "";
        string timeframe = "";

        if(lastBestRow != -1 && lastBestCol != -1) {
            if(lastTableType == 0) {  // MA tabulka
                nearestLevel = MA_Values[lastBestRow][lastBestCol];
                levelType = MA_labels_X[lastBestRow].text;  // Např. "EMA100"
                timeframe = MA_labels_Y[lastBestCol];       // Např. "H1"
            } else if(lastTableType == 1) {  // D tabulka
                nearestLevel = D_Values[lastBestRow][lastBestCol];
                levelType = D_labels_X[lastBestRow].text;   // Např. "High"
                timeframe = D_labels_Y[lastBestCol];        // Např. "D1"
            } else if(lastTableType == 2) {  // ZZ tabulka
                nearestLevel = ZZ_Table_Values[lastBestRow][lastBestCol];
                levelType = ZZ_labels_X[lastBestRow].text;  // Např. "zzHigh"
                timeframe = ZZ_labels_Y[lastBestCol];       // Např. "M5"
            }
        }

        string direction = (zzDistanceM5 < 0) ? "BUY" : "SELL";
        commentText += StringFormat("\nHledám %s obchod\n", direction);
        commentText += StringFormat("Nejbližší úroveň: %.2f (%s %s)\n", nearestLevel, levelType, timeframe);
    }

    // Přidání informací o obchodech od spuštění EA
    commentText += StringFormat("\nMaxProfity obchodů s Magic Number %d/%d od spuštění EA:\n", MagicNo1, MagicNo2);

    if(SessionOrderCount > 0) {
        // Máme nějaké obchody od spuštění EA
        for(int i = 0; i < SessionOrderCount; i++) {
            // Formátování profitu v bodech (s jedním desetinným místem, protože dělíme 100)
            string profitText = DoubleToString(MaxProfit[i][MP_MAX_PROFIT], 1);

            // Formátování data a času ve formátu DD.MM.RRRR HH:MM
            datetime orderTime = (datetime)MaxProfit[i][MP_TIME];
            string dateStr = StringFormat("%02d.%02d.%04d %02d:%02d",
                                         TimeDay(orderTime),
                                         TimeMonth(orderTime),
                                         TimeYear(orderTime),
                                         TimeHour(orderTime),
                                         TimeMinute(orderTime));

            // Získání otevírací ceny a typu obchodu
            double openPrice = MaxProfit[i][MP_OPEN_PRICE];
            int orderType = (int)MaxProfit[i][MP_TYPE];
            string typeStr = (orderType == OP_BUY) ? "BUY" : "SELL";

            // Přidání informace o obchodu ve formátu "číslo obchodu - Datum a čas otevření - Typ obchodu - OpenPrice - MaxProfit v bodech"
            commentText += StringFormat("#%d - %s - %s - %s - MaxProfit: %s bodů\n",
                                       (int)MaxProfit[i][MP_TICKET],
                                       dateStr,
                                       typeStr,
                                       DoubleToString(openPrice, Digits), // Formátování otevírací ceny na správný počet desetinných míst
                                       profitText);
        }
    } else {
        // Nemáme žádné obchody od spuštění EA, zobrazíme nulové hodnoty
        string zeroText = DoubleToString(0.0, Digits); // Formátování nuly podle počtu desetinných míst instrumentu
        commentText += "Žádné obchody - " + zeroText + "b\n";
    }

    // Zobrazení komentáře
    SafeComment(commentText);
}

//+------------------------------------------------------------------+
//| Funkce pro aktualizaci textu "Searching for..." s aktuální LD    |
//+------------------------------------------------------------------+
void UpdateSearchingText() {
    string orderSearchLabelName = EA_Tag + "_ORDER_SEARCH_INFO";
    string nlLabelName          = EA_Tag + "_NL_INFO";
    string orderSearchText      = "";
    string nlText               = "";

    // Získání nejbližší hodnoty z globálních proměnných
    double nearestLevel = 0.0;
    string levelType    = "";
    string timeframe    = "";
    color  levelColor   = clrWhite;   // Výchozí barva

    if(lastBestRow != -1 && lastBestCol != -1) {
        if(lastTableType == 0) {  // MA tabulka
            nearestLevel = MA_Values[lastBestRow][lastBestCol];
            levelType    = MA_labels_X[lastBestRow].text;   // Např. "EMA100"
            timeframe    = MA_labels_Y[lastBestCol];        // Např. "H1"
            levelColor   = MA_labels_X[lastBestRow].clr;    // Barva odpovídající danému MA
        } else if(lastTableType == 1) {  // D tabulka
            nearestLevel = D_Values[lastBestRow][lastBestCol];
            levelType    = D_labels_X[lastBestRow].text;   // Např. "High"
            timeframe    = D_labels_Y[lastBestCol];        // Např. "D1"
            levelColor   = D_labels_X[lastBestRow].clr;    // Barva odpovídající danému D levelu
        } else if(lastTableType == 2) {  // ZZ tabulka
            nearestLevel = ZZ_Table_Values[lastBestRow][lastBestCol];
            levelType    = ZZ_labels_X[lastBestRow].text;   // Např. "zzHigh"
            timeframe    = ZZ_labels_Y[lastBestCol];        // Např. "M5"
            levelColor   = ZZ_labels_X[lastBestRow].clr;    // Barva odpovídající danému ZZ levelu
        }

        // Aktualizace globální proměnné s informací o nejbližším levelu
        NearestLevelInfo = levelType + "-" + timeframe;
    }

    // Formátování hodnoty LD pro zobrazení
    string ldText = "";
    if(lastBestRow != -1 && lastBestCol != -1) {
        string sign = LastNearestDistance > 0 ? "+" : "";
        ldText      = ", LD: " + sign + DoubleToString(LastNearestDistance, Digits) + " b";
    }

    // Určení typu příkazu podle znaménka ZZ vzdálenosti
    if(zzDistanceM5 < 0) {
        orderSearchText = "Hledám BUY order, ";
        nlText          = StringFormat("NL: %." + IntegerToString(Digits) + "f, %s %s%s",
                                       nearestLevel, levelType, timeframe, ldText);
    } else if(zzDistanceM5 > 0) {
        orderSearchText = "Hledám SELL order, ";
        nlText          = StringFormat("NL: %." + IntegerToString(Digits) + "f, %s %s%s",
                                       nearestLevel, levelType, timeframe, ldText);
    }

    // Určení, zda je ZZ vzdálenost dostatečná pro obchodování
    double absZZDistance    = MathAbs(zzDistanceM5);
    double selectedATRLimit = 0.0;

    switch(MinZZDistForTrade) {
        case ZZ_Green:   // Zelená - největší vzdálenost
            selectedATRLimit = greenATRLimit;
            break;
        case ZZ_Orange:   // Oranžová - střední vzdálenost
            selectedATRLimit = orangeATRLimit;
            break;
        case ZZ_Red:   // Červená - nejmenší vzdálenost
            selectedATRLimit = redATRLimit;
            break;
    }

    bool zzDistanceCondition = (absZZDistance > selectedATRLimit);

    // Vykreslení textu o hledání obchodu
    if(orderSearchText != "") {
        // Získání fontu a velikosti z globálních proměnných
        string fontSmall     = "Arial";
        int    fontSizeSmall = 10;
        int    corner        = SelectedCorner;
        int    offsetX       = 250;
        int    offsetY       = 20;
        int    fontSizeMain  = 18;

        // Barva pro hlavní text (Searching for...)
        color mainTextColor;

        if(zzDistanceCondition) {
            // Pokud je ZZ vzdálenost dostatečná, použijeme bílou barvu pro hlavní text
            mainTextColor = clrWhite;
        } else {
            // Pokud podmínka není splněna, použijeme šedou barvu
            mainTextColor = clrDarkGray;
        }

        // Barva pro NL text - vždy barva levelu, bez ohledu na splnění podmínky ZZ vzdálenosti
        color nlTextColor = levelColor;

        // Odstranění případného obdélníku na pozadí, pokud existuje
        string bgLabelName = EA_Tag + "_ORDER_SEARCH_BG";
        if(ObjectFind(0, bgLabelName) >= 0) {
            ObjectDelete(0, bgLabelName);
        }

        // Aktualizace nebo vytvoření hlavního textového objektu (Searching for...)
        if(ObjectFind(0, orderSearchLabelName) >= 0) {
            ObjectSetText(orderSearchLabelName, orderSearchText, fontSizeSmall, fontSmall, mainTextColor);
        } else {
            ObjectCreate(orderSearchLabelName, OBJ_LABEL, 0, 0, 0);
            ObjectSetText(orderSearchLabelName, orderSearchText, fontSizeSmall, fontSmall, mainTextColor);
            ObjectSet(orderSearchLabelName, OBJPROP_CORNER, corner);
            ObjectSet(orderSearchLabelName, OBJPROP_XDISTANCE, offsetX);
            ObjectSet(orderSearchLabelName, OBJPROP_YDISTANCE, offsetY + fontSizeMain + 8 + fontSizeSmall + 10);
        }

        // Výpočet šířky hlavního textu pro umístění NL textu
        int textWidth = StringLen(orderSearchText) * 15 - 10;   // Přibližná šířka textu (6 pixelů na znak)

        // Aktualizace nebo vytvoření NL textového objektu
        // Pokud je splněna podmínka ZZ vzdálenosti, text bude blikat
        color finalNlTextColor = nlTextColor;
        if(zzDistanceCondition) {
            // Použijeme blikání mezi barvou levelu a bílou barvou
            finalNlTextColor = blinkState ? nlTextColor : clrWhite;
        }

        if(ObjectFind(0, nlLabelName) >= 0) {
            ObjectSetText(nlLabelName, nlText, fontSizeSmall, fontSmall, finalNlTextColor);
            // Aktualizace pozice
            ObjectSet(nlLabelName, OBJPROP_XDISTANCE, offsetX - textWidth + 30);
        } else {
            ObjectCreate(nlLabelName, OBJ_LABEL, 0, 0, 0);
            ObjectSetText(nlLabelName, nlText, fontSizeSmall, fontSmall, finalNlTextColor);
            ObjectSet(nlLabelName, OBJPROP_CORNER, corner);
            ObjectSet(nlLabelName, OBJPROP_XDISTANCE, offsetX - textWidth + 30);
            ObjectSet(nlLabelName, OBJPROP_YDISTANCE, offsetY + fontSizeMain + 8 + fontSizeSmall + 10);
        }
    }
}

//+------------------------------------------------------------------+
//| Aktualizuje vzdálenosti od D0 High/Low                          |
//+------------------------------------------------------------------+
void UpdateD0Distances() {
    // Získání aktuální ceny Close[1]
    double currentClose = Close[1];

    // Výpočet vzdáleností od D0 High a Low (D_Values[0][0] = D0 High, D_Values[1][0] = D0 Low)
    Dist_D0_High = D_Values[0][0] - currentClose;  // Vzdálenost Close(1) od D0 High
    Dist_D0_Low = currentClose - D_Values[1][0];   // Vzdálenost Close(1) od D0 Low

    if(DebugMode && !StrategyTester) {
        Print("D0 Distances updated - High: ", Dist_D0_High, ", Low: ", Dist_D0_Low);
    }
}

// UpdateZZTable funkce odstraněna - data se ukládají přímo v ZigZagInfo funkci

//+------------------------------------------------------------------+
//| Kontroluje podmínky pro obchodování s MagicNo1 POUZE            |
//+------------------------------------------------------------------+
bool CheckTradingConditions(bool &allowNormalTrade, bool &allowReverseTrade) {
    allowNormalTrade = false;
    allowReverseTrade = false;

    // Pokud je TradeNo1 vypnuté, nepovolujeme žádné obchody s MagicNo1
    if(!TradeNo1) {
        return false;
    }

    // PRIORITNÍ KONTROLA: D_Max_Loss - při dosažení se zastaví VŠE
    if(D_Max_Loss > 0 && Trades_Mg1_Loss >= D_Max_Loss) {
        // Dosažen limit ztrát - ŽÁDNÉ obchody MG1 pro zbytek dne
        if(DebugMode && !StrategyTester) {
            Print("Daily MaxLoss dosaženo: ", D_Max_Loss, ". Trading stopped for today!");
        }
        return false;
    }

    // Kontrola Reverse_Loss logiky - POUZE PRO MG1
    // Pokud je Reverse_Trade_Pending = true, MUSÍ se provést reverzní obchod MG1
    if(Reverse_Loss > 0 && Reverse_Trade_Pending) {
        allowNormalTrade = false;
        allowReverseTrade = true;  // Povol reverzní obchod MG1

        if(DebugMode && !StrategyTester) {
            Print("CheckTradingConditions Debug - REVERZNÍ OBCHOD MG1 AKTIVNÍ - Reverse_Trade_Pending = TRUE");
        }

        return true;
    }

    // Standardní logika pro obchodování MG1
    if(D_Max_Loss == 0 || Trades_Mg1_Loss < D_Max_Loss) {
        // Počet ztrát je v limitu nebo není kontrolován
        allowNormalTrade = (Trade_Type_Mode == NORMAL);
        allowReverseTrade = (Trade_Type_Mode == REVERSE);

        if(DebugMode && !StrategyTester) {
            Print("CheckTradingConditions Debug - D_Max_Loss: ", D_Max_Loss, ", Trades_Mg1_Loss: ", Trades_Mg1_Loss);
            Print("CheckTradingConditions Debug - Trade_Type_Mode: ", Trade_Type_Mode == NORMAL ? "NORMAL" : "REVERSE");
            Print("CheckTradingConditions Debug - allowNormalTrade: ", allowNormalTrade ? "YES" : "NO");
            Print("CheckTradingConditions Debug - allowReverseTrade: ", allowReverseTrade ? "YES" : "NO");
            Print("CheckTradingConditions Debug - MG2 má nyní vlastní nezávislou logiku");
        }
    } else {
        if(DebugMode && !StrategyTester) {
            Print("CheckTradingConditions Debug - D_Max_Loss dosažen: ", Trades_Mg1_Loss, " >= ", D_Max_Loss);
        }
    }

    return (allowNormalTrade || allowReverseTrade);
}

//+------------------------------------------------------------------+
//| Kontroluje podmínky pro obchodování s MagicNo2 NEZÁVISLE        |
//+------------------------------------------------------------------+
bool CheckTradingConditionsMG2() {
    // MG2 je nezávislé na MG1, ale respektuje některé globální limity

    // Základní kontrola - musí být zapnuté
    if(!TradeNo2) {
        if(DebugMode && !StrategyTester) {
            Print("CheckTradingConditionsMG2 Debug - TradeNo2 je vypnuté");
        }
        return false;
    }

    // Respektuje D_Max_Loss (globální denní limit)
    if(D_Max_Loss > 0 && Trades_Mg1_Loss >= D_Max_Loss) {
        if(DebugMode && !StrategyTester) {
            Print("CheckTradingConditionsMG2 Debug - D_Max_Loss dosažen: ", Trades_Mg1_Loss, " >= ", D_Max_Loss);
        }
        return false;
    }

    // MG2 se neblokuje reverzními obchody MG1 - je nezávislé
    if(DebugMode && !StrategyTester) {
        Print("CheckTradingConditionsMG2 Debug - MG2 podmínky splněny");
    }

    return true;
}

//+------------------------------------------------------------------+
//| Provede obchod podle zadaných parametrů                         |
//+------------------------------------------------------------------+
void ExecuteTrade(bool isBuySignal, bool isSellSignal, double bestValue, double currentBid, double currentAsk,
                  double stopLoss, double takeProfit, int magicNumber, string prefix, bool isReverseTradeMode = false) {

    bool tradeExecuted = false;

    if(isBuySignal) {
        // BUY obchod
        if(isReverseTradeMode) {
            // REVERZNÍ BUY obchod - vždy jako BUY LIMIT s posunutou cenou
            double reverseEntryPrice = NormalizeDouble(currentAsk - stopLoss, Digits);  // Vstupní cena = Ask - SL (níže)

            // Určení správných proměnných podle magic number
            int currentBuyTicket = (magicNumber == MagicNo1) ? pendingBuyTicket : pendingBuyTicket_MG2;
            double currentBuyPrice = (magicNumber == MagicNo1) ? pendingBuyPrice : pendingBuyPrice_MG2;

            if(!IsPendingOrderValidWithMagic(currentBuyTicket, magicNumber) || MathAbs(currentBuyPrice - reverseEntryPrice) > Point * 10) {
                int ticket = OpenOrder(OP_BUYLIMIT, reverseEntryPrice, stopLoss, takeProfit, 0.0, "", magicNumber);
                if(ticket > 0) {
                    tradeExecuted = true;
                    if(magicNumber == MagicNo1) {
                        pendingBuyTicket = ticket;
                        pendingBuyPrice = reverseEntryPrice;
                    } else if(magicNumber == MagicNo2) {
                        pendingBuyTicket_MG2 = ticket;
                        pendingBuyPrice_MG2 = reverseEntryPrice;
                    }
                    // Výpočet skutečných SL a TP cen pro výpis
                    double actualSL = NormalizeDouble(reverseEntryPrice - stopLoss, Digits);
                    double actualTP = NormalizeDouble(reverseEntryPrice + takeProfit, Digits);
                    if(!StrategyTester) Print(prefix, " - Umístěn REVERZNÍ BUY LIMIT pokyn na úrovni: ", reverseEntryPrice,
                                              ", SL: ", actualSL, ", TP: ", actualTP, ", Magic: ", magicNumber);
                }
            }
        } else {
            // STANDARDNÍ BUY obchod - podle OrderType nastavení
            bool useMarket = false;
            bool useLimit = false;

            // Rozhodnutí o typu obchodu podle OrderType
            if(OrderType == MARKET_ONLY) {
                useMarket = true;
            } else if(OrderType == LIMIT_ONLY) {
                useLimit = true;
            } else { // MARKET_LIMIT - podle pozice ceny
                if(currentAsk < bestValue) {
                    useMarket = true;
                } else {
                    useLimit = true;
                }
            }

            if(useMarket) {
                // Market BUY
                if(!PositionExistsWithMagic(OP_BUY, magicNumber)) {
                    double normalizedAsk = NormalizeDouble(currentAsk, Digits);
                    int ticket = OpenOrder(OP_BUY, normalizedAsk, stopLoss, takeProfit, 0.0, "", magicNumber);
                    if(ticket > 0) {
                        tradeExecuted = true;
                        if(!StrategyTester) Print(prefix, " - Otevřen market BUY obchod, cena: ", normalizedAsk, ", Magic: ", magicNumber);
                    }
                }
            } else if(useLimit) {
                // Limit BUY
                // Určení správných proměnných podle magic number
                int currentBuyTicket = (magicNumber == MagicNo1) ? pendingBuyTicket : pendingBuyTicket_MG2;
                double currentBuyPrice = (magicNumber == MagicNo1) ? pendingBuyPrice : pendingBuyPrice_MG2;

                double limitPrice = (OrderType == LIMIT_ONLY) ? currentAsk : bestValue;  // Pro LIMIT_ONLY použij aktuální cenu

                if(!IsPendingOrderValidWithMagic(currentBuyTicket, magicNumber) || MathAbs(currentBuyPrice - limitPrice) > Point * 10) {
                    double normalizedPrice = NormalizeDouble(limitPrice, Digits);
                    int ticket = OpenOrder(OP_BUYLIMIT, normalizedPrice, stopLoss, takeProfit, 0.0, "", magicNumber);
                    if(ticket > 0) {
                        tradeExecuted = true;
                        if(magicNumber == MagicNo1) {
                            pendingBuyTicket = ticket;
                            pendingBuyPrice = normalizedPrice;
                        } else if(magicNumber == MagicNo2) {
                            pendingBuyTicket_MG2 = ticket;
                            pendingBuyPrice_MG2 = normalizedPrice;
                        }
                        if(!StrategyTester) Print(prefix, " - Umístěn BUY LIMIT pokyn na úrovni: ", normalizedPrice, ", Magic: ", magicNumber);
                    }
                }
            }
        }
    } else if(isSellSignal) {
        // SELL obchod
        if(isReverseTradeMode) {
            // REVERZNÍ SELL obchod - vždy jako SELL LIMIT s posunutou cenou
            double reverseEntryPrice = NormalizeDouble(currentBid + stopLoss, Digits);  // Vstupní cena = Bid + SL (výše)

            // Určení správných proměnných podle magic number
            int currentSellTicket = (magicNumber == MagicNo1) ? pendingSellTicket : pendingSellTicket_MG2;
            double currentSellPrice = (magicNumber == MagicNo1) ? pendingSellPrice : pendingSellPrice_MG2;

            if(!IsPendingOrderValidWithMagic(currentSellTicket, magicNumber) || MathAbs(currentSellPrice - reverseEntryPrice) > Point * 10) {
                int ticket = OpenOrder(OP_SELLLIMIT, reverseEntryPrice, stopLoss, takeProfit, 0.0, "", magicNumber);
                if(ticket > 0) {
                    tradeExecuted = true;
                    if(magicNumber == MagicNo1) {
                        pendingSellTicket = ticket;
                        pendingSellPrice = reverseEntryPrice;
                    } else if(magicNumber == MagicNo2) {
                        pendingSellTicket_MG2 = ticket;
                        pendingSellPrice_MG2 = reverseEntryPrice;
                    }
                    // Výpočet skutečných SL a TP cen pro výpis
                    double actualSL = NormalizeDouble(reverseEntryPrice + stopLoss, Digits);
                    double actualTP = NormalizeDouble(reverseEntryPrice - takeProfit, Digits);
                    if(!StrategyTester) Print(prefix, " - Umístěn REVERZNÍ SELL LIMIT pokyn na úrovni: ", reverseEntryPrice,
                                              ", SL: ", actualSL, ", TP: ", actualTP, ", Magic: ", magicNumber);
                }
            }
        } else {
            // STANDARDNÍ SELL obchod - podle OrderType nastavení
            bool useMarket = false;
            bool useLimit = false;

            // Rozhodnutí o typu obchodu podle OrderType
            if(OrderType == MARKET_ONLY) {
                useMarket = true;
            } else if(OrderType == LIMIT_ONLY) {
                useLimit = true;
            } else { // MARKET_LIMIT - podle pozice ceny
                if(currentBid > bestValue) {
                    useMarket = true;
                } else {
                    useLimit = true;
                }
            }

            if(useMarket) {
                // Market SELL
                if(!PositionExistsWithMagic(OP_SELL, magicNumber)) {
                    double normalizedBid = NormalizeDouble(currentBid, Digits);
                    int ticket = OpenOrder(OP_SELL, normalizedBid, stopLoss, takeProfit, 0.0, "", magicNumber);
                    if(ticket > 0) {
                        tradeExecuted = true;
                        if(!StrategyTester) Print(prefix, " - Otevřen market SELL obchod, cena: ", normalizedBid, ", Magic: ", magicNumber);
                    }
                }
            } else if(useLimit) {
                // Limit SELL
                // Určení správných proměnných podle magic number
                int currentSellTicket = (magicNumber == MagicNo1) ? pendingSellTicket : pendingSellTicket_MG2;
                double currentSellPrice = (magicNumber == MagicNo1) ? pendingSellPrice : pendingSellPrice_MG2;

                double limitPrice = (OrderType == LIMIT_ONLY) ? currentBid : bestValue;  // Pro LIMIT_ONLY použij aktuální cenu

                if(!IsPendingOrderValidWithMagic(currentSellTicket, magicNumber) || MathAbs(currentSellPrice - limitPrice) > Point * 10) {
                    double normalizedPrice = NormalizeDouble(limitPrice, Digits);
                    int ticket = OpenOrder(OP_SELLLIMIT, normalizedPrice, stopLoss, takeProfit, 0.0, "", magicNumber);
                    if(ticket > 0) {
                        tradeExecuted = true;
                        if(magicNumber == MagicNo1) {
                            pendingSellTicket = ticket;
                            pendingSellPrice = normalizedPrice;
                        } else if(magicNumber == MagicNo2) {
                            pendingSellTicket_MG2 = ticket;
                            pendingSellPrice_MG2 = normalizedPrice;
                        }
                        if(!StrategyTester) Print(prefix, " - Umístěn SELL LIMIT pokyn na úrovni: ", normalizedPrice, ", Magic: ", magicNumber);
                    }
                }
            }
        }
    }

    // Reset Reverse_Trade_Pending po provedení reverzního obchodu s MagicNo1
    if(tradeExecuted && magicNumber == MagicNo1 && Reverse_Trade_Pending) {
        Reverse_Trade_Pending = false;
        Reverse_Trade_Active = true;  // Označit, že je aktivní reverzní obchod
        if(DebugMode && !StrategyTester) {
            Print("Reverse_Trade_Pending resetován na FALSE - reverzní obchod proveden s TP=10*SL, Reverse_Trade_Active = TRUE");
        }
    }
}

//+------------------------------------------------------------------+
//| Kontroluje pozice s konkrétním Magic Number                     |
//+------------------------------------------------------------------+
bool PositionExistsWithMagic(ENUM_ORDER_TYPE type, int magicNumber) {
    bool isBuyDirection = (type == OP_BUY || type == OP_BUYLIMIT || type == OP_BUYSTOP);

    for(int i = 0; i < OrdersTotal(); i++) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            if(OrderMagicNumber() == magicNumber && OrderSymbol() == _Symbol) {
                int orderType = OrderType();
                bool isOrderBuyDirection = (orderType == OP_BUY || orderType == OP_BUYLIMIT || orderType == OP_BUYSTOP);

                if(isBuyDirection == isOrderBuyDirection)
                    return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Kontroluje platnost čekajícího pokynu s konkrétním Magic Number |
//+------------------------------------------------------------------+
bool IsPendingOrderValidWithMagic(int ticket, int magicNumber) {
    if(ticket <= 0) return false;

    if(OrderSelect(ticket, SELECT_BY_TICKET)) {
        if(OrderMagicNumber() == magicNumber && OrderSymbol() == _Symbol) {
            int orderType = OrderType();
            if(orderType == OP_BUYLIMIT || orderType == OP_BUYSTOP ||
               orderType == OP_SELLLIMIT || orderType == OP_SELLSTOP) {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Aktualizuje počítadla obchodů pro aktuální den                  |
//+------------------------------------------------------------------+
void UpdateDailyTradeCounters() {
    static datetime lastCheckDate = 0;
    datetime currentDate = iTime(_Symbol, PERIOD_D1, 0);

    // Pokud je nový den, znovu inicializujeme z historie
    if(lastCheckDate != currentDate) {
        lastCheckDate = currentDate;

        if(DebugMode && !StrategyTester) {
            Print("Nový den - reinicializace počítadel z historie");
        }

        // Reinicializace z historie pro nový den
        InitializeTradingCountersFromHistory();
        return;  // Ukončíme funkci, protože InitializeTradingCountersFromHistory už vše spočítala
    }

    // Spočítáme obchody s MagicNo1 pro aktuální den a analyzujeme po sobě jdoucí ztráty
    int totalTrades = 0;
    int lossTrades = 0;
    int consecutiveBuyLoss = 0;
    int consecutiveSellLoss = 0;
    int lastTradeType = -1;  // -1 = neznámý, 0 = BUY, 1 = SELL

    // Vytvoříme pole pro chronologické řazení obchodů
    datetime tradeTimes[];
    int tradeTypes[];
    double tradeProfits[];
    int tradeCount = 0;

    // Nejprve sesbíráme všechny dnešní PROVEDENÉ obchody (ne zrušené pokyny)
    for(int i = 0; i < OrdersHistoryTotal(); i++) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_HISTORY)) {
            if(OrderSymbol() == _Symbol && OrderMagicNumber() == MagicNo1) {
                datetime closeTime = OrderCloseTime();
                if(iTime(_Symbol, PERIOD_D1, 0) <= closeTime && closeTime < iTime(_Symbol, PERIOD_D1, 0) + 86400) {
                    // Kontrola, zda se jedná o provedený obchod (ne zrušený pokyn)
                    int orderType = OrderType();

                    // Počítáme pouze market obchody (BUY/SELL) které byly skutečně provedeny
                    if((orderType == OP_BUY || orderType == OP_SELL) && OrderCloseTime() > 0) {
                        // Dodatečná kontrola - obchod musí mít nějakou dobu trvání
                        if(OrderCloseTime() > OrderOpenTime()) {
                            ArrayResize(tradeTimes, tradeCount + 1);
                            ArrayResize(tradeTypes, tradeCount + 1);
                            ArrayResize(tradeProfits, tradeCount + 1);

                            tradeTimes[tradeCount] = OrderOpenTime();
                            tradeTypes[tradeCount] = orderType;  // 0=BUY, 1=SELL
                            tradeProfits[tradeCount] = OrderProfit();
                            tradeCount++;
                        }
                    }
                }
            }
        }
    }

    // Seřadíme obchody podle času otevření (chronologicky)
    for(int i = 0; i < tradeCount - 1; i++) {
        for(int j = i + 1; j < tradeCount; j++) {
            if(tradeTimes[i] > tradeTimes[j]) {
                // Swap times
                datetime tempTime = tradeTimes[i];
                tradeTimes[i] = tradeTimes[j];
                tradeTimes[j] = tempTime;

                // Swap types
                int tempType = tradeTypes[i];
                tradeTypes[i] = tradeTypes[j];
                tradeTypes[j] = tempType;

                // Swap profits
                double tempProfit = tradeProfits[i];
                tradeProfits[i] = tradeProfits[j];
                tradeProfits[j] = tempProfit;
            }
        }
    }

    // Analyzujeme po sobě jdoucí ztráty ve stejném směru
    for(int i = 0; i < tradeCount; i++) {
        totalTrades++;

        if(tradeProfits[i] < 0) {
            lossTrades++;

            // Kontrola po sobě jdoucích ztrát ve stejném směru
            if(tradeTypes[i] == 0) {  // BUY obchod
                if(lastTradeType == 0) {  // Předchozí byl také BUY loss
                    consecutiveBuyLoss++;
                } else {
                    consecutiveBuyLoss = 1;  // První BUY loss v sérii
                    consecutiveSellLoss = 0; // Reset SELL série
                }
                lastTradeType = 0;
            } else if(tradeTypes[i] == 1) {  // SELL obchod
                if(lastTradeType == 1) {  // Předchozí byl také SELL loss
                    consecutiveSellLoss++;
                } else {
                    consecutiveSellLoss = 1;  // První SELL loss v sérii
                    consecutiveBuyLoss = 0;  // Reset BUY série
                }
                lastTradeType = 1;
            }
        } else {
            // Ziskový obchod přeruší sérii ztrát
            consecutiveBuyLoss = 0;
            consecutiveSellLoss = 0;
            lastTradeType = -1;
        }
    }

    // Aktualizace globálních proměnných
    Trades_Mg1_Count = totalTrades;
    Trades_Mg1_Loss = lossTrades;
    Consecutive_BUY_Loss = consecutiveBuyLoss;
    Consecutive_SELL_Loss = consecutiveSellLoss;

    // Kontrola Reverse_Loss logiky - nyní na základě po sobě jdoucích ztrát ve stejném směru
    // FUNGUJE PRO OBA MÓDY (NORMAL i REVERSE)
    if(Reverse_Loss > 0) {
        bool consecutiveLossCondition = (Consecutive_BUY_Loss >= Reverse_Loss || Consecutive_SELL_Loss >= Reverse_Loss);

        if(consecutiveLossCondition && !Reverse_Trade_Pending) {
            // Kontrola, zda je ještě prostor pro reverzní obchod (před D_Max_Loss)
            if(D_Max_Loss == 0 || Trades_Mg1_Loss < D_Max_Loss) {
                Reverse_Trade_Pending = true;
                string lossDirection = (Consecutive_BUY_Loss >= Reverse_Loss) ? "BUY" : "SELL";
                if(DebugMode && !StrategyTester) {
                    Print("Reverse_Trade_Pending nastaven na TRUE - dosaženo ", Reverse_Loss, " po sobě jdoucích ", lossDirection, " ztrát");
                }
            }
        }
    } else {
        // Reset když je Reverse_Loss = 0
        Reverse_Trade_Pending = false;
    }
}

//+------------------------------------------------------------------+
//| Inicializuje počítadla obchodů z historie při spuštění EA       |
//+------------------------------------------------------------------+
void InitializeTradingCountersFromHistory() {
    // Reset všech počítadel
    Trades_Mg1_Count = 0;
    Trades_Mg1_Loss = 0;
    Consecutive_BUY_Loss = 0;
    Consecutive_SELL_Loss = 0;
    Reverse_Trade_Pending = false;
    Reverse_Trade_Active = false;

    datetime todayStart = iTime(_Symbol, PERIOD_D1, 0);
    datetime todayEnd = todayStart + 86400;

    // Vytvoříme pole pro chronologické řazení obchodů
    datetime tradeTimes[];
    int tradeTypes[];
    double tradeProfits[];
    int tradeCount = 0;

    // Sesbíráme všechny dnešní PROVEDENÉ obchody s MagicNo1 (ne zrušené pokyny)
    for(int i = 0; i < OrdersHistoryTotal(); i++) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_HISTORY)) {
            if(OrderSymbol() == _Symbol && OrderMagicNumber() == MagicNo1) {
                datetime closeTime = OrderCloseTime();

                // Kontrola, zda byl obchod uzavřen dnes
                if(todayStart <= closeTime && closeTime < todayEnd) {
                    // Kontrola, zda se jedná o provedený obchod (ne zrušený pokyn)
                    // Zrušené pokyny mají obvykle OrderProfit() == 0 a OrderCloseTime() == OrderOpenTime()
                    // nebo jsou to pending orders které byly zrušeny
                    int orderType = OrderType();

                    // Počítáme pouze market obchody (BUY/SELL) které byly skutečně provedeny
                    if((orderType == OP_BUY || orderType == OP_SELL) && OrderCloseTime() > 0) {
                        // Dodatečná kontrola - obchod musí mít nějakou dobu trvání
                        if(OrderCloseTime() > OrderOpenTime()) {
                            ArrayResize(tradeTimes, tradeCount + 1);
                            ArrayResize(tradeTypes, tradeCount + 1);
                            ArrayResize(tradeProfits, tradeCount + 1);

                            tradeTimes[tradeCount] = OrderOpenTime();
                            tradeTypes[tradeCount] = orderType;  // 0=BUY, 1=SELL
                            tradeProfits[tradeCount] = OrderProfit();
                            tradeCount++;

                            if(DebugMode && !StrategyTester) {
                                Print("INIT: Načten obchod - Ticket: ", OrderTicket(),
                                      ", Type: ", orderType == 0 ? "BUY" : "SELL",
                                      ", Profit: ", OrderProfit(),
                                      ", Open: ", TimeToString(OrderOpenTime()),
                                      ", Close: ", TimeToString(OrderCloseTime()));
                            }
                        }
                    }
                }
            }
        }
    }

    // Seřadíme obchody podle času otevření (chronologicky)
    for(int i = 0; i < tradeCount - 1; i++) {
        for(int j = i + 1; j < tradeCount; j++) {
            if(tradeTimes[i] > tradeTimes[j]) {
                // Swap times
                datetime tempTime = tradeTimes[i];
                tradeTimes[i] = tradeTimes[j];
                tradeTimes[j] = tempTime;

                // Swap types
                int tempType = tradeTypes[i];
                tradeTypes[i] = tradeTypes[j];
                tradeTypes[j] = tempType;

                // Swap profits
                double tempProfit = tradeProfits[i];
                tradeProfits[i] = tradeProfits[j];
                tradeProfits[j] = tempProfit;
            }
        }
    }

    // Analyzujeme obchody a počítáme po sobě jdoucí ztráty
    int consecutiveBuyLoss = 0;
    int consecutiveSellLoss = 0;
    int lastTradeType = -1;  // -1 = neznámý, 0 = BUY, 1 = SELL

    for(int i = 0; i < tradeCount; i++) {
        Trades_Mg1_Count++;

        if(tradeProfits[i] < 0) {
            Trades_Mg1_Loss++;

            // Kontrola po sobě jdoucích ztrát ve stejném směru
            if(tradeTypes[i] == 0) {  // BUY obchod
                if(lastTradeType == 0) {  // Předchozí byl také BUY loss
                    consecutiveBuyLoss++;
                } else {
                    consecutiveBuyLoss = 1;  // První BUY loss v sérii
                    consecutiveSellLoss = 0; // Reset SELL série
                }
                lastTradeType = 0;
            } else if(tradeTypes[i] == 1) {  // SELL obchod
                if(lastTradeType == 1) {  // Předchozí byl také SELL loss
                    consecutiveSellLoss++;
                } else {
                    consecutiveSellLoss = 1;  // První SELL loss v sérii
                    consecutiveBuyLoss = 0;  // Reset BUY série
                }
                lastTradeType = 1;
            }
        } else {
            // Ziskový obchod přeruší sérii ztrát
            consecutiveBuyLoss = 0;
            consecutiveSellLoss = 0;
            lastTradeType = -1;
        }
    }

    // Nastavení finálních hodnot
    Consecutive_BUY_Loss = consecutiveBuyLoss;
    Consecutive_SELL_Loss = consecutiveSellLoss;

    // Kontrola, zda je potřeba nastavit Reverse_Trade_Pending
    // FUNGUJE PRO OBA MÓDY (NORMAL i REVERSE)
    if(Reverse_Loss > 0) {
        bool consecutiveLossCondition = (Consecutive_BUY_Loss >= Reverse_Loss || Consecutive_SELL_Loss >= Reverse_Loss);

        if(consecutiveLossCondition) {
            // Kontrola, zda je ještě prostor pro reverzní obchod (před D_Max_Loss)
            if(D_Max_Loss == 0 || Trades_Mg1_Loss < D_Max_Loss) {
                Reverse_Trade_Pending = true;
                string lossDirection = (Consecutive_BUY_Loss >= Reverse_Loss) ? "BUY" : "SELL";
                if(DebugMode && !StrategyTester) {
                    Print("INIT: Reverse_Trade_Pending nastaven na TRUE - nalezeno ", Reverse_Loss, " po sobě jdoucích ", lossDirection, " ztrát v historii");
                }
            }
        }
    }

    if(DebugMode && !StrategyTester) {
        Print("INIT: Načteny hodnoty z historie - Count: ", Trades_Mg1_Count,
              ", Loss: ", Trades_Mg1_Loss,
              ", BUY_Loss: ", Consecutive_BUY_Loss,
              ", SELL_Loss: ", Consecutive_SELL_Loss,
              ", Reverse_Pending: ", Reverse_Trade_Pending ? "YES" : "NO");
    }
}

//+------------------------------------------------------------------+
//| Kontroluje uzavření reverse obchodů na konci obchodního dne     |
//+------------------------------------------------------------------+
void CheckEndOfDayCloseForReverseOrders() {
    // Kontrola pouze pokud jsou zapnuté obchodní hodiny
    if(!UseTradeHours) {
        return;
    }

    // Kontrola, zda jsme na konci obchodního dne
    int currentHour = TimeHour(TimeCurrent());
    if(currentHour != TradeHoursEnd) {
        return;
    }

    // Uzavření všech aktivních pozic s MagicNo1 v OBOU módech
    // (reverzní obchody se vždy uzavírají na konci obchodního dne)
    if(Reverse_Trade_Active) {
        for(int i = OrdersTotal() - 1; i >= 0; i--) {
            if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
                if(OrderSymbol() == _Symbol && OrderMagicNumber() == MagicNo1) {
                    int orderType = OrderType();
                    if(orderType == OP_BUY || orderType == OP_SELL) {
                        // Uzavření market pozice
                        bool result = false;
                        if(orderType == OP_BUY) {
                            result = OrderClose(OrderTicket(), OrderLots(), MarketInfo(_Symbol, MODE_BID), 3);
                        } else {
                            result = OrderClose(OrderTicket(), OrderLots(), MarketInfo(_Symbol, MODE_ASK), 3);
                        }

                        if(result) {
                            if(!StrategyTester) Print("Uzavřena reverzní MG1 pozice na konci obchodního dne - Ticket: ", OrderTicket());
                            Reverse_Trade_Active = false;  // Reset po uzavření
                        } else {
                            if(!StrategyTester) Print("Chyba při uzavírání reverzní MG1 pozice na konci dne: ", GetLastError());
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
