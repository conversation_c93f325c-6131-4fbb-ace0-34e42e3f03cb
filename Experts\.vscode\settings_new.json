{"C_Cpp.default.includePath": ["c:\\Users\\<USER>\\AppData\\Roaming\\MetaQuotes\\Terminal\\2BF3BABFCAC5D01B05BDC27880F98926\\MQL4\\Include\\Include"], "mql_tools.terminal": "c:\\Users\\<USER>\\AppData\\Roaming\\MetaQuotes\\Terminal\\2BF3BABFCAC5D01B05BDC27880F98926", "mql_tools.metaeditorPath": "C:\\Program Files (x86)\\MetaTrader 4 IC Markets\\metaeditor.exe", "mql_tools.compileOnSave": true, "mql_tools.showCompileReport": true, "mql_tools.showCompileProgress": true, "mql_tools.compileOnType": false, "mql_tools.compileOnChange": false, "mql_tools.compileOnBuild": true, "mql_tools.compileOnOpen": false, "github.copilot.enable": {"*": false}, "files.associations": {"*.mq4": "cpp", "ma table": "cpp"}}