<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rekapitulace podmínek pro vstup do obchodu</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 20px;
        }
        h3 {
            color: #3498db;
        }
        strong {
            color: #e74c3c;
        }
        ul {
            margin-bottom: 15px;
        }
        li {
            margin-bottom: 5px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        @media print {
            body {
                font-size: 12pt;
            }
            h1 {
                font-size: 18pt;
            }
            h2 {
                font-size: 16pt;
            }
            h3 {
                font-size: 14pt;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Rekapitulace všech podmínek pro vstup do obchodu</h1>
        
        <p>Pro vstup do obchodu musí být splněny <strong>všechny</strong> následující podmínky:</p>
        
        <h2>1. Základní podmínky (společné pro BUY i SELL)</h2>
        <ul>
            <li><strong>ZZ vzdálenost</strong> musí být větší než vybraný ATR limit (zelený, oranžový nebo červený podle nastavení MinZZDistForTrade)</li>
            <li><strong>Nejbližší hodnota</strong> musí být v rozsahu pro obchodování (MathAbs(LastNearestDistance) <= HighlightDistance)</li>
            <li><strong>Obchodní hodiny</strong> - aktuální čas musí být v povoleném rozmezí obchodních hodin (pokud je UseTradeHours=true)</li>
            <li><strong>BandCheck</strong> - pokud je BandCheck=true, musí být splněna podmínka pro obálky (viz níže)</li>
        </ul>
        
        <h2>2. Podmínka BandCheck (pokud je aktivní)</h2>
        <ul>
            <li>Pro <strong>BUY</strong>: Cena musí být pod spodní obálkou (lastClose < LowerEnvelope)</li>
            <li>Pro <strong>SELL</strong>: Cena musí být nad horní obálkou (lastClose > UpperEnvelope)</li>
        </ul>
        
        <h2>3. Specifické podmínky pro typ obchodu</h2>
        
        <h3>Pro BUY obchody:</h3>
        <ul>
            <li>Základní podmínka: lastClose < bestValue (potenciální BUY)</li>
            <li><strong>Market BUY</strong>: Pokud je aktuální cena Ask pod MA (currentAsk < bestValue)
                <ul>
                    <li>A zároveň nemáme aktivní BUY pozici (!PositionExists(OP_BUY))</li>
                </ul>
            </li>
            <li><strong>Buy Limit</strong>: Pokud je aktuální cena Ask nad nebo na úrovni MA (currentAsk >= bestValue)
                <ul>
                    <li>A zároveň nemáme aktivní Buy Limit pokyn nebo se cena výrazně změnila (!IsPendingOrderValid(pendingBuyTicket) || MathAbs(pendingBuyPrice - bestValue) > Point * 10)</li>
                </ul>
            </li>
        </ul>
        
        <h3>Pro SELL obchody:</h3>
        <ul>
            <li>Základní podmínka: lastClose > bestValue (potenciální SELL)</li>
            <li><strong>Market SELL</strong>: Pokud je aktuální cena Bid nad MA (currentBid > bestValue)
                <ul>
                    <li>A zároveň nemáme aktivní SELL pozici (!PositionExists(OP_SELL))</li>
                </ul>
            </li>
            <li><strong>Sell Limit</strong>: Pokud je aktuální cena Bid pod nebo na úrovni MA (currentBid <= bestValue)
                <ul>
                    <li>A zároveň nemáme aktivní Sell Limit pokyn nebo se cena výrazně změnila (!IsPendingOrderValid(pendingSellTicket) || MathAbs(pendingSellPrice - bestValue) > Point * 10)</li>
                </ul>
            </li>
        </ul>
        
        <h2>4. Parametry obchodu</h2>
        <ul>
            <li><strong>Stop Loss</strong>: ATR14_D1 / 10</li>
            <li><strong>Take Profit</strong>: 2 × Stop Loss</li>
        </ul>
        
        <h2>5. Zrušení čekajících pokynů</h2>
        <p>Čekající pokyny jsou zrušeny, pokud:</p>
        <ul>
            <li>Není splněna některá z výše uvedených podmínek</li>
            <li>Jsme mimo obchodní hodiny</li>
            <li>Hodnota není v obchodním rozsahu</li>
            <li>ZZ vzdálenost není dostatečná</li>
            <li>Podmínka BandCheck není splněna</li>
        </ul>
    </div>
</body>
</html>
