{"mql-tools.defaultCompiler": "mql-tools", "files.associations": {"*.mqh": "cpp", "*.mq4": "cpp", "*.mq5": "cpp"}, "C_Cpp.default.compilerPath": "", "C_Cpp.autocomplete": "default", "C_Cpp.intelliSenseEngine": "default", "C_Cpp.errorSquiggles": "disabled", "C_Cpp.default.cStandard": "c11", "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.includePath": ["${workspaceFolder}/**", "${workspaceFolder}/Include", "c:\\Users\\<USER>\\AppData\\Roaming\\MetaQuotes\\Terminal\\2BF3BABFCAC5D01B05BDC27880F98926\\MQL4\\Include\\Include"], "C_Cpp.default.intelliSenseMode": "gcc-x64", "C_Cpp.autocompleteAddParentheses": true, "files.exclude": {"**/*.ex4": true, "**/*.ex5": true, "**/*_@!!@.mq4": true, "**/*_@!!@.mq5": true, "**/*_@!!@.mqh": true, "**/*_@!!@.log": true}, "editor.tabSize": 3, "C_Cpp.default.forcedInclude": ["c:\\Users\\<USER>\\.vscode\\extensions\\l-i-v.mql-tools-2.2.0\\data\\mql4_en.mqh"], "mql_tools.Metaeditor.Metaeditor4Dir": "C:\\Program Files (x86)\\MetaTrader 4 IC Markets 5\\metaeditor.exe"}