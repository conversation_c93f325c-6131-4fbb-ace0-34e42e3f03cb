
//+------------------------------------------------------------------+
//|                                                   TableDisplayEA |
//|                      Copyright 2024, Custom Scripts              |
//+------------------------------------------------------------------+
#property strict


#import "kernel32.dll"
   int WinExec(const uchar &cmd[], uint mode);
#import

#import "shell32.dll"
   int ShellExecuteW(int hwnd, string lpOperation, string lpFile, string lpParameters, string lpDirectory, int nShowCmd);
#import



// ZZ Setup
extern string sep1 = "=== ZZ Setup ===";  //--------------------- ZZ Setup -------------------------------

extern double StopLoss = 50;       // Stop Loss v bodech
extern double TakeProfit = 100;    // Take Profit v bodech

extern int ZZ_Depth = 12;             // Depth
extern int ZZ_Deviation = 5;     // Deviation
extern int ZZ_Backstep = 3;        // Backstep

extern int Stoch_K = 13;           // Perioda %K pro Stochastic
extern int Stoch_D = 3;            // Perioda %D pro Stochastic
extern int Stoch_Slowing = 8;      // Zpomalení Stochastic
extern double StochOverbought = 80; // Úvěrově překoupení
extern double StochOversold = 20;   // Úvěrově přeprodanosti
extern bool BandCheck = true;       // Podmínka pro kontrolu Envelopes
extern double EnvelopeDeviation = 0.5; // Odchylka Envelopes
extern int EnvelopePeriod = 20;     // Perioda Envelopes
extern int EALastValues = 5;      
extern int FileGenLastValues = 200;
extern bool FileExport = true;


enum ChartCornerZZ
{
   LeftUpperZZ = 0,
   RightUpperZZ = 1,
   LeftLowerZZ = 2,
   RightLowerZZ = 3
};

// Enum pro výběr zvuku
enum AlertSoundType
{
   Alert_None,
   Alert_Alert,
   Alert_Alert2,
   Alert_Tick,
   Alert_OK,
   Alert_Timeout,
   Alert_News,
   Alert_Stops
};


input AlertSoundType g_AlertSound = Alert_None;     // Výběr zvuku
input ChartCornerZZ SelectedCorner = RightUpperZZ;
input double Min_ZZ_Distance1 = 10.0;   // hranice pro červenou/oranžovou
input double Min_ZZ_Distance2 = 20.0;  // hranice pro oranžovou/zelenou


// ZigZag buffer
double zzHigh[], zzLow[];
int zzHandle;

   double zzHighValue, zzLowValue, zzPeakPrice, zzLastL, zzLastH, zzLastPeak;
   double zzHighValue2, zzLowValue2, zzPeakPrice2;
   double StochK, StochD;
   double UpperEnvelope, LowerEnvelope;
   
   double zzValues[], zzDistance, zzLastDistance;
   int zzIndexes[];
   string zzMessages[];
   string zzTypes[];
   string zzInfos[];
   string zzOutputs[];

string maName, HLineName;
static int lastPeriod = 0;
datetime lastUpdate = 0;


// Enum pro volbu rohu
enum ChartCorner
{
   LeftUpper = 0,  // Levý horní roh
   RightUpper = 1, // Pravý horní roh
   LeftLower = 2,  // Levý dolní roh
   RightLower = 3  // Pravý dolní roh
};

// --- Vstupní proměnné pro pozice a vzhled ---
input ChartCorner Corner = LeftUpper;    // Umístění v rohu
input int CellWidth = 60;                // Šířka buňky
input int CellHeight = 16;               // Výška buňky
input color GripColor = clrYellow;       // Barva GripPointu
input int TableMargin = 20;              // Okraj od okraje grafu
input bool AutoAdjustSize = true;        // Automaticky přizpůsobit velikost rozlišení

// --- Vstupní proměnné pro zvýraznění buněk ---
input double HighlightDistance = 1.5;    // Minimální vzdálenost od ceny pro zvýraznění

// Aktuální hodnoty velikosti buněk (mohou být změněny podle rozlišení)
int currentCellWidth;
int currentCellHeight;

string MA_labels_Y[3] = {"M5", "H1", "H4"};
string D_labels_Y[3] = {"D0", "D1", "W1"};

// Proměnné pro stav zobrazení tabulek
bool showMATable = true;
bool showDTable = true;

// Proměnné pro pozice
int cornerX = 20;  // Výchozí X pozice řídícího bodu
int cornerY = 50;  // Výchozí Y pozice řídícího bodu
string gripPointName = "GripPoint"; // Název GripPointu
int chartWidth, chartHeight;        // Rozměry grafu

// Proměnné pro blikání a zvýraznění buněk
datetime lastBlinkTime = 0;
bool blinkState = false;
double LastNearestDistance = 0;                     // Nejmenší vzdálenost od Close(1) z funkce FindClosestToCloseAndColorMap
input color DefaultColor = clrWhite;                // Výchozí barva buňky
input color CloseRangeColor = clrGreen;             // Barva blízké hodnoty
input color BlinkColor = clrRed;                    // Barva blikající hodnoty
input color BlinkAltColor = clrWhite;               // Alternativní barva při blikání
input int DefaultFontSize = 10;                     // Výchozí velikost písma v tabulce
input int HighlightFontSize = 12;                   // Zvýrazněná velikost písma
input int BlinkIntervalSec = 1;                     // Interval blikání v sekundách

// Globální proměnné pro velikosti polí
int maRows, maCols;  // Počet řádků a sloupců MA tabulky
int dRows, dCols;    // Počet řádků a sloupců D tabulky

struct LabelInfo {
   string text;
   color  clr;
   int    period;
   int    maType;
};

LabelInfo MA_labels_X[4] = {
   {"EMA21",  clrBlue,     21,  MODE_EMA},
   {"EMA100", clrViolet,   100, MODE_EMA},
   {"EMA200", clrOrangeRed, 200, MODE_EMA},
   {"SMA200", clrDarkOrange, 200, MODE_SMA}
};

LabelInfo D_labels_X[3] = {
   {"High", clrLightSalmon},
   {"Low",  clrLightSalmon},
   {"HL50", clrViolet}
};

double MA_Values[4][3];
color MA_Colors[4][3];
int MA_FontSizes[4][3];

double D_Values[3][3];
color D_Colors[3][3];
int D_FontSizes[3][3];


string TF_labels[3][3] = {
    {"M5", "H1", "H4"},
    {"M15", "H4", "D1"},
    {"H1", "H4", "W1"}
};
int currentTFIndex = 0;



int OnInit()
{
    // Inicializace aktuálních hodnot velikosti buněk z vstupních proměnných
    currentCellWidth = CellWidth;
    currentCellHeight = CellHeight;

    // Automatické přizpůsobení velikosti buněk podle rozlišení
    AdjustCellSizeForResolution();

    // Inicializace globálních proměnných pro velikosti polí
    maRows = ArraySize(MA_labels_Y);
    maCols = ArraySize(MA_labels_X);
    dRows = ArraySize(D_labels_Y);
    dCols = ArraySize(D_labels_X);

    // Inicializace velikostí hodnot v tabulkách
    int maValRows = ArrayRange(MA_Values, 0);
    int maValCols = ArrayRange(MA_Values, 1);
    int dValRows = ArrayRange(D_Values, 0);
    int dValCols = ArrayRange(D_Values, 1);

    // Inicializace barev a velikostí písma s použitím globálních proměnných
    for (int i = 0; i < maCols; i++)
        for (int j = 0; j < maRows; j++) {
            MA_Colors[i][j] = DefaultColor;
            MA_FontSizes[i][j] = DefaultFontSize;
        }

    for (int i = 0; i < dCols; i++)
        for (int j = 0; j < dRows; j++) {
            D_Colors[i][j] = DefaultColor;
            D_FontSizes[i][j] = DefaultFontSize;
        }

    DeleteTable("MACell");
    DeleteTable("DCell");

    // Nastavení výchozích souřadnic podle zvoleného rohu
    SetInitialCoordinates();

    // Vytvoření GripPointu
    CreateGripPoint();

    // Vytvoření tlačítek s výchozí zelenou barvou (tabulky jsou zobrazeny)
    // Všechny pozice jsou relativní k cornerX a cornerY

    // Výpočet vertikálního odsazení pro tlačítka a tabulky
    int buttonY = cornerY + 10; // Tlačítka jsou výše (zmenšeno z 20)
    int maTableY = buttonY + 20; // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)
    int dButtonY = maTableY + (currentCellHeight * 4) + 10; // D tlačítko je pod MA tabulkou s mezerou
    int dTableY = dButtonY + 20; // D tabulka začíná 20 pixelů pod D tlačítkem (zvětšeno z 15)

    // Vytvoření tlačítek pro MA tabulku
    CreateToggleButton("ToggleMA", cornerX + 0, buttonY, "MA Tabulka", clrForestGreen);
    CreateToggleButton("TFNext", cornerX + 90, buttonY, "TF >>", clrSlateGray); // Posunuto blíže (z 100)
    CreateToggleButton("TFPrev", cornerX + 180, buttonY, "TF <<", clrSlateGray); // Posunuto blíže (z 180)

    UpdateMAValues();
    UpdateDValues();

    // Inicializace časovače pro blikání
    if (!EventSetTimer(BlinkIntervalSec)) {
        Print("Nepodařilo se nastavit časovač, Chyba: ", GetLastError());
        return INIT_FAILED;
    }

    // Inicializace barev a zvýraznění buněk
    FindClosestToCloseAndColorMap();

    // Zobrazení MA tabulky (výchozí stav je zobrazeno)
    if (showMATable) {
        DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, maRows, maCols, cornerX, maTableY, clrWhite);
    }

    // Vytvoření tlačítka pro D tabulku s výchozí zelenou barvou
    CreateToggleButton("ToggleD", cornerX, dButtonY, "D Tabulka", clrForestGreen);

    // Zobrazení D tabulky (výchozí stav je zobrazeno)
    if (showDTable) {
        DrawTable("DCell", D_labels_X, D_labels_Y, D_Values, D_Colors, D_FontSizes, dRows, dCols, cornerX, dTableY, clrWhite);
    }

    UpdateTFLabels();

    // Nastavení události pro změnu velikosti grafu
    ChartSetInteger(0, CHART_EVENT_OBJECT_CREATE, true);
    ChartSetInteger(0, CHART_EVENT_OBJECT_DELETE, true);

    return INIT_SUCCEEDED;
}

void OnDeinit(const int reason)
{
    // Odstranění tabulek
    DeleteTable("MACell");
    DeleteTable("DCell");

    // Odstranění tlačítek
    ObjectDelete(0, "ToggleMA");
    ObjectDelete(0, "ToggleD");
    ObjectDelete(0, "TFNext");
    ObjectDelete(0, "TFPrev");

    // Odstranění GripPointu
    ObjectDelete(0, gripPointName);

    // Ukončení časovače
    EventKillTimer();
}

void OnTick()
{

   static datetime Last_M1_Open = 0;
   static datetime Last_M5_Open = 0;
   static datetime Last_TF_Open = 0;
   
   if (Period() != lastPeriod)
   {
      Print("Timeframe se změnil!");
      lastPeriod = Period();
   }

   // 1. Blok pro provedení při každém otevření nové M1 svíčky
   datetime currentM1BarTime = iTime(_Symbol, PERIOD_M1, 0);

   if (Last_M1_Open != currentM1BarTime)
   {
      Last_M1_Open = currentM1BarTime;
      
      // Volání funkce ZigZag (5 posledních hodnot)
      ZigZagInfo(zzValues, zzIndexes, zzMessages, false, false);
   
      zzDistance = NormalizeDouble((zzValues[0] - zzValues[1]), Digits);
   
      ShowBigNumber(zzDistance);

   }
   
   // 2.  Blok pro provedení při každém otevření nové M5 svíčky
   datetime currentM5BarTime = iTime(_Symbol, PERIOD_M5, 0);
   if (Last_M5_Open != currentM5BarTime)
   {
      Last_M5_Open = currentM5BarTime;

      // 🔧 Zde můžeš přidat vlastní logiku pro M5 (např. potvrzení patternu)
      // Print("🔹 Nová M5 svíčka: ", TimeToString(currentM5BarTime, TIME_DATE | TIME_MINUTES));
   }
   
  
   // 3. Blok pro provedení při každém otevření nové svíčky podle zvoleného TF 
   if (Last_TF_Open != Time[0])
   {
       Last_TF_Open = Time[0];
       
      // Volejte funkci DrawMA po uzavření svíčky
      // string levelNames[] = {"Denní High", "Denní Low", "PD1 High", "PD1 Low", "50% PD1 H-L", "PW1 High", "PW1 Low", "50% PW1 H-L"};
      
      // Získání hodnot High a Low z předchozího a aktuálního dne
      double previousDayHigh = iHigh(NULL, PERIOD_D1, 1);
      double previousDayLow = iLow(NULL, PERIOD_D1, 1);
      double previousDayHighLow50 = NormalizeDouble(previousDayHigh - ((previousDayHigh - previousDayLow) / 2), Digits);  // Výpočet 50 % mezi High a Low - D1
      
      double previousWeekHigh = iHigh(NULL, PERIOD_W1, 1);
      double previousWeekLow = iLow(NULL, PERIOD_W1, 1);
      double previousWeekHighLow50 = NormalizeDouble(previousWeekHigh - ((previousWeekHigh - previousWeekLow) / 2), Digits);  // Výpočet 50 % mezi High a Low - W1       

      // M5
      DrawMA(Symbol(), PERIOD_M5, 100, 0, MODE_EMA, PRICE_CLOSE, 1, clrDarkViolet, STYLE_DASHDOTDOT, 1, "EMA");
      DrawMA(Symbol(), PERIOD_M5, 200, 0, MODE_EMA, PRICE_CLOSE, 1, clrRed, STYLE_DASHDOTDOT, 1, "EMA");        
      DrawMA(Symbol(), PERIOD_M5, 200, 0, MODE_SMA, PRICE_CLOSE, 1, clrOrange, STYLE_DASHDOTDOT, 1, "SMA");

      // H1
      DrawMA(Symbol(), PERIOD_H1,  21, 0, MODE_EMA, PRICE_CLOSE, 1, clrBlue, STYLE_DASH, 2, "EMA");
      DrawMA(Symbol(), PERIOD_H1, 100, 0, MODE_EMA, PRICE_CLOSE, 1, clrDarkViolet, STYLE_DASH, 2, "EMA");
      DrawMA(Symbol(), PERIOD_H1, 200, 0, MODE_EMA, PRICE_CLOSE, 1, clrRed, STYLE_DASH, 2, "EMA");        
      DrawMA(Symbol(), PERIOD_H1, 200, 0, MODE_SMA, PRICE_CLOSE, 1, clrOrange, STYLE_DASH, 2, "SMA");
      
      // H4
      DrawMA(Symbol(), PERIOD_H4,  21, 0, MODE_EMA, PRICE_CLOSE, 1, clrBlue, STYLE_DASH, 2, "EMA");
      DrawMA(Symbol(), PERIOD_H4, 100, 0, MODE_EMA, PRICE_CLOSE, 1, clrDarkViolet, STYLE_DASH, 2, "EMA");
      DrawMA(Symbol(), PERIOD_H4, 200, 0, MODE_EMA, PRICE_CLOSE, 1, clrRed, STYLE_DASH, 2, "EMA");        
      DrawMA(Symbol(), PERIOD_H4, 200, 0, MODE_SMA, PRICE_CLOSE, 1, clrOrange, STYLE_DASH, 2, "SMA");
      
        
      // Zavolání funkce pro vykreslení čar pro High, Low a 50 % předchozího dne s popiskem
      
      DrawHorizontalLineWithLabel("PrevDayHigh", previousDayHigh, clrLightSalmon, STYLE_DOT, 1, "PD1 High");
      DrawHorizontalLineWithLabel("PrevDayLow", previousDayLow, clrLightSalmon, STYLE_DOT, 1, "PD1 Low");
      DrawHorizontalLineWithLabel("PrevDayHighLow50", previousDayHighLow50, clrDarkViolet, STYLE_DOT, 1, "PD1 50% High-Low");
      
      DrawHorizontalLineWithLabel("PreviousWeekHigh", previousWeekHigh, clrLightSalmon, STYLE_DOT, 1, "PW1 High");
      DrawHorizontalLineWithLabel("PreviousWeekLow", previousWeekLow, clrLightSalmon, STYLE_DOT, 1, "PW1 Low");
      DrawHorizontalLineWithLabel("PreviousWeekHighLow50", previousWeekHighLow50, clrDarkViolet, STYLE_DOT, 1, "PW1 50% High-Low");


   // Získání posledních ZigZag hodnot
   int totalBars = Bars(_Symbol, PERIOD_CURRENT);
   zzPeakPrice = iCustom(NULL, 0, "ZigZag", 0, 1);
   zzHighValue = iCustom(NULL, 0, "ZigZag", 1, 1);
   zzLowValue = iCustom(NULL, 0, "ZigZag", 2, 1);
   
   zzPeakPrice2 = iCustom(NULL, 0, "ZigZag", 0, 2);
   zzHighValue2 = iCustom(NULL, 0, "ZigZag", 1, 2);
   zzLowValue2 = iCustom(NULL, 0, "ZigZag", 2, 2);
   
   //zzPeakPrice = iCustom(_Symbol, PERIOD_CURRENT, "ZigZag", ZZ_Depth, ZZ_Deviation, ZZ_Backstep, 0, 0);
   //zzHighValue = iCustom(_Symbol, PERIOD_CURRENT, "ZigZag", ZZ_Depth, ZZ_Deviation, ZZ_Backstep, 1, 0);
   //zzLowValue = iCustom(_Symbol, PERIOD_CURRENT, "ZigZag", ZZ_Depth, ZZ_Deviation, ZZ_Backstep, 2, 0);
   

   // Získání hodnot Stochastic Oscillator
   StochK = iStochastic(_Symbol, PERIOD_CURRENT, Stoch_K, Stoch_D, Stoch_Slowing, MODE_SMA, 1, MODE_MAIN, 1);
   StochD = iStochastic(_Symbol, PERIOD_CURRENT, Stoch_K, Stoch_D, Stoch_Slowing, MODE_SMA, 1, MODE_SIGNAL, 1);

   // Získání hodnot Envelopes
   UpperEnvelope = iEnvelopes(_Symbol, PERIOD_CURRENT, EnvelopePeriod, 0, MODE_SMA, PRICE_CLOSE, EnvelopeDeviation, MODE_UPPER, 1);
   LowerEnvelope = iEnvelopes(_Symbol, PERIOD_CURRENT, EnvelopePeriod, 0, MODE_SMA, PRICE_CLOSE, EnvelopeDeviation, MODE_LOWER, 1);

   double lastClose = iClose(_Symbol, PERIOD_CURRENT, 1);
   
   if (zzPeakPrice > 0) zzLastPeak = zzPeakPrice;
   if (zzHighValue > 0) zzLastH = zzHighValue;
   if (zzLowValue > 0) zzLastL = zzLowValue;
   
   // Volání funkce ZigZag (5 posledních hodnot)
   // ZigZagInfo(5, zzValues, zzIndexes, zzMessages, false, false);

   for (int i = 0; i < ArraySize(zzMessages); i++)
   {
   Print(zzMessages[i]);
   }
   
   
   //      Comment(
   //     "zzHighValue = ", zzLastH, ",  zzHighValue 2 = ", zzHighValue2,
   //     "\nzzLowValue = ", zzLastL,  ",  zzLowValue 2 = ", zzLowValue2,
   //     "\nzzPeakPrice = ", zzLastPeak,   ",  zzPeakPrice 2 = ", zzPeakPrice2,
   //     "\n\nLow 1 = ", Low[1], ", High 1 = ", High[1],
   //     "\n\nzzValues 1 a 2 = ", zzValues[0],   ",  zzPeakPrice 2 = ", zzValues[1],
   //     "\n\nzzIndexes 1 a 2 = ", zzIndexes[0],   ",  zzIndexes 2 = ", zzIndexes[1],
   //     "\n\nzzDistance 1 a 2 = ", zzDistance, "  bodů"
        
   //     );
        
   // Zavolání funkce

   // ZigZagInfoBuff();
   // ZigZagInfo();

   // Zakreslování vzdálenosti mezi ZigZag vrcholy
   //DrawZigZagDistances();

   // Podmínky pro otevření long pozice (BUY)
   if (zzLowValue > 0 && StochK < StochOversold && StochD < StochOversold && (!BandCheck || lastClose < LowerEnvelope) && !PositionExists(ORDER_TYPE_BUY))
   {
      OpenOrder(ORDER_TYPE_BUY);
   }

   // Podmínky pro otevření short pozice (SELL)
   if (zzHighValue > 0 && StochK > StochOverbought && StochD > StochOverbought && (!BandCheck || lastClose > UpperEnvelope) && !PositionExists(ORDER_TYPE_SELL))
   {
      OpenOrder(ORDER_TYPE_SELL);
   }


        // Aktualizace hodnot a barev
        UpdateMAValues();
        UpdateDValues();
        FindClosestToCloseAndColorMap();
        lastUpdate = Time[0];
        
  

        // Překreslení tabulek s novými hodnotami
        if (showMATable || showDTable) {
            // Výpočet vertikálního odsazení pro tlačítka a tabulky
            int buttonY = cornerY + 10; // Tlačítka jsou výše (zmenšeno z 20)
            int maTableY = buttonY + 20; // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)
            int dButtonY = maTableY + (currentCellHeight * 4) + 10; // D tlačítko je pod MA tabulkou s mezerou
            int dTableY = dButtonY + 20; // D tabulka začíná 20 pixelů pod D tlačítkem (zvětšeno z 15)

            if (showMATable) {
                DeleteTable("MACell");
                DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, maRows, maCols, cornerX, maTableY, clrWhite);
            }

            if (showDTable) {
                DeleteTable("DCell");
                DrawTable("DCell", D_labels_X, D_labels_Y, D_Values, D_Colors, D_FontSizes, dRows, dCols, cornerX, dTableY, clrWhite);
            }
        }
     }  
        
    }


void OnTimer()
{
    // Přepnutí stavu blikání
    blinkState = !blinkState;
    lastBlinkTime = TimeCurrent();

   if (blinkState && g_AlertSound != Alert_None)
   {
      string soundFile = "";
      switch(g_AlertSound)
      {
         case Alert_Alert:    soundFile = "alert.wav";    break;
         case Alert_Alert2:   soundFile = "alert2.wav";   break;
         case Alert_Tick:     soundFile = "tick.wav";     break;
         case Alert_OK:       soundFile = "ok.wav";       break;
         case Alert_Timeout:  soundFile = "timeout.wav";  break;
         case Alert_News:     soundFile = "news.wav";     break;
         case Alert_Stops:    soundFile = "stops.wav";    break;
         default: break;
      }
      if (soundFile != "")
      {
         PlaySound(soundFile);
         // Pro ladění: zalogujeme, že se pokusíme přehrát zvuk
         //Print("Pokus o přehrání zvuku: ", soundFile);
      }
   }  


    // Aktualizace barev a zvýraznění buněk
    FindClosestToCloseAndColorMap();

    // Překreslení tabulek s novými barvami
    if (showMATable || showDTable) {
        // Výpočet vertikálního odsazení pro tlačítka a tabulky
        int buttonY = cornerY + 10; // Tlačítka jsou výše (zmenšeno z 20)
        int maTableY = buttonY + 20; // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)
        int dButtonY = maTableY + (currentCellHeight * 4) + 10; // D tlačítko je pod MA tabulkou s mezerou
        int dTableY = dButtonY + 20; // D tabulka začíná 20 pixelů pod D tlačítkem (zvětšeno z 15)

        if (showMATable) {
            DeleteTable("MACell");
            DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, maRows, maCols, cornerX, maTableY, clrWhite);
        }

        if (showDTable) {
            DeleteTable("DCell");
            DrawTable("DCell", D_labels_X, D_labels_Y, D_Values, D_Colors, D_FontSizes, dRows, dCols, cornerX, dTableY, clrWhite);
        }
    }
}

/**
 * Aktualizuje popisky časových rámců v MA tabulce podle aktuálně vybraného indexu časového rámce.
 * Kopíruje hodnoty z aktuálního řádku TF_labels do MA_labels_Y.
 * Používá již inicializovanou globální proměnnou maRows.
 */
void UpdateTFLabels() {
    // Dynamicky procházíme všechny řádky
    for (int i = 0; i < maRows; i++) {
        // Kopírování hodnot z aktuálního řádku TF_labels do MA_labels_Y
        MA_labels_Y[i] = TF_labels[currentTFIndex][i];
    }
}


/**
 * Aktualizuje hodnoty klouzavých průměrů v MA tabulce.
 * Vypočítá hodnoty klouzavých průměrů pro všechny časové rámce
 * definované v aktuálním řádku TF_labels.
 * Používá dynamické indexy podle velikosti polí a hodnoty z MA_labels_X.
 */
void UpdateMAValues() {
    // Dynamicky procházíme všechny sloupce a řádky
    for (int i = 0; i < maCols; i++) {
        for (int j = 0; j < maRows; j++) {
            // Získání časového rámce z aktuálního řádku TF_labels
            int tf = TFStrToPeriod(TF_labels[currentTFIndex][j]);

            // Získání periody a typu klouzavého průměru z MA_labels_X
            int period = MA_labels_X[i].period;
            int maType = MA_labels_X[i].maType;

            // Výpočet hodnoty klouzavého průměru
            MA_Values[i][j] = iMA(NULL, tf, period, 0, maType, PRICE_CLOSE, 1);
        }
    }
}

/**
 * Aktualizuje hodnoty v D tabulce (High, Low, HL50).
 * Vypočítá hodnoty pro D0, D1 a W1 podle názvů řádků v D_labels_Y.
 * Používá dynamické indexy podle velikosti polí a efektivnější výpočty.
 *
 * Názvy řádků jsou interpretovány následovně:
 * - D0, D1, D2, atd. - PERIOD_D1 s příslušným shiftem (0, 1, 2, ...)
 * - W0, W1, W2, atd. - PERIOD_W1 s příslušným shiftem (0, 1, 2, ...)
 */
void UpdateDValues() {
    // Dynamicky procházíme všechny řádky
    for (int j = 0; j < dRows; j++) {
        // Získání časového rámce a shiftu z názvu řádku
        int timeframe = PERIOD_D1; // Výchozí hodnota
        int shift = 0;             // Výchozí hodnota

        string rowLabel = D_labels_Y[j];

        // Analýza názvu řádku pro určení časového rámce a shiftu
        if (StringLen(rowLabel) >= 2) {
            // První znak určuje časový rámec
            if (StringSubstr(rowLabel, 0, 1) == "D") {
                timeframe = PERIOD_D1;
            }
            else if (StringSubstr(rowLabel, 0, 1) == "W") {
                timeframe = PERIOD_W1;
            }

            // Druhý a další znaky určují shift
            string shiftStr = StringSubstr(rowLabel, 1);
            shift = (int)StringToInteger(shiftStr);
        }

        // Pro každý řádek získáme High a Low
        double highValue = iHigh(NULL, timeframe, shift);
        double lowValue = iLow(NULL, timeframe, shift);

        // Uložení hodnot do pole
        D_Values[0][j] = highValue;  // High
        D_Values[1][j] = lowValue;   // Low

        // Vypočítáme HL50 (střed mezi High a Low) - optimalizovaný výpočet
        D_Values[2][j] = highValue - ((highValue - lowValue) / 2);
    }
}

/**
 * Převádí textový řetězec časového rámce na odpovídající číselnou konstantu.
 * @param tf Textový řetězec časového rámce (např. "M1", "H1", "D1")
 * @return Číselná konstanta časového rámce (např. PERIOD_M1, PERIOD_H1, PERIOD_D1)
 */
int TFStrToPeriod(string tf) {
    // Použití if-else podmínek pro kompatibilitu
    if (tf == "M1")  return PERIOD_M1;
    if (tf == "M5")  return PERIOD_M5;
    if (tf == "M15") return PERIOD_M15;
    if (tf == "M30") return PERIOD_M30;
    if (tf == "H1")  return PERIOD_H1;
    if (tf == "H4")  return PERIOD_H4;
    if (tf == "D1")  return PERIOD_D1;
    if (tf == "W1")  return PERIOD_W1;

    // Pokud není nalezena shoda, vrátíme aktuální časový rámec
    Print("Neznámý časový rámec: ", tf, ", použit aktuální časový rámec");
    return Period();
}

// Nastavení výchozích souřadnic podle zvoleného rohu
/**
 * Nastaví výchozí souřadnice pro umístění tabulek a ovládacích prvků podle zvoleného rohu grafu.
 * Zajišťuje, aby tabulky byly vždy viditelné a nepřesahovaly okraje grafu.
 */
void SetInitialCoordinates()
{
    // Zjištění rozměrů grafu
    chartWidth = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
    chartHeight = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);

    // Výpočet celkové šířky tabulky (pro MA tabulku, která je větší)
    int totalTableWidth = currentCellWidth * 5 + 10; // 5 sloupců pro MA tabulku (4 + 1 pro popisky) + malá rezerva
    int totalTableHeight = currentCellHeight * 10; // přibližná celková výška obou tabulek s tlačítky

    // Použití uživatelského nastavení okraje
    int offsetX = TableMargin;
    int offsetY = TableMargin;

    // Minimální vzdálenost od horního okraje grafu (aby tabulka nebyla příliš vysoko)
    int minTopMargin = (int)(chartHeight * 0.05); // 5% výšky grafu
    if (minTopMargin < 30) minTopMargin = 30; // Minimálně 30 pixelů od horního okraje

    // Minimální vzdálenost od spodního okraje grafu (aby tabulka nebyla příliš nízko)
    int minBottomMargin = (int)(chartHeight * 0.05); // 5% výšky grafu
    if (minBottomMargin < 30) minBottomMargin = 30; // Minimálně 30 pixelů od spodního okraje

    // Nastavení pozice GripPointu podle zvoleného rohu
    switch(Corner)
    {
        case LeftUpper:
            cornerX = offsetX;
            cornerY = minTopMargin; // Použití minimální vzdálenosti od horního okraje
            break;
        case RightUpper:
            // Zajistíme, aby tabulka nebyla mimo viditelnou oblast
            cornerX = chartWidth - totalTableWidth - 5; // Posunuto téměř k pravému okraji (pouze 5 pixelů od okraje)
            cornerY = minTopMargin; // Použití minimální vzdálenosti od horního okraje
            break;
        case LeftLower:
            cornerX = offsetX;
            cornerY = chartHeight - totalTableHeight - minBottomMargin;
            break;
        case RightLower:
            // Zajistíme, aby tabulka nebyla mimo viditelnou oblast
            cornerX = chartWidth - totalTableWidth - 5; // Posunuto téměř k pravému okraji (pouze 5 pixelů od okraje)
            cornerY = chartHeight - totalTableHeight - minBottomMargin;
            break;
    }

    // Kontrola, aby tabulka nebyla mimo viditelnou oblast
    if (cornerX < 0) cornerX = 0;
    if (cornerY < 0) cornerY = 0;

    // Zajistíme, aby tabulka byla vždy alespoň částečně viditelná
    int minVisibleWidth = MathMin(100, totalTableWidth / 2);
    int minVisibleHeight = MathMin(50, totalTableHeight / 2);

    if (cornerX > chartWidth - minVisibleWidth)
        cornerX = chartWidth - minVisibleWidth;
    if (cornerY > chartHeight - minVisibleHeight)
        cornerY = chartHeight - minVisibleHeight;
}

// Vytvoření GripPointu pro přesouvání
/**
 * Vytvoří GripPoint - malé tlačítko, které slouží k přesouvání tabulek a ovládacích prvků.
 * GripPoint je umístěn před tlačítkem MA Tabulka a má stejnou Y-pozici.
 */
void CreateGripPoint()
{
    if(ObjectFind(0, gripPointName) >= 0)
        ObjectDelete(0, gripPointName);

    // Výpočet pozice GripPointu - před tlačítkem MA Tabulka
    int buttonY = cornerY + 20;

    ObjectCreate(0, gripPointName, OBJ_BUTTON, 0, 0, 0);
    ObjectSetInteger(0, gripPointName, OBJPROP_CORNER, 0);
    ObjectSetInteger(0, gripPointName, OBJPROP_XDISTANCE, cornerX - 15 ); // 15 pixelů před tlačítkem MA Tabulka
    ObjectSetInteger(0, gripPointName, OBJPROP_YDISTANCE, buttonY - 10 ); // Stejná Y-pozice jako tlačítko MA Tabulka
    ObjectSetInteger(0, gripPointName, OBJPROP_XSIZE, 10);
    ObjectSetInteger(0, gripPointName, OBJPROP_YSIZE, 10);
    ObjectSetInteger(0, gripPointName, OBJPROP_BGCOLOR, GripColor);
    ObjectSetInteger(0, gripPointName, OBJPROP_BORDER_COLOR, clrBlack);
    ObjectSetInteger(0, gripPointName, OBJPROP_HIDDEN, false);
    ObjectSetInteger(0, gripPointName, OBJPROP_SELECTABLE, true);
    ObjectSetString(0, gripPointName, OBJPROP_TEXT, "");
}

/**
 * Automaticky přizpůsobí velikost buněk a pozici tabulek podle rozlišení grafu.
 * Menší grafy mají menší buňky, větší grafy mají větší buňky.
 * Také zajišťuje, aby tabulky byly vždy viditelné a nepřesahovaly okraje grafu.
 */
void AdjustCellSizeForResolution()
{
    if (AutoAdjustSize) {
        // Zjištění rozměrů grafu
        int width = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
        int height = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);

        // Přizpůsobení velikosti buněk podle šířky grafu
        if (width < 800) {
            // Pro malé rozlišení
            currentCellWidth = 50; // Zmenšeno z 60
            currentCellHeight = 14;
        } else if (width < 1200) {
            // Pro střední rozlišení
            currentCellWidth = 60; // Zmenšeno z 70
            currentCellHeight = 16;
        } else {
            // Pro vysoké rozlišení
            currentCellWidth = 70; // Zmenšeno z 80
            currentCellHeight = 18;
        }

        // Zajištění, aby tabulka nebyla příliš nízko na grafu
        // Pokud je tabulka v horní části grafu, posuneme ji níže od horního okraje
        if (Corner == LeftUpper || Corner == RightUpper) {
            // Pro horní rohy - posuneme tabulku níže od horního okraje
            int minTopMargin = (int)(height * 0.05); // 5% výšky grafu jako minimální horní okraj
            if (cornerY < minTopMargin) {
                cornerY = minTopMargin;
            }
        } else {
            // Pro dolní rohy - zajistíme, aby tabulka nebyla příliš blízko spodního okraje
            int totalTableHeight = currentCellHeight * 10; // přibližná celková výška obou tabulek s tlačítky
            int minBottomMargin = (int)(height * 0.05); // 5% výšky grafu jako minimální spodní okraj

            if (cornerY > height - totalTableHeight - minBottomMargin) {
                cornerY = height - totalTableHeight - minBottomMargin;
            }
        }
    }
}

/**
 * Vytvoří přepínací tlačítko s daným názvem, pozicí a textem.
 * @param name Název objektu tlačítka
 * @param x X-souřadnice tlačítka
 * @param y Y-souřadnice tlačítka
 * @param label Text zobrazený na tlačítku
 * @param bg Barva pozadí tlačítka
 */
void CreateToggleButton(string name, int x, int y, string label, color bg)
{
   ObjectCreate(0, name, OBJ_BUTTON, 0, 0, 0);
   ObjectSetInteger(0, name, OBJPROP_CORNER, 0);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(0, name, OBJPROP_BORDER_COLOR, clrGray);
   ObjectSetInteger(0, name, OBJPROP_BGCOLOR, bg);
   ObjectSetInteger(0, name, OBJPROP_XSIZE, 70); // Zmenšeno z 80
   ObjectSetInteger(0, name, OBJPROP_YSIZE, 16);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, false);
   ObjectSetInteger(0, name, OBJPROP_STATE, true);
   ObjectSetString(0, name, OBJPROP_TEXT, label);
}

/**
 * Aktualizuje barvu tlačítka podle jeho stavu (zapnuto/vypnuto).
 * @param buttonName Název tlačítka, jehož barva se má aktualizovat
 * @param isOn True, pokud je tlačítko zapnuto (zelená barva), False pro vypnuto (červená barva)
 */
void UpdateButtonColor(string buttonName, bool isOn)
{
   color buttonColor = isOn ? clrForestGreen : clrFireBrick;
   ObjectSetInteger(0, buttonName, OBJPROP_BGCOLOR, buttonColor);
}

/**
 * Odstraní všechny objekty tabulky se zadaným prefixem.
 * @param prefix Prefix názvů objektů, které mají být odstraněny
 */
void DeleteTable(string prefix)
{
    for (int i = ObjectsTotal() - 1; i >= 0; i--)
    {
        string name = ObjectName(0, i);
        if (StringFind(name, prefix) == 0)
            ObjectDelete(0, name);
    }
}

/**
 * Vykreslí tabulku s hodnotami a popisky.
 * @param prefix Prefix pro názvy objektů tabulky
 * @param labels_X Pole popisků sloupců
 * @param labels_Y Pole popisků řádků
 * @param data Dvourozměrné pole hodnot
 * @param colorMap Dvourozměrné pole barev pro jednotlivé buňky
 * @param fontSizes Dvourozměrné pole velikostí písma pro jednotlivé buňky
 * @param rows Počet řádků tabulky
 * @param cols Počet sloupců tabulky
 * @param xOffset X-souřadnice levého horního rohu tabulky
 * @param yOffset Y-souřadnice levého horního rohu tabulky
 * @param colDefault Výchozí barva textu
 */
void DrawTable(string prefix,
               LabelInfo &labels_X[],
               string &labels_Y[],
               double &data[][],
               color &colorMap[][],
               int &fontSizes[][],
               int rows,
               int cols,
               int xOffset,
               int yOffset,
               color colDefault)
{
    int index = 0;

    // --- Zobrazení hodnoty DIST v pravém horním rohu ---
    if (prefix == "MACell" && LastNearestDistance != 0.0)
    {
        // Formátování textu se znaménkem + nebo -
        string sign = LastNearestDistance > 0 ? "+" : "";
        string distText = "LD: " + sign + DoubleToString(LastNearestDistance, 2) + " b";
        color distColor = (MathAbs(LastNearestDistance) <= HighlightDistance) ? clrLimeGreen : colDefault;

        string distLabelName = prefix + "_NearestDistanceTopRight";
        ObjectCreate(0, distLabelName, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(0, distLabelName, OBJPROP_CORNER, 0);
        ObjectSetInteger(0, distLabelName, OBJPROP_XDISTANCE, xOffset + currentCellWidth * cols);
        ObjectSetInteger(0, distLabelName, OBJPROP_YDISTANCE, yOffset - currentCellHeight - 4 );
        ObjectSetInteger(0, distLabelName, OBJPROP_FONTSIZE, 12);
        ObjectSetInteger(0, distLabelName, OBJPROP_COLOR, distColor);
        ObjectSetString(0, distLabelName, OBJPROP_TEXT, distText);
    }

    // --- Vykreslení celé tabulky ---
    for (int row = 0; row <= rows; row++)
    {
        for (int col_i = 0; col_i <= cols; col_i++)
        {
            string text;
            color useColor = colDefault;
            int fontSize = 10;

            if (row == 0 && col_i == 0)
                text = "";
            else if (row == 0)
            {
                text = labels_X[col_i - 1].text;
                useColor = labels_X[col_i - 1].clr;
            }
            else if (col_i == 0)
                text = labels_Y[row - 1];
            else
            {
                text = DoubleToString(data[col_i - 1][row - 1], 2);
                useColor = colorMap[col_i - 1][row - 1];
                fontSize = fontSizes[col_i - 1][row - 1];
            }

            string objName = prefix + "_" + (string)index;
            ObjectCreate(0, objName, OBJ_LABEL, 0, 0, 0);
            ObjectSetInteger(0, objName, OBJPROP_CORNER, 0);
            ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, xOffset + currentCellWidth * col_i);
            ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, yOffset + currentCellHeight * row);
            ObjectSetInteger(0, objName, OBJPROP_FONTSIZE, fontSize);
            ObjectSetInteger(0, objName, OBJPROP_COLOR, useColor);
            ObjectSetString(0, objName, OBJPROP_TEXT, text);
            index++;
        }
    }
}




/**
 * Aktualizuje pozice a obsah všech prvků podle nové pozice GripPointu.
 * Přepočítá pozice tlačítek a tabulek, aktualizuje hodnoty a překreslí tabulky.
 */
void UpdateAllElements()
{
    // Výpočet vertikálního odsazení pro tlačítka a tabulky
    int buttonY = cornerY + 10; // Tlačítka jsou výše (zmenšeno z 20)
    int maTableY = buttonY + 20; // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)
    int dButtonY = maTableY + (currentCellHeight * 4) + 10; // D tlačítko je pod MA tabulkou s mezerou
    int dTableY = dButtonY + 20; // D tabulka začíná 20 pixelů pod D tlačítkem (zvětšeno z 15)

    // Aktualizace pozice GripPointu
    ObjectSetInteger(0, gripPointName, OBJPROP_XDISTANCE, cornerX - 15); // 15 pixelů před tlačítkem MA Tabulka
    ObjectSetInteger(0, gripPointName, OBJPROP_YDISTANCE, buttonY); // Stejná Y-pozice jako tlačítko MA Tabulka

    // Aktualizace pozic tlačítek
    ObjectSetInteger(0, "ToggleMA", OBJPROP_XDISTANCE, cornerX + 0);
    ObjectSetInteger(0, "ToggleMA", OBJPROP_YDISTANCE, buttonY);

    ObjectSetInteger(0, "TFNext", OBJPROP_XDISTANCE, cornerX + 80); // Posunuto blíže (z 100)
    ObjectSetInteger(0, "TFNext", OBJPROP_YDISTANCE, buttonY);

    ObjectSetInteger(0, "TFPrev", OBJPROP_XDISTANCE, cornerX + 155); // Posunuto blíže (z 180)
    ObjectSetInteger(0, "TFPrev", OBJPROP_YDISTANCE, buttonY);

    ObjectSetInteger(0, "ToggleD", OBJPROP_XDISTANCE, cornerX);
    ObjectSetInteger(0, "ToggleD", OBJPROP_YDISTANCE, dButtonY);

    // Aktualizace hodnot a barev
    UpdateMAValues();
    UpdateDValues();
    FindClosestToCloseAndColorMap();

    // Překreslení tabulek na nové pozice
    if (showMATable) {
        DeleteTable("MACell");
        DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, maRows, maCols, cornerX, maTableY, clrWhite);
    }

    if (showDTable) {
        DeleteTable("DCell");
        DrawTable("DCell", D_labels_X, D_labels_Y, D_Values, D_Colors, D_FontSizes, dRows, dCols, cornerX, dTableY, clrWhite);
    }
}

/**
 * Hledá hodnoty v tabulkách, které jsou nejblíže aktuální ceně, a zvýrazňuje je.
 * Hodnoty v rozsahu g_HighlightDistance od aktuální ceny jsou označeny zelenou barvou.
 * Hodnota, která je nejblíže aktuální ceně, je zvýrazněna blikající barvou a větším písmem.
 */
void FindClosestToCloseAndColorMap()
{
   double dist = HighlightDistance;
   double closePrice = Close[0];
   double bestDiff = dist;
   int bestRow = -1, bestCol = -1;
   bool isMA = true;

   ArrayInitialize(MA_Colors, DefaultColor);
   ArrayInitialize(D_Colors, DefaultColor);
   ArrayInitialize(MA_FontSizes, DefaultFontSize);
   ArrayInitialize(D_FontSizes, DefaultFontSize);

   color blinkColor = blinkState ? BlinkColor : BlinkAltColor;

   // --- MA tabulka ---
   for (int ma_j = 0; ma_j < maRows; ma_j++)
   {
      for (int ma_i = 0; ma_i < maCols; ma_i++)
      {
         double val = MA_Values[ma_i][ma_j];
         double diff = MathAbs(val - closePrice);

         if (diff <= dist)
         {
            MA_Colors[ma_i][ma_j] = CloseRangeColor;

            if (diff < bestDiff)
            {
               bestDiff = diff;
               bestRow = ma_i;
               bestCol = ma_j;
               isMA = true;
            }
         }
      }
   }

   // --- D tabulka ---
   for (int d_j = 0; d_j < dRows; d_j++)
   {
      for (int d_i = 0; d_i < dCols; d_i++)
      {
         double val = D_Values[d_i][d_j];
         double diff = MathAbs(val - closePrice);

         if (diff <= dist)
         {
            D_Colors[d_i][d_j] = CloseRangeColor;

            if (diff < bestDiff)
            {
               bestDiff = diff;
               bestRow = d_i;
               bestCol = d_j;
               isMA = false;
            }
         }
      }
   }

   // --- Zvýraznění nejbližší hodnoty ---
   if (bestRow != -1 && bestCol != -1 && bestDiff < dist)
   {
      // Uložení hodnoty s jejím znaménkem (kladné nebo záporné)
      double bestValue;
      if (isMA)
         bestValue = MA_Values[bestRow][bestCol];
      else
         bestValue = D_Values[bestRow][bestCol];

      // Výpočet rozdílu se znaménkem (cena - hodnota)
      LastNearestDistance = closePrice - bestValue;

      if (isMA)
      {
         MA_Colors[bestRow][bestCol] = blinkColor;
         MA_FontSizes[bestRow][bestCol] = HighlightFontSize;

         Print("Nejbližší MA[", bestRow, "][", bestCol, "] = ",
               DoubleToString(MA_Values[bestRow][bestCol], 2),
               ", Close = ", DoubleToString(closePrice, 2),
               ", Diff = ", DoubleToString(LastNearestDistance, 2));
      }
      else
      {
         D_Colors[bestRow][bestCol] = blinkColor;
         D_FontSizes[bestRow][bestCol] = HighlightFontSize;

         Print("Nejbližší D[", bestRow, "][", bestCol, "] = ",
               DoubleToString(D_Values[bestRow][bestCol], 2),
               ", Close = ", DoubleToString(closePrice, 2),
               ", Diff = ", DoubleToString(LastNearestDistance, 2));
      }
   }
   else
   {
      LastNearestDistance = 0.0;
      Print("Žádná hodnota v rozsahu. Close = ", DoubleToString(closePrice, 2));
   }
}





/**
 * Zpracovává události grafu - přetažení GripPointu, změnu velikosti grafu a kliknutí na tlačítka.
 * @param id Identifikátor události
 * @param lparam Dlouhý parametr události
 * @param dparam Desetinný parametr události
 * @param sparam Textový parametr události
 */
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{

    Print("Event: id = ", id, ", sparam = ", sparam);

    // Detekce přetažení GripPointu
    if (id == CHARTEVENT_OBJECT_DRAG && sparam == gripPointName) {
        // Získání nových souřadnic GripPointu
        int gripX = (int)ObjectGetInteger(0, gripPointName, OBJPROP_XDISTANCE);
        int gripY = (int)ObjectGetInteger(0, gripPointName, OBJPROP_YDISTANCE);

        // Aktualizace hlavních souřadnic (cornerX, cornerY)
        // GripPoint je 15 pixelů před tlačítkem MA Tabulka, proto přičteme 15
        cornerX = gripX + 15;
        cornerY = gripY + 10;

        // Aktualizace všech prvků podle nové pozice
        UpdateAllElements();
    }

    // Detekce změny velikosti grafu
    if (id == CHARTEVENT_CHART_CHANGE) {
        int newWidth = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
        int newHeight = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);

        // Pokud se změnila velikost grafu, aktualizujeme pozice
        if (newWidth != chartWidth || newHeight != chartHeight) {
            chartWidth = newWidth;
            chartHeight = newHeight;

            // Automatické přizpůsobení velikosti buněk podle rozlišení
            AdjustCellSizeForResolution();

            // Vždy aktualizujeme pozici tabulky při změně velikosti grafu
            // aby se zabránilo situaci, kdy je tabulka mimo viditelnou oblast
            SetInitialCoordinates();
            UpdateAllElements();
        }
    }

    // Zpracování kliknutí na objekty
    if (id == CHARTEVENT_OBJECT_CLICK) {
        // Přepínání MA tabulky
        if (sparam == "ToggleMA") {
            showMATable = !showMATable; // Přepnutí stavu
            UpdateButtonColor("ToggleMA", showMATable); // Aktualizace barvy tlačítka

            // Výpočet vertikálního odsazení pro tlačítka a tabulky
            int buttonY = cornerY + 10; // Tlačítka jsou výše (zmenšeno z 20)
            int maTableY = buttonY + 20; // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)

            if (showMATable) {
                // Zobrazení tabulky
                // Aktualizace globálních proměnných pro velikosti polí
                maRows = ArraySize(MA_labels_Y);
                maCols = ArraySize(MA_labels_X);
                DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, maRows, maCols, cornerX, maTableY, clrWhite);
            } else {
                // Skrytí tabulky
                DeleteTable("MACell");
            }
        }

        // Přepínání D tabulky
        if (sparam == "ToggleD") {
            showDTable = !showDTable; // Přepnutí stavu
            UpdateButtonColor("ToggleD", showDTable); // Aktualizace barvy tlačítka

            // Výpočet vertikálního odsazení pro tlačítka a tabulky
            int buttonY = cornerY + 10; // Tlačítka jsou výše (zmenšeno z 20)
            int maTableY = buttonY + 20; // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)
            int dButtonY = maTableY + (currentCellHeight * 4) + 10; // D tlačítko je pod MA tabulkou s mezerou
            int dTableY = dButtonY + 20; // D tabulka začíná 20 pixelů pod D tlačítkem (zvětšeno z 15)

            if (showDTable) {
                // Zobrazení tabulky
                // Aktualizace globálních proměnných pro velikosti polí
                dRows = ArraySize(D_labels_Y);
                dCols = ArraySize(D_labels_X);
                DrawTable("DCell", D_labels_X, D_labels_Y, D_Values, D_Colors, D_FontSizes, dRows, dCols, cornerX, dTableY, clrWhite);
            } else {
                // Skrytí tabulky
                DeleteTable("DCell");
            }
        }

        // Posun timeframe dopředu
        if (sparam == "TFNext") {
            currentTFIndex++;
            if (currentTFIndex > 2) currentTFIndex = 0;
            UpdateTFLabels();

            if (showMATable) {
                // Výpočet vertikálního odsazení pro tlačítka a tabulky
                int buttonY = cornerY + 10; // Tlačítka jsou výše (zmenšeno z 20)
                int maTableY = buttonY + 20; // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)

                DeleteTable("MACell");
                UpdateMAValues();
                UpdateDValues();
                // Aktualizace globálních proměnných pro velikosti polí
                maRows = ArraySize(MA_labels_Y);
                maCols = ArraySize(MA_labels_X);
                DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, maRows, maCols, cornerX, maTableY, clrWhite);
            }
        }

        // Posun timeframe zpět
        if (sparam == "TFPrev") {
            currentTFIndex--;
            if (currentTFIndex < 0) currentTFIndex = 2;
            UpdateTFLabels();

            if (showMATable) {
                // Výpočet vertikálního odsazení pro tlačítka a tabulky
                int buttonY = cornerY + 10; // Tlačítka jsou výše (zmenšeno z 20)
                int maTableY = buttonY + 20; // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)

                DeleteTable("MACell");
                UpdateMAValues();
                UpdateDValues();
                // Aktualizace globálních proměnných pro velikosti polí
                maRows = ArraySize(MA_labels_Y);
                maCols = ArraySize(MA_labels_X);
                DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, maRows, maCols, cornerX, maTableY, clrWhite);
            }
        }
    }
}

// ----------------------------- ZIG ZAG -----------------------------------

void ZigZagInfo(
   double &zigzagValues[],
   int &zigzagIndexes[],
   string &outputMessages[],
   bool drawLabels,
   bool drawMiddleText
)
{
   int maxPoints = FileExport ? MathMax(EALastValues, FileGenLastValues) : EALastValues;
   ArrayResize(zigzagValues, maxPoints);
   ArrayResize(zigzagIndexes, maxPoints);
   ArrayResize(outputMessages, EALastValues);

   int foundPoints = LoadZigZagPoints(maxPoints, zigzagValues, zigzagIndexes);
   if (foundPoints == 0)
   {
      Print("? Žádné ZigZag body nenalezeny.");
      return;
   }

   if (FileExport)
      ExportZigZagData(foundPoints, zigzagValues, zigzagIndexes);

   PrintZigZagPreview(foundPoints, zigzagValues, zigzagIndexes, outputMessages);
}

int LoadZigZagPoints(int maxPoints, double &values[], int &indexes[])
{
   int found = 0, shift = 0;
   while (found < maxPoints && shift < Bars)
   {
      double val = iCustom(NULL, PERIOD_M5, "ZigZag", 12, 5, 3, 0, shift);
      if (val != 0.0)
      {
         values[found] = val;
         indexes[found] = shift;
         found++;
      }
      shift++;
   }
   return found;
}

void ExportZigZagData(int foundPoints, double &values[], int &indexes[])
{
   string dir = "ZigZagExport\\";
   string csvPath = dir + "ZigZag_Export.csv";
   string txtPath = dir + "ZigZag_Export.txt";

   int csvHandle = FileOpen(csvPath, FILE_WRITE | FILE_CSV, ',');
   int txtHandle = FileOpen(txtPath, FILE_WRITE | FILE_TXT);
   if (csvHandle == INVALID_HANDLE || txtHandle == INVALID_HANDLE)
   {
      Print("? Nelze otevřít výstupní soubory.");
      return;
   }

   FileWrite(csvHandle, "Datum a čas", "Index", "Hodnota", "Typ", "Rozdíl v pips", "Čas mezi body", "Rozdíl %");
   FileWriteString(txtHandle, "Datum a čas        Index   Hodnota     Typ       Rozdíl v pips   Čas mezi body    Rozdíl %\r\n");

   double sumPlus = 0.0, sumMinus = 0.0;
   int countPlus = 0, countMinus = 0;
   int timePlus = 0, timeMinus = 0;
   double maxRise = 0.0, maxDrop = 0.0;
   string maxRiseTime = "", maxDropTime = "";
   string maxRisePct = "", maxDropPct = "";
   double pointMultiplier = MathPow(10, Digits) / 100;

   for (int i = 1; i < foundPoints; i++)
   {
      double diff = (values[i - 1] - values[i]) * pointMultiplier;
      int seconds = (int)MathAbs(Time[indexes[i - 1]] - Time[indexes[i]]);
      int minutes = seconds / 60, hours = minutes / 60, days = hours / 24;
      hours %= 24; minutes %= 60;

      string dt = TimeToStringCustom(Time[indexes[i - 1]]);
      string valStr = DoubleToString(NormalizeDouble(values[i - 1], Digits), Digits);
      string type = (values[i - 1] > values[i]) ? "Maximum" : "Minimum";
      string timeStr = StringFormat("%d dní %02d:%02d", days, hours, minutes);
      string diffStr = StringFormat("%+.1f", diff);
      string pctStr = (values[i] != 0.0) ? StringFormat("%+.2f %%", ((values[i - 1] - values[i]) / values[i]) * 100.0) : "-";

      if (diff > 0)
      {
         sumPlus += diff; timePlus += seconds; countPlus++;
         if (diff > maxRise) { maxRise = diff; maxRiseTime = dt; maxRisePct = pctStr; }
      }
      else if (diff < 0)
      {
         double absD = MathAbs(diff);
         sumMinus += absD; timeMinus += seconds; countMinus++;
         if (absD > maxDrop) { maxDrop = absD; maxDropTime = dt; maxDropPct = pctStr; }
      }

      FileWrite(csvHandle, dt, indexes[i - 1], valStr, type, diffStr, timeStr, pctStr);
      FileWriteString(txtHandle, StringFormat("%-18s %-7d %-10s %-9s %-15s %-16s %s\r\n", dt, indexes[i - 1], valStr, type, diffStr, timeStr, pctStr));
   }

   FileWriteString(txtHandle, "\r\n--- Sumář ---\r\n");
   FileWriteString(txtHandle, StringFormat("Suma +: %.1f\r\n", sumPlus));
   FileWriteString(txtHandle, StringFormat("Suma -: %.1f\r\n", sumMinus));
   FileWriteString(txtHandle, StringFormat("Celkový rozdíl: %.1f\r\n", sumPlus - sumMinus));

   int avgPlus = (countPlus > 0) ? timePlus / countPlus : 0;
   int avgMinus = (countMinus > 0) ? timeMinus / countMinus : 0;

   FileWriteString(txtHandle, StringFormat("Průměrná délka trvání růstu: %s\r\n", FormatDuration(avgPlus)));
   FileWriteString(txtHandle, StringFormat("Průměrná délka trvání poklesu: %s\r\n", FormatDuration(avgMinus)));
   FileWriteString(txtHandle, StringFormat("Maximální růst: %s, %.1f bodů (%s)\r\n", maxRiseTime, maxRise, maxRisePct));
   FileWriteString(txtHandle, StringFormat("Maximální pokles: %s, %.1f bodů (%s)\r\n", maxDropTime, maxDrop, maxDropPct));

   FileClose(csvHandle);
   FileClose(txtHandle);

   RunExcelExport();
}

void PrintZigZagPreview(int foundPoints, double &values[], int &indexes[], string &messages[])
{
   double pointMultiplier = MathPow(10, Digits) / 100;
   for (int i = 0; i < MathMin(EALastValues, foundPoints); i++)
   {
      string zigzagType = "Neznámý";
      if (i < foundPoints - 1)
         zigzagType = (values[i] > values[i + 1]) ? "Maximum" : "Minimum";
      else if (i > 0)
         zigzagType = (values[i] > values[i - 1]) ? "Maximum" : "Minimum";

      string distanceInfo = "";
      if (i < foundPoints - 1)
      {
         double priceDistance = MathAbs(values[i + 1] - values[i]);
         int barDistance = MathAbs(indexes[i + 1] - indexes[i]);
         double pipDistance = priceDistance * pointMultiplier;
         double speed = (barDistance > 0) ? (pipDistance / barDistance) : 0;
         distanceInfo = StringFormat(", Vzdál.: %.1f, %d svíček, %.2f b/s", pipDistance, barDistance, speed);
      }

      messages[i] = StringFormat("ZigZag bod č. %d: Hodnota = %s, Index = %d, Typ = %s%s",
                                 i + 1,
                                 DoubleToString(values[i], Digits),
                                 indexes[i],
                                 zigzagType,
                                 distanceInfo);
   }
}

string TimeToStringCustom(datetime t)
{
   MqlDateTime dt;
   TimeToStruct(t, dt);
   return StringFormat("%02d.%02d.%04d %02d:%02d", dt.day, dt.mon, dt.year, dt.hour, dt.min);
}







string FormatDuration(int totalSec)
{
   int minutes = totalSec / 60;
   int hours = minutes / 60;
   int days = hours / 24;
   hours %= 24;
   minutes %= 60;
   return StringFormat("%d dnů, %02d hodin, %02d minut", days, hours, minutes);
}

void RunExcelExport() {
   string batRelative = "ZigZagExport\\export_excel.bat";
   string terminalPath = TerminalInfoString(TERMINAL_DATA_PATH);
   string batFullPath = terminalPath + "\\MQL4\\Files\\" + batRelative;

   ushort batPathUnicode[256];
   StringToShortArray(batFullPath, batPathUnicode);

   int result = ShellExecuteW(0, "open", batFullPath, "", "", 1);

   if (result <= 32)
      Print("Spuštění BAT selhalo, kód chyby: ", result);
   else
      Print("Spouštím BAT: ", batFullPath);
}

void RunPythonScript2()
{
   string batPath = TerminalInfoString(TERMINAL_DATA_PATH) + "\\MQL4\\Files\\ZigZagExport\\export_excel.bat";
   string commandStr = "cmd /c \"" + batPath + "\"";

   // Převod na char pole
   uchar commandArray[];
   StringToCharArray(commandStr, commandArray);

   Print("📦 Spouštím příkaz: ", commandStr);

   // Spuštění
   WinExec(commandArray, 1);  // 1 = SW_SHOWNORMAL
}




void ShowBigNumber(double zzdistance)
{
   string mainLabelName = "ZZ_MAIN_INFO";
   string timeLabelName = "ZZ_TIME_INFO";

   // ❗ Vynuluj zzLastDistance, pokud má jiné znaménko než zzdistance
   if (MathSign(zzdistance) != MathSign(zzLastDistance))
   {
      zzLastDistance = 0;
   }

   // Výpočet rozdílu od poslední hodnoty
   double difference = zzdistance - zzLastDistance;

   // Formátovaný text: aktuální vzdálenost a rozdíl
   string mainText = StringFormat("ZZ vzdál. M5: %.1f bodů (%+.1f)", zzdistance, difference);

   // Aktuální čas
   datetime now = TimeCurrent();
   string timeStr = TimeToString(now, TIME_SECONDS);
   string timeText = "Last Update: " + timeStr;

   // Nastavení pozice
   int corner = SelectedCorner;
   int offsetX = 30;
   int offsetY = 20;

   // Výběr barvy dle vzdálenosti
   color fontColor;
   double absDistance = MathAbs(zzdistance);

   if (absDistance > Min_ZZ_Distance2)
      fontColor = clrLime;       // ZELENÁ
   else if (absDistance > Min_ZZ_Distance1)
      fontColor = clrOrange;     // ORANŽOVÁ
   else
      fontColor = clrRed;        // ČERVENÁ

   // Debug výpis
   Print("Distance: ", zzdistance, ", LDist: ", zzLastDistance, "  Min1: ", Min_ZZ_Distance1, "  Min2: ", Min_ZZ_Distance2);

   // Uložení nové hodnoty
   zzLastDistance = zzdistance;

   // Fonty a velikosti
   string fontMain = "Arial Bold";
   string fontSmall = "Arial";
   int fontSizeMain = 18;
   int fontSizeSmall = 10;

   // Vykreslení hlavního textu
   ObjectDelete(mainLabelName);
   ObjectCreate(mainLabelName, OBJ_LABEL, 0, 0, 0);
   ObjectSetText(mainLabelName, mainText, fontSizeMain, fontMain, fontColor);
   ObjectSet(mainLabelName, OBJPROP_CORNER, corner);
   ObjectSet(mainLabelName, OBJPROP_XDISTANCE, offsetX);
   ObjectSet(mainLabelName, OBJPROP_YDISTANCE, offsetY);

   // Vykreslení času
   ObjectDelete(timeLabelName);
   ObjectCreate(timeLabelName, OBJ_LABEL, 0, 0, 0);
   ObjectSetText(timeLabelName, timeText, fontSizeSmall, fontSmall, fontColor);
   ObjectSet(timeLabelName, OBJPROP_CORNER, corner);
   ObjectSet(timeLabelName, OBJPROP_XDISTANCE, offsetX);
   ObjectSet(timeLabelName, OBJPROP_YDISTANCE, offsetY + fontSizeMain + 8);
}

int MathSign(double value)
{
   if (value > 0) return 1;
   if (value < 0) return -1;
   return 0;
}




//+------------------------------------------------------------------+
//| Funkce pro otevření obchodů                                     |
//+------------------------------------------------------------------+
void OpenOrder(ENUM_ORDER_TYPE type)
{
   double price = (type == ORDER_TYPE_BUY) ? Ask : Bid;
   double sl = (type == ORDER_TYPE_BUY) ? price - StopLoss * Point : price + StopLoss * Point;
   double tp = (type == ORDER_TYPE_BUY) ? price + TakeProfit * Point : price - TakeProfit * Point;

   OrderSend(_Symbol, type, 0.1, price, 3, sl, tp, "ZigZag + Stochastic Entry");
}

//+------------------------------------------------------------------+
//| Kontrola pozic                                                  |
//+------------------------------------------------------------------+
bool PositionExists(ENUM_ORDER_TYPE type)
{
   for (int i = 0; i < OrdersTotal(); i++)
   {
      if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if (OrderType() == type)
            return true;
      }
   }
   return false;
}

// Pomocná funkce: vykreslí šipku, popisek a středový text (dle parametrů)
void DrawZigZagPoint(
   int index,
   double price,
   int barIndex,
   string type,
   string distanceInfo,
   bool drawLabel = true,
   bool drawMidText = false,
   double nextPrice = 0,
   int nextBarIndex = 0
)
{
   string arrowName = "ZZ_Point_" + IntegerToString(index);
   string labelName = "ZZ_Label_" + IntegerToString(index);
   string distLabel = "ZZ_Dist_" + IntegerToString(index);

   ObjectDelete(arrowName);
   ObjectDelete(labelName);
   ObjectDelete(distLabel);

   double offset = MathMax(Point * 50, price * 0.002);
   double priceWithOffset = (type == "Maximum") ? price + offset : price - offset;
   double labelPrice = (type == "Maximum") ? priceWithOffset + offset * 0.8 : priceWithOffset - offset * 0.8;

   int shiftRight = 2;
   int timeIndex = MathMin(barIndex + shiftRight, Bars - 1);
   datetime labelTime = Time[timeIndex];

   ObjectCreate(arrowName, OBJ_ARROW, 0, Time[barIndex], priceWithOffset);
   ObjectSet(arrowName, OBJPROP_ARROWCODE, (type == "Maximum") ? 233 : 234);
   ObjectSet(arrowName, OBJPROP_COLOR, (type == "Maximum") ? Red : Blue);
   ObjectSet(arrowName, OBJPROP_WIDTH, 2);

   if (drawLabel)
   {
      string labelText = "#" + IntegerToString(index);
      if (distanceInfo != "")
         labelText += "\n" + distanceInfo;

      ObjectCreate(labelName, OBJ_TEXT, 0, labelTime, labelPrice);
      ObjectSetText(labelName, labelText, 10, "Arial", clrWhite);
      ObjectSet(labelName, OBJPROP_CORNER, 0);
   }

   if (drawMidText)
   {
      datetime midTime = (Time[barIndex] + Time[nextBarIndex]) / 2;
      double midPrice = (price + nextPrice) / 2;

      ObjectCreate(distLabel, OBJ_TEXT, 0, midTime, midPrice);
      ObjectSetText(distLabel, distanceInfo, 9, "Arial", clrYellow);
      ObjectSet(distLabel, OBJPROP_CORNER, 0);
   }
}



void DrawMA(string symbol, int timeframe, int period, int maShift, int maType, int appliedPrice, int shift, color lineColor, int lineStyle, int width, string maTypeName)
{
    // Název MA čáry bude odvozen od period, typu, symbolu a názvu typu MA
    maName = "MA_" + maTypeName + IntegerToString(period) + "_" + symbol + "_" + IntegerToString(timeframe);

    // Vytvořit MA čáru, pokud již neexistuje
    if (ObjectFind(0, maName) == -1)
    {
        if (!ObjectCreate(0, maName, OBJ_TREND, 0, Time[0], 0))
        {
            Print("Chyba při vytváření MA čáry: ", GetLastError());
            return;
        }

        // Nastavit základní vlastnosti MA čáry
        ObjectSetInteger(0, maName, OBJPROP_COLOR, lineColor);
        ObjectSetInteger(0, maName, OBJPROP_STYLE, lineStyle);
        ObjectSetInteger(0, maName, OBJPROP_WIDTH, width);
        ObjectSetInteger(0, maName, OBJPROP_RAY_RIGHT, false);
    }

    // Získání hodnoty MA pro zadaný symbol, časový rámec a další parametry
    double maValueCurrent = iMA(symbol, timeframe, period, maShift, maType, appliedPrice, shift);
    double maValuePrevious = iMA(symbol, timeframe, period, maShift, maType, appliedPrice, shift + 1);

    // Nastavení souřadnic pro MA čáru mezi předchozí a aktuální hodnotou
    datetime previousH1BarTime = iTime(symbol, period, 1);
    datetime currentTime = Time[maShift];
    datetime previousTime = Time[maShift + 1];

    ObjectMove(0, maName, maShift, previousH1BarTime, maValuePrevious);
    ObjectMove(0, maName, maShift + 1, currentTime, maValueCurrent);
}

//+------------------------------------------------------------------+
//| Funkce pro vykreslení horizontální čáry s popiskem               |
//+------------------------------------------------------------------+
void DrawHorizontalLineWithLabel(string name, double price, color lineColor, ENUM_LINE_STYLE style, int width, string labelText)
{

    // Název HLine čáry
    HLineName = "HLine_" + name;
    
    // Vytvoření čáry nebo aktualizace existující
    if (ObjectFind(0, HLineName) == -1)
    {
        ObjectCreate(0, HLineName, OBJ_HLINE, 0, 0, 0);
    }
    ObjectSetDouble(0, HLineName, OBJPROP_PRICE, price);  // Nastavení ceny čáry
    ObjectSetInteger(0, HLineName, OBJPROP_COLOR, lineColor);  // Nastavení barvy čáry
    ObjectSetInteger(0, HLineName, OBJPROP_STYLE, style);  // Nastavení stylu čáry
    ObjectSetInteger(0, HLineName, OBJPROP_WIDTH, width);  // Nastavení šířky čáry
    
    // Vytvoření nebo aktualizace popisku pro čáru
    string labelName = "HLine_Label_" + name;
    if (ObjectFind(0, labelName) == -1)
    {
        ObjectCreate(0, labelName, OBJ_TEXT, 0, Time[0], price);  // Vytvoření nového popisku
    }
    else
    {
        ObjectMove(0, labelName, 0, Time[0], price);  // Přesunutí existujícího popisku
    }
    ObjectSetInteger(0, labelName, OBJPROP_COLOR, lineColor);  // Nastavení barvy popisku
    ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 10);  // Nastavení velikosti písma popisku
    ObjectSetString(0, labelName, OBJPROP_TEXT, labelText);  // Nastavení textu popisku
    ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_LEFT_LOWER);  // Nastavení pozice popisku
}
