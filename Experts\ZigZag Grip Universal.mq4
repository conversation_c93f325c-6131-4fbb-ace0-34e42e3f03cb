//+------------------------------------------------------------------+
//| GripPoint a Tabulka - Univerzální verze                          |
//+------------------------------------------------------------------+
#property strict
#property copyright "Traderpoint"
#property version   "1.00"

// Enum pro volbu rohu
enum ChartCorner
{
   LeftUpper = 0,  // <PERSON><PERSON> horní roh
   RightUpper = 1, // Pravý horní roh
   LeftLower = 2,  // Levý dolní roh
   RightLower = 3  // Pravý dolní roh
};

// Vstupní parametry
input ChartCorner g_Corner = LeftUpper;  // Umístění v rohu
input int g_CellWidth = 70;              // Šíř<PERSON> buňky
input int g_CellHeight = 18;             // Výška buňky
input color g_GripColor = clrYellow;     // Barva GripPointu
input color g_CellColor = clrWhite;      // Barva buněk
input color g_TextColor = clrBlack;      // Barva textu

// Globální proměnné
int cornerX, cornerY;                    // Pozice GripPointu
string gripPointName = "GripPoint";      // Název GripPointu
int chartWidth, chartHeight;             // Rozměry grafu

//+------------------------------------------------------------------+
//| Expert initialization function                                    |
//+------------------------------------------------------------------+
int OnInit()
{
   // Zjištění rozměrů grafu
   chartWidth = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
   chartHeight = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);
   
   // Nastavení výchozích souřadnic podle zvoleného rohu
   SetInitialCoordinates();
   
   // Vytvoření GripPointu
   CreateGripPoint();
   
   // Vykreslení tabulky
   DrawTable();
   
   // Nastavení události pro změnu velikosti grafu
   ChartSetInteger(0, CHART_EVENT_OBJECT_CREATE, true);
   ChartSetInteger(0, CHART_EVENT_OBJECT_DELETE, true);
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                  |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Odstranění všech objektů
   ObjectDelete(0, gripPointName);
   DeleteTable();
}

//+------------------------------------------------------------------+
//| Expert tick function                                              |
//+------------------------------------------------------------------+
void OnTick()
{
   // Není potřeba nic dělat při každém ticku
}

//+------------------------------------------------------------------+
//| ChartEvent function                                               |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
   // Detekce přetažení GripPointu
   if(id == CHARTEVENT_OBJECT_DRAG && sparam == gripPointName)
   {
      // Získání nových souřadnic GripPointu
      cornerX = (int)ObjectGetInteger(0, gripPointName, OBJPROP_XDISTANCE);
      cornerY = (int)ObjectGetInteger(0, gripPointName, OBJPROP_YDISTANCE);
      
      // Překreslení tabulky na nové pozici
      DrawTable();
   }
   
   // Detekce změny velikosti grafu
   if(id == CHARTEVENT_CHART_CHANGE)
   {
      int newWidth = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
      int newHeight = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);
      
      // Pokud se změnila velikost grafu, aktualizujeme pozice
      if(newWidth != chartWidth || newHeight != chartHeight)
      {
         chartWidth = newWidth;
         chartHeight = newHeight;
         
         // Aktualizace pozice GripPointu podle nové velikosti grafu
         UpdateGripPointPosition();
         
         // Překreslení tabulky
         DrawTable();
      }
   }
}

//+------------------------------------------------------------------+
//| Nastavení výchozích souřadnic podle zvoleného rohu               |
//+------------------------------------------------------------------+
void SetInitialCoordinates()
{
   // Výchozí offsety od rohů
   int offsetX = 20;
   int offsetY = 20;
   
   // Nastavení pozice GripPointu podle zvoleného rohu
   switch(g_Corner)
   {
      case LeftUpper:
         cornerX = offsetX;
         cornerY = offsetY;
         break;
      case RightUpper:
         cornerX = chartWidth - offsetX;
         cornerY = offsetY;
         break;
      case LeftLower:
         cornerX = offsetX;
         cornerY = chartHeight - offsetY;
         break;
      case RightLower:
         cornerX = chartWidth - offsetX;
         cornerY = chartHeight - offsetY;
         break;
   }
}

//+------------------------------------------------------------------+
//| Aktualizace pozice GripPointu při změně velikosti grafu          |
//+------------------------------------------------------------------+
void UpdateGripPointPosition()
{
   // Získání aktuální pozice GripPointu
   int currentX = (int)ObjectGetInteger(0, gripPointName, OBJPROP_XDISTANCE);
   int currentY = (int)ObjectGetInteger(0, gripPointName, OBJPROP_YDISTANCE);
   
   // Výpočet nové pozice podle zvoleného rohu
   switch(g_Corner)
   {
      case LeftUpper:
         // Pozice zůstává stejná
         cornerX = currentX;
         cornerY = currentY;
         break;
      case RightUpper:
         // Zachování vzdálenosti od pravého okraje
         cornerX = chartWidth - (chartWidth - currentX);
         cornerY = currentY;
         break;
      case LeftLower:
         // Zachování vzdálenosti od spodního okraje
         cornerX = currentX;
         cornerY = chartHeight - (chartHeight - currentY);
         break;
      case RightLower:
         // Zachování vzdálenosti od pravého a spodního okraje
         cornerX = chartWidth - (chartWidth - currentX);
         cornerY = chartHeight - (chartHeight - currentY);
         break;
   }
   
   // Aktualizace pozice GripPointu
   ObjectSetInteger(0, gripPointName, OBJPROP_XDISTANCE, cornerX);
   ObjectSetInteger(0, gripPointName, OBJPROP_YDISTANCE, cornerY);
}

//+------------------------------------------------------------------+
//| Vytvoření GripPointu                                             |
//+------------------------------------------------------------------+
void CreateGripPoint()
{
   if(ObjectFind(0, gripPointName) >= 0)
      ObjectDelete(0, gripPointName);
      
   ObjectCreate(0, gripPointName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, gripPointName, OBJPROP_CORNER, CORNER_LEFT_UPPER); // Vždy z levého horního rohu
   ObjectSetInteger(0, gripPointName, OBJPROP_XDISTANCE, cornerX);
   ObjectSetInteger(0, gripPointName, OBJPROP_YDISTANCE, cornerY);
   ObjectSetInteger(0, gripPointName, OBJPROP_XSIZE, 15);
   ObjectSetInteger(0, gripPointName, OBJPROP_YSIZE, 15);
   ObjectSetInteger(0, gripPointName, OBJPROP_BGCOLOR, g_GripColor);
   ObjectSetInteger(0, gripPointName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, gripPointName, OBJPROP_BORDER_COLOR, clrBlack);
   ObjectSetInteger(0, gripPointName, OBJPROP_SELECTABLE, true);
   ObjectSetInteger(0, gripPointName, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, gripPointName, OBJPROP_HIDDEN, false);
   ObjectSetString(0, gripPointName, OBJPROP_TOOLTIP, "Táhněte pro posun tabulky");
}

//+------------------------------------------------------------------+
//| Vykreslení tabulky 5x3                                           |
//+------------------------------------------------------------------+
void DrawTable()
{
   // Nejprve smažeme starou tabulku
   DeleteTable();
   
   // Určení offsetu pro tabulku podle GripPointu a rohu
   int tableX, tableY;
   
   // Nastavení pozice tabulky podle rohu
   switch(g_Corner)
   {
      case LeftUpper:
         tableX = cornerX + 20;
         tableY = cornerY + 20;
         break;
      case RightUpper:
         tableX = cornerX - (3*g_CellWidth + 20);
         tableY = cornerY + 20;
         break;
      case LeftLower:
         tableX = cornerX + 20;
         tableY = cornerY - (5*g_CellHeight + 20);
         break;
      case RightLower:
         tableX = cornerX - (3*g_CellWidth + 20);
         tableY = cornerY - (5*g_CellHeight + 20);
         break;
   }
   
   // Vykreslení tabulky 5x3
   for(int row = 0; row < 5; row++)
   {
      for(int col = 0; col < 3; col++)
      {
         string cellName = "TableCell_" + IntegerToString(row) + "_" + IntegerToString(col);
         
         // Výpočet pozice buňky
         int cellX = tableX + col * g_CellWidth;
         int cellY = tableY + row * g_CellHeight;
         
         // Vytvoření buňky (obdélníkový štítek)
         ObjectCreate(0, cellName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
         ObjectSetInteger(0, cellName, OBJPROP_CORNER, CORNER_LEFT_UPPER); // Vždy z levého horního rohu
         ObjectSetInteger(0, cellName, OBJPROP_XDISTANCE, cellX);
         ObjectSetInteger(0, cellName, OBJPROP_YDISTANCE, cellY);
         ObjectSetInteger(0, cellName, OBJPROP_XSIZE, g_CellWidth);
         ObjectSetInteger(0, cellName, OBJPROP_YSIZE, g_CellHeight);
         ObjectSetInteger(0, cellName, OBJPROP_BGCOLOR, g_CellColor);
         ObjectSetInteger(0, cellName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
         ObjectSetInteger(0, cellName, OBJPROP_BORDER_COLOR, clrBlack);
         
         // Vytvoření textu v buňce
         string textName = "TableText_" + IntegerToString(row) + "_" + IntegerToString(col);
         string cellText = "R" + IntegerToString(row+1) + "C" + IntegerToString(col+1);
         
         ObjectCreate(0, textName, OBJ_LABEL, 0, 0, 0);
         ObjectSetInteger(0, textName, OBJPROP_CORNER, CORNER_LEFT_UPPER); // Vždy z levého horního rohu
         ObjectSetInteger(0, textName, OBJPROP_XDISTANCE, cellX + 5);
         ObjectSetInteger(0, textName, OBJPROP_YDISTANCE, cellY + 3);
         ObjectSetString(0, textName, OBJPROP_TEXT, cellText);
         ObjectSetInteger(0, textName, OBJPROP_COLOR, g_TextColor);
         ObjectSetInteger(0, textName, OBJPROP_FONTSIZE, 8);
      }
   }
}

//+------------------------------------------------------------------+
//| Smazání tabulky                                                  |
//+------------------------------------------------------------------+
void DeleteTable()
{
   for(int i = ObjectsTotal(0, -1, -1) - 1; i >= 0; i--)
   {
      string objName = ObjectName(0, i, -1, -1);
      if(StringFind(objName, "TableCell_") == 0 || StringFind(objName, "TableText_") == 0)
         ObjectDelete(0, objName);
   }
}
