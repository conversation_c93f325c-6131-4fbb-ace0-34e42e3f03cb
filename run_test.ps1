# Spuštění testu sčítání na otevřené tabulce

try {
    Write-Host "=== SPUŠTĚNÍ TESTU SČÍTÁNÍ ==="
    
    # Připoj se k běžící instanci Excelu
    $excel = [System.Runtime.InteropServices.Marshal]::GetActiveObject("Excel.Application")
    
    if ($excel) {
        Write-Host "Připojen k běžící instanci Excelu"
        
        # Získej aktivní workbook
        $workbook = $excel.ActiveWorkbook
        Write-Host "Aktivní workbook: $($workbook.Name)"
        
        # Načti test modul
        $testModulePath = "$PWD\test_summing.bas"
        if (Test-Path $testModulePath) {
            Write-Host "Načítám test modul..."
            
            # Přidej modul
            $vbaProject = $workbook.VBProject
            $testModule = $vbaProject.VBComponents.Add(1)  # 1 = vbext_ct_StdModule
            
            # Na<PERSON>ti obsah modulu
            $moduleContent = Get-Content $testModulePath -Raw
            $testModule.CodeModule.AddFromString($moduleContent)
            
            Write-Host "Spouštím test makro..."
            $excel.Run("TestSummingLogic")
            
            Write-Host "Test dokončen!"
        } else {
            Write-Host "Test modul nenalezen: $testModulePath"
        }
    } else {
        Write-Host "Excel není spuštěn nebo není dostupný"
    }
    
} catch {
    Write-Host "Chyba: $($_.Exception.Message)"
}
