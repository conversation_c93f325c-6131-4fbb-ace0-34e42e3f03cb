//+------------------------------------------------------------------+
//|                        ZigZagEA.mq4                              |
//|  EA s filtrem EMA100, EMA200, SMA200, ZigZag pivot a volitelnymi  |
//|  TP/SL: statickym (body), dynamickym EMA100 ci ATR(14)            |
//+------------------------------------------------------------------+
#property strict

// Vstupni parametry
input int   Depth            = 6;      // ZigZag depth (bars)
input int   Deviation        = 5;      // ZigZag deviation (bodů)
input int   Backstep         = 3;      // ZigZag backstep (bars)
input double Lots            = 0.1;    // Velikost lotu

// Staticke TP a SL (v bodech)
input int   StopLossPoints   = 140;    // Stop loss v bodech
input int   TakeProfitPoints = 1440;   // Staticky TP v bodech

// Dynamicke EMA100 TP
input bool  UseDynamicTP     = false;  // Pouzit EMA100 jako TP
input bool  UseMaxStaticEMA  = false;  // Pouzit max(static TP, EMA100 TP)

// ATR(14) D1 pro volatilitu
input bool  UseATRBased      = false;  // Pouzit ATR(14) pro SL/TP
input double ATRSLMult       = 1.0;    // multiplikator ATR pro SL
input double ATRTPMult       = 2.0;    // multiplikator ATR pro TP
input bool  UseMaxStaticATR  = false;  // max(stat. TP, ATR-based TP)

input int   MaxBarsHeld      = 100;    // Max pocet baru drzeni pozice
input bool  UseTrendFilter   = true;   // Zapnout EMA/SMA filtr
input int   MagicNumber      = 20250417; // Magic number pro objednavky

//------------------------------------------------------------------
int OnInit()
{
   return(INIT_SUCCEEDED);
}

//------------------------------------------------------------------
void OnDeinit(const int reason)
{
}

//------------------------------------------------------------------
void OnTick()
{
   static datetime lastBar = 0;
   if(Time[0] == lastBar) return;
   lastBar = Time[0];

   // pouze jedna otevrena pozice
   for(int i=OrdersTotal()-1; i>=0; i--)
      if(OrderSelect(i,SELECT_BY_POS) && OrderMagicNumber()==MagicNumber)
         return;

   // ZigZag body na H1
   double zz0 = iCustom(NULL, PERIOD_H1, "ZigZag", Depth, Deviation, Backstep, 0, 0);
   double zz1 = iCustom(NULL, PERIOD_H1, "ZigZag", Depth, Deviation, Backstep, 0, 1);
   double zz2 = iCustom(NULL, PERIOD_H1, "ZigZag", Depth, Deviation, Backstep, 0, 2);

   if(zz1>0 && zz1<zz0 && zz1<zz2)
   {
      bool trendOk=true;
      if(UseTrendFilter)
      {
         double ema100 = iMA(NULL, PERIOD_H1, 100, 0, MODE_EMA, PRICE_CLOSE, 1);
         double ema200 = iMA(NULL, PERIOD_H1, 200, 0, MODE_EMA, PRICE_CLOSE, 1);
         double sma200 = iMA(NULL, PERIOD_H1, 200, 0, MODE_SMA, PRICE_CLOSE, 1);
         trendOk = (ema100>ema200) && (ema200>sma200) && (zz1> sma200);
      }
      if(trendOk)
      {
         double entry = NormalizeDouble(Ask, Digits);
         double sl    = NormalizeDouble(entry - StopLossPoints * Point, Digits);
         double tp    = NormalizeDouble(entry + TakeProfitPoints * Point, Digits);

         if(UseDynamicTP)
         {
            double emaTP = NormalizeDouble(iMA(NULL,PERIOD_H1,100,0,MODE_EMA,PRICE_CLOSE,1), Digits);
            tp = emaTP;
            if(UseMaxStaticEMA)
            {
               double staticTP = NormalizeDouble(entry + TakeProfitPoints * Point, Digits);
               if(staticTP > tp) tp = staticTP;
            }
         }

         if(UseATRBased)
         {
            double atr = iATR(NULL, PERIOD_D1, 14, 1) * Point;
            double slAtr = NormalizeDouble(entry - atr * ATRSLMult, Digits);
            double tpAtr = NormalizeDouble(entry + atr * ATRTPMult, Digits);
            sl = slAtr;
            tp = tpAtr;
            if(UseMaxStaticATR)
            {
               double staticTP = NormalizeDouble(entry + TakeProfitPoints * Point, Digits);
               if(staticTP > tp) tp = staticTP;
            }
         }

         int ticket = OrderSend(Symbol(), OP_BUY, Lots, entry, 0, sl, tp,
                                "ZigZagEA", MagicNumber, 0, clrBlue);
         if(ticket<0) Print("Send error ", GetLastError());
      }
   }

   // exit podminky
   for(int i=OrdersTotal()-1; i>=0; i--)
   {
      if(OrderSelect(i,SELECT_BY_POS) && OrderMagicNumber()==MagicNumber)
      {
         int bars = Bars - iBarShift(NULL,PERIOD_H1,OrderOpenTime());
         if(bars > MaxBarsHeld)
            OrderClose(OrderTicket(), OrderLots(), Bid, 0, clrRed);
      }
   }
}
