import pandas as pd
import os

try:
    # Změň pracovní adresář
    os.chdir(r'c:\Users\<USER>\OneDrive\Trading\DVL\Excel transfers\Transfer data 3.0')
    
    # Načti Excel soubor
    df = pd.read_excel('Tabulka sloupců.xlsx')
    
    print('=== TABULKA SLOUPCŮ ===')
    print(df.to_string(index=False))
    
    print('\n=== INFORMACE O SLOUPCÍCH ===')
    print(f'Počet řádků: {len(df)}')
    print(f'Sloupce: {list(df.columns)}')
    
except Exception as e:
    print(f'Chyba: {e}')
    import traceback
    traceback.print_exc()
