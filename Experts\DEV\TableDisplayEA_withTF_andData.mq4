
//+------------------------------------------------------------------+
//|                                                   TableDisplayEA |
//|                      Copyright 2024, Custom Scripts              |
//+------------------------------------------------------------------+
#property strict

// Enum pro volbu rohu
enum ChartCorner
{
   LeftUpper = 0,  // Levý horní roh
   RightUpper = 1, // Pravý horní roh
   LeftLower = 2,  // Levý dolní roh
   RightLower = 3  // Pravý dolní roh
};

// --- Vstupní proměnné pro pozice a vzhled ---
input ChartCorner g_Corner = LeftUpper;  // Umístění v rohu
input int g_CellWidth = 60;              // <PERSON><PERSON><PERSON><PERSON> buňky
input int g_CellHeight = 16;             // Výška buňky
input color g_GripColor = clrYellow;     // <PERSON><PERSON> GripPointu
input int g_TableMargin = 20;            // Okraj od okraje grafu
input bool g_AutoAdjustSize = true;      // Automaticky přizpůsobit velikost rozlišení

// --- Vstupní proměnné pro zvýraznění buněk ---
input double g_HighlightDistance = 1.5;  // Minimální vzdálenost od ceny pro zvýraznění

// Aktuální hodnoty velikosti buněk (mohou být změněny podle rozlišení)
int currentCellWidth;
int currentCellHeight;

string MA_labels_Y[3] = {"M5", "H1", "H4"};
string D_labels_Y[2] = {"D0", "D1"};

// Proměnné pro stav zobrazení tabulek
bool showMATable = true;
bool showDTable = true;

// Proměnné pro pozice
int cornerX = 20;  // Výchozí X pozice řídícího bodu
int cornerY = 50;  // Výchozí Y pozice řídícího bodu
string gripPointName = "GripPoint"; // Název GripPointu
int chartWidth, chartHeight;        // Rozměry grafu

// Proměnné pro blikání a zvýraznění buněk
datetime lastBlinkTime = 0;
bool blinkState = false;
double g_LastNearestDistance = 0;                     // Nejmenší vzdálenost od Close(1) z funkce FindClosestToCloseAndColorMap
input color g_DefaultColor = clrWhite;                // Výchozí barva buňky
input color g_CloseRangeColor = clrGreen;             // Barva blízké hodnoty
input color g_BlinkColor = clrRed;                    // Barva blikající hodnoty
input color g_BlinkAltColor = clrWhite;               // Alternativní barva při blikání
input int g_DefaultFontSize = 10;                     // Výchozí velikost písma v tabulce
input int g_HighlightFontSize = 12;                   // Zvýrazněná velikost písma
input int g_BlinkIntervalSec = 1;                     // Interval blikání v sekundách

struct LabelInfo {
   string text;
   color  clr;
};

LabelInfo MA_labels_X[4] = {
   {"EMA21",  clrBlue},
   {"EMA100", clrViolet},
   {"EMA200", clrOrangeRed},
   {"SMA200", clrDarkOrange}
};

LabelInfo D_labels_X[3] = {
   {"High", clrLightSalmon},
   {"Low",  clrLightSalmon},
   {"HL50", clrViolet}
};

double MA_Values[4][3];
color MA_Colors[4][3];
int MA_FontSizes[4][3];

double D_Values[3][2];
color D_Colors[3][2];
int D_FontSizes[3][2];


string TF_labels[3][3] = {
    {"M5", "H1", "H4"},
    {"M15", "H4", "D1"},
    {"H1", "H4", "W1"}
};
int currentTFIndex = 0;

/**
 * Aktualizuje popisky časových rámců v MA tabulce podle aktuálně vybraného indexu časového rámce.
 * Kopíruje hodnoty z aktuálního řádku TF_labels do MA_labels_Y.
 */
void UpdateTFLabels() {
    for (int i = 0; i < 3; i++)
        MA_labels_Y[i] = TF_labels[currentTFIndex][i];
}


/**
 * Aktualizuje hodnoty klouzavých průměrů v MA tabulce.
 * Vypočítá hodnoty EMA21, EMA100, EMA200 a SMA200 pro všechny časové rámce
 * definované v aktuálním řádku TF_labels.
 */
void UpdateMAValues() {
    int periods[4] = {21, 100, 200, 200};
    int maTypes[4] = {MODE_EMA, MODE_EMA, MODE_EMA, MODE_SMA};
    for (int i = 0; i < 4; i++) {
        for (int j = 0; j < 3; j++) {
            int tf = TFStrToPeriod(TF_labels[currentTFIndex][j]);
            MA_Values[i][j] = iMA(NULL, tf, periods[i], 0, maTypes[i], PRICE_CLOSE, 1);
        }
    }
}

/**
 * Aktualizuje hodnoty v D tabulce (High, Low, HL50).
 * Vypočítá hodnoty High, Low a HL50 (střed mezi High a Low) pro aktuální a předchozí den.
 */
void UpdateDValues() {
    for (int i = 0; i < 2; i++) {
        double high = iHigh(NULL, PERIOD_D1, i);
        double low = iLow(NULL, PERIOD_D1, i);
        double hl50 = high - ((high - low) / 2.0);
        D_Values[0][i] = high;
        D_Values[1][i] = low;
        D_Values[2][i] = hl50;
    }
}

/**
 * Převádí textový řetězec časového rámce na odpovídající číselnou konstantu.
 * @param tf Textový řetězec časového rámce (např. "M1", "H1", "D1")
 * @return Číselná konstanta časového rámce (např. PERIOD_M1, PERIOD_H1, PERIOD_D1)
 */
int TFStrToPeriod(string tf) {
    if (tf == "M1") return PERIOD_M1;
    if (tf == "M5") return PERIOD_M5;
    if (tf == "M15") return PERIOD_M15;
    if (tf == "M30") return PERIOD_M30;
    if (tf == "H1") return PERIOD_H1;
    if (tf == "H4") return PERIOD_H4;
    if (tf == "D1") return PERIOD_D1;
    if (tf == "W1") return PERIOD_W1;
    return Period();
}

// Nastavení výchozích souřadnic podle zvoleného rohu
/**
 * Nastaví výchozí souřadnice pro umístění tabulek a ovládacích prvků podle zvoleného rohu grafu.
 * Zajišťuje, aby tabulky byly vždy viditelné a nepřesahovaly okraje grafu.
 */
void SetInitialCoordinates()
{
    // Zjištění rozměrů grafu
    chartWidth = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
    chartHeight = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);

    // Výpočet celkové šířky tabulky (pro MA tabulku, která je větší)
    int totalTableWidth = currentCellWidth * 5 + 10; // 5 sloupců pro MA tabulku (4 + 1 pro popisky) + malá rezerva
    int totalTableHeight = currentCellHeight * 10; // přibližná celková výška obou tabulek s tlačítky

    // Použití uživatelského nastavení okraje
    int offsetX = g_TableMargin;
    int offsetY = g_TableMargin;

    // Minimální vzdálenost od horního okraje grafu (aby tabulka nebyla příliš vysoko)
    int minTopMargin = (int)(chartHeight * 0.05); // 5% výšky grafu
    if (minTopMargin < 30) minTopMargin = 30; // Minimálně 30 pixelů od horního okraje

    // Minimální vzdálenost od spodního okraje grafu (aby tabulka nebyla příliš nízko)
    int minBottomMargin = (int)(chartHeight * 0.05); // 5% výšky grafu
    if (minBottomMargin < 30) minBottomMargin = 30; // Minimálně 30 pixelů od spodního okraje

    // Nastavení pozice GripPointu podle zvoleného rohu
    switch(g_Corner)
    {
        case LeftUpper:
            cornerX = offsetX;
            cornerY = minTopMargin; // Použití minimální vzdálenosti od horního okraje
            break;
        case RightUpper:
            // Zajistíme, aby tabulka nebyla mimo viditelnou oblast
            cornerX = chartWidth - totalTableWidth - 5; // Posunuto téměř k pravému okraji (pouze 5 pixelů od okraje)
            cornerY = minTopMargin; // Použití minimální vzdálenosti od horního okraje
            break;
        case LeftLower:
            cornerX = offsetX;
            cornerY = chartHeight - totalTableHeight - minBottomMargin;
            break;
        case RightLower:
            // Zajistíme, aby tabulka nebyla mimo viditelnou oblast
            cornerX = chartWidth - totalTableWidth - 5; // Posunuto téměř k pravému okraji (pouze 5 pixelů od okraje)
            cornerY = chartHeight - totalTableHeight - minBottomMargin;
            break;
    }

    // Kontrola, aby tabulka nebyla mimo viditelnou oblast
    if (cornerX < 0) cornerX = 0;
    if (cornerY < 0) cornerY = 0;

    // Zajistíme, aby tabulka byla vždy alespoň částečně viditelná
    int minVisibleWidth = MathMin(100, totalTableWidth / 2);
    int minVisibleHeight = MathMin(50, totalTableHeight / 2);

    if (cornerX > chartWidth - minVisibleWidth)
        cornerX = chartWidth - minVisibleWidth;
    if (cornerY > chartHeight - minVisibleHeight)
        cornerY = chartHeight - minVisibleHeight;
}

// Vytvoření GripPointu pro přesouvání
/**
 * Vytvoří GripPoint - malé tlačítko, které slouží k přesouvání tabulek a ovládacích prvků.
 * GripPoint je umístěn před tlačítkem MA Tabulka a má stejnou Y-pozici.
 */
void CreateGripPoint()
{
    if(ObjectFind(0, gripPointName) >= 0)
        ObjectDelete(0, gripPointName);

    // Výpočet pozice GripPointu - před tlačítkem MA Tabulka
    int buttonY = cornerY + 20;

    ObjectCreate(0, gripPointName, OBJ_BUTTON, 0, 0, 0);
    ObjectSetInteger(0, gripPointName, OBJPROP_CORNER, 0);
    ObjectSetInteger(0, gripPointName, OBJPROP_XDISTANCE, cornerX - 15); // 15 pixelů před tlačítkem MA Tabulka
    ObjectSetInteger(0, gripPointName, OBJPROP_YDISTANCE, buttonY); // Stejná Y-pozice jako tlačítko MA Tabulka
    ObjectSetInteger(0, gripPointName, OBJPROP_XSIZE, 10);
    ObjectSetInteger(0, gripPointName, OBJPROP_YSIZE, 10);
    ObjectSetInteger(0, gripPointName, OBJPROP_BGCOLOR, g_GripColor);
    ObjectSetInteger(0, gripPointName, OBJPROP_BORDER_COLOR, clrBlack);
    ObjectSetInteger(0, gripPointName, OBJPROP_HIDDEN, false);
    ObjectSetInteger(0, gripPointName, OBJPROP_SELECTABLE, true);
    ObjectSetString(0, gripPointName, OBJPROP_TEXT, "");
}

/**
 * Automaticky přizpůsobí velikost buněk a pozici tabulek podle rozlišení grafu.
 * Menší grafy mají menší buňky, větší grafy mají větší buňky.
 * Také zajišťuje, aby tabulky byly vždy viditelné a nepřesahovaly okraje grafu.
 */
void AdjustCellSizeForResolution()
{
    if (g_AutoAdjustSize) {
        // Zjištění rozměrů grafu
        int width = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
        int height = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);

        // Přizpůsobení velikosti buněk podle šířky grafu
        if (width < 800) {
            // Pro malé rozlišení
            currentCellWidth = 50; // Zmenšeno z 60
            currentCellHeight = 14;
        } else if (width < 1200) {
            // Pro střední rozlišení
            currentCellWidth = 60; // Zmenšeno z 70
            currentCellHeight = 16;
        } else {
            // Pro vysoké rozlišení
            currentCellWidth = 70; // Zmenšeno z 80
            currentCellHeight = 18;
        }

        // Zajištění, aby tabulka nebyla příliš nízko na grafu
        // Pokud je tabulka v horní části grafu, posuneme ji níže od horního okraje
        if (g_Corner == LeftUpper || g_Corner == RightUpper) {
            // Pro horní rohy - posuneme tabulku níže od horního okraje
            int minTopMargin = (int)(height * 0.05); // 5% výšky grafu jako minimální horní okraj
            if (cornerY < minTopMargin) {
                cornerY = minTopMargin;
            }
        } else {
            // Pro dolní rohy - zajistíme, aby tabulka nebyla příliš blízko spodního okraje
            int totalTableHeight = currentCellHeight * 10; // přibližná celková výška obou tabulek s tlačítky
            int minBottomMargin = (int)(height * 0.05); // 5% výšky grafu jako minimální spodní okraj

            if (cornerY > height - totalTableHeight - minBottomMargin) {
                cornerY = height - totalTableHeight - minBottomMargin;
            }
        }
    }
}

int OnInit()
{
    // Inicializace aktuálních hodnot velikosti buněk z vstupních proměnných
    currentCellWidth = g_CellWidth;
    currentCellHeight = g_CellHeight;

    // Automatické přizpůsobení velikosti buněk podle rozlišení
    AdjustCellSizeForResolution();

    // Inicializace barev a velikostí písma
    for (int i = 0; i < 4; i++)
        for (int j = 0; j < 3; j++) {
            MA_Colors[i][j] = g_DefaultColor;
            MA_FontSizes[i][j] = g_DefaultFontSize;
        }

    for (int i = 0; i < 3; i++)
        for (int j = 0; j < 2; j++) {
            D_Colors[i][j] = g_DefaultColor;
            D_FontSizes[i][j] = g_DefaultFontSize;
        }

    DeleteTable("MACell");
    DeleteTable("DCell");

    // Nastavení výchozích souřadnic podle zvoleného rohu
    SetInitialCoordinates();

    // Vytvoření GripPointu
    CreateGripPoint();

    // Vytvoření tlačítek s výchozí zelenou barvou (tabulky jsou zobrazeny)
    // Všechny pozice jsou relativní k cornerX a cornerY

    // Výpočet vertikálního odsazení pro tlačítka a tabulky
    int buttonY = cornerY + 10; // Tlačítka jsou výše (zmenšeno z 20)
    int maTableY = buttonY + 20; // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)
    int dButtonY = maTableY + (currentCellHeight * 4) + 10; // D tlačítko je pod MA tabulkou s mezerou
    int dTableY = dButtonY + 20; // D tabulka začíná 20 pixelů pod D tlačítkem (zvětšeno z 15)

    // Vytvoření tlačítek pro MA tabulku
    CreateToggleButton("ToggleMA", cornerX + 0, buttonY, "MA Tabulka", clrForestGreen);
    CreateToggleButton("TFNext", cornerX + 90, buttonY, "TF >>", clrSlateGray); // Posunuto blíže (z 100)
    CreateToggleButton("TFPrev", cornerX + 180, buttonY, "TF <<", clrSlateGray); // Posunuto blíže (z 180)

    UpdateMAValues();
    UpdateDValues();

    // Inicializace časovače pro blikání
    if (!EventSetTimer(g_BlinkIntervalSec)) {
        Print("Nepodařilo se nastavit časovač, Chyba: ", GetLastError());
        return INIT_FAILED;
    }

    // Inicializace barev a zvýraznění buněk
    FindClosestToCloseAndColorMap();

    // Zobrazení MA tabulky (výchozí stav je zobrazeno)
    if (showMATable) {
        DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, 3, 4, cornerX, maTableY, clrWhite);
    }

    // Vytvoření tlačítka pro D tabulku s výchozí zelenou barvou
    CreateToggleButton("ToggleD", cornerX, dButtonY, "D Tabulka", clrForestGreen);

    // Zobrazení D tabulky (výchozí stav je zobrazeno)
    if (showDTable) {
        DrawTable("DCell", D_labels_X, D_labels_Y, D_Values, D_Colors, D_FontSizes, 2, 3, cornerX, dTableY, clrWhite);
    }

    UpdateTFLabels();

    // Nastavení události pro změnu velikosti grafu
    ChartSetInteger(0, CHART_EVENT_OBJECT_CREATE, true);
    ChartSetInteger(0, CHART_EVENT_OBJECT_DELETE, true);

    return INIT_SUCCEEDED;
}

void OnDeinit(const int reason)
{
    // Odstranění tabulek
    DeleteTable("MACell");
    DeleteTable("DCell");

    // Odstranění tlačítek
    ObjectDelete(0, "ToggleMA");
    ObjectDelete(0, "ToggleD");
    ObjectDelete(0, "TFNext");
    ObjectDelete(0, "TFPrev");

    // Odstranění GripPointu
    ObjectDelete(0, gripPointName);

    // Ukončení časovače
    EventKillTimer();
}

void OnTick()
{
    static datetime lastUpdate = 0;
    datetime currentTime = TimeCurrent();

    // Aktualizace hodnot každých 5 sekund nebo při změně svíčky
    if (currentTime - lastUpdate >= 5 || Time[0] != lastUpdate) {
        lastUpdate = currentTime;

        // Aktualizace hodnot a barev
        UpdateMAValues();
        UpdateDValues();
        FindClosestToCloseAndColorMap();

        // Překreslení tabulek s novými hodnotami
        if (showMATable || showDTable) {
            // Výpočet vertikálního odsazení pro tlačítka a tabulky
            int buttonY = cornerY + 10; // Tlačítka jsou výše (zmenšeno z 20)
            int maTableY = buttonY + 20; // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)
            int dButtonY = maTableY + (currentCellHeight * 4) + 10; // D tlačítko je pod MA tabulkou s mezerou
            int dTableY = dButtonY + 20; // D tabulka začíná 20 pixelů pod D tlačítkem (zvětšeno z 15)

            if (showMATable) {
                DeleteTable("MACell");
                DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, 3, 4, cornerX, maTableY, clrWhite);
            }

            if (showDTable) {
                DeleteTable("DCell");
                DrawTable("DCell", D_labels_X, D_labels_Y, D_Values, D_Colors, D_FontSizes, 2, 3, cornerX, dTableY, clrWhite);
            }
        }
    }
}

void OnTimer()
{
    // Přepnutí stavu blikání
    blinkState = !blinkState;
    lastBlinkTime = TimeCurrent();

    // Aktualizace barev a zvýraznění buněk
    FindClosestToCloseAndColorMap();

    // Překreslení tabulek s novými barvami
    if (showMATable || showDTable) {
        // Výpočet vertikálního odsazení pro tlačítka a tabulky
        int buttonY = cornerY + 10; // Tlačítka jsou výše (zmenšeno z 20)
        int maTableY = buttonY + 20; // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)
        int dButtonY = maTableY + (currentCellHeight * 4) + 10; // D tlačítko je pod MA tabulkou s mezerou
        int dTableY = dButtonY + 20; // D tabulka začíná 20 pixelů pod D tlačítkem (zvětšeno z 15)

        if (showMATable) {
            DeleteTable("MACell");
            DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, 3, 4, cornerX, maTableY, clrWhite);
        }

        if (showDTable) {
            DeleteTable("DCell");
            DrawTable("DCell", D_labels_X, D_labels_Y, D_Values, D_Colors, D_FontSizes, 2, 3, cornerX, dTableY, clrWhite);
        }
    }
}

/**
 * Vytvoří přepínací tlačítko s daným názvem, pozicí a textem.
 * @param name Název objektu tlačítka
 * @param x X-souřadnice tlačítka
 * @param y Y-souřadnice tlačítka
 * @param label Text zobrazený na tlačítku
 * @param bg Barva pozadí tlačítka
 */
void CreateToggleButton(string name, int x, int y, string label, color bg)
{
   ObjectCreate(0, name, OBJ_BUTTON, 0, 0, 0);
   ObjectSetInteger(0, name, OBJPROP_CORNER, 0);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(0, name, OBJPROP_BORDER_COLOR, clrGray);
   ObjectSetInteger(0, name, OBJPROP_BGCOLOR, bg);
   ObjectSetInteger(0, name, OBJPROP_XSIZE, 70); // Zmenšeno z 80
   ObjectSetInteger(0, name, OBJPROP_YSIZE, 16);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, false);
   ObjectSetInteger(0, name, OBJPROP_STATE, true);
   ObjectSetString(0, name, OBJPROP_TEXT, label);
}

/**
 * Aktualizuje barvu tlačítka podle jeho stavu (zapnuto/vypnuto).
 * @param buttonName Název tlačítka, jehož barva se má aktualizovat
 * @param isOn True, pokud je tlačítko zapnuto (zelená barva), False pro vypnuto (červená barva)
 */
void UpdateButtonColor(string buttonName, bool isOn)
{
   color buttonColor = isOn ? clrForestGreen : clrFireBrick;
   ObjectSetInteger(0, buttonName, OBJPROP_BGCOLOR, buttonColor);
}

/**
 * Odstraní všechny objekty tabulky se zadaným prefixem.
 * @param prefix Prefix názvů objektů, které mají být odstraněny
 */
void DeleteTable(string prefix)
{
    for (int i = ObjectsTotal() - 1; i >= 0; i--)
    {
        string name = ObjectName(0, i);
        if (StringFind(name, prefix) == 0)
            ObjectDelete(0, name);
    }
}

/**
 * Vykreslí tabulku s hodnotami a popisky.
 * @param prefix Prefix pro názvy objektů tabulky
 * @param labels_X Pole popisků sloupců
 * @param labels_Y Pole popisků řádků
 * @param data Dvourozměrné pole hodnot
 * @param colorMap Dvourozměrné pole barev pro jednotlivé buňky
 * @param fontSizes Dvourozměrné pole velikostí písma pro jednotlivé buňky
 * @param rows Počet řádků tabulky
 * @param cols Počet sloupců tabulky
 * @param xOffset X-souřadnice levého horního rohu tabulky
 * @param yOffset Y-souřadnice levého horního rohu tabulky
 * @param colDefault Výchozí barva textu
 */
void DrawTable(string prefix,
               LabelInfo &labels_X[],
               string &labels_Y[],
               double &data[][],
               color &colorMap[][],
               int &fontSizes[][],
               int rows,
               int cols,
               int xOffset,
               int yOffset,
               color colDefault)
{
    int index = 0;

    // --- Zobrazení hodnoty DIST v pravém horním rohu ---
    if (prefix == "MACell" && g_LastNearestDistance > 0)
    {
        string distText = "Dist: " + DoubleToString(g_LastNearestDistance, 2) + " bodů";
        color distColor = (g_LastNearestDistance <= g_HighlightDistance) ? g_CloseRangeColor : colDefault;

        string distLabelName = prefix + "_NearestDistanceTopRight";
        ObjectCreate(0, distLabelName, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(0, distLabelName, OBJPROP_CORNER, 0);
        ObjectSetInteger(0, distLabelName, OBJPROP_XDISTANCE, xOffset + currentCellWidth * cols);
        ObjectSetInteger(0, distLabelName, OBJPROP_YDISTANCE, yOffset - currentCellHeight);
        ObjectSetInteger(0, distLabelName, OBJPROP_FONTSIZE, 9);
        ObjectSetInteger(0, distLabelName, OBJPROP_COLOR, distColor);
        ObjectSetString(0, distLabelName, OBJPROP_TEXT, distText);
    }

    // --- Vykreslení celé tabulky ---
    for (int row = 0; row <= rows; row++)
    {
        for (int col_i = 0; col_i <= cols; col_i++)
        {
            string text;
            color useColor = colDefault;
            int fontSize = 10;

            if (row == 0 && col_i == 0)
                text = "";
            else if (row == 0)
            {
                text = labels_X[col_i - 1].text;
                useColor = labels_X[col_i - 1].clr;
            }
            else if (col_i == 0)
                text = labels_Y[row - 1];
            else
            {
                text = DoubleToString(data[col_i - 1][row - 1], 2);
                useColor = colorMap[col_i - 1][row - 1];
                fontSize = fontSizes[col_i - 1][row - 1];
            }

            string objName = prefix + "_" + (string)index;
            ObjectCreate(0, objName, OBJ_LABEL, 0, 0, 0);
            ObjectSetInteger(0, objName, OBJPROP_CORNER, 0);
            ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, xOffset + currentCellWidth * col_i);
            ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, yOffset + currentCellHeight * row);
            ObjectSetInteger(0, objName, OBJPROP_FONTSIZE, fontSize);
            ObjectSetInteger(0, objName, OBJPROP_COLOR, useColor);
            ObjectSetString(0, objName, OBJPROP_TEXT, text);
            index++;
        }
    }
}




/**
 * Aktualizuje pozice a obsah všech prvků podle nové pozice GripPointu.
 * Přepočítá pozice tlačítek a tabulek, aktualizuje hodnoty a překreslí tabulky.
 */
void UpdateAllElements()
{
    // Výpočet vertikálního odsazení pro tlačítka a tabulky
    int buttonY = cornerY + 10; // Tlačítka jsou výše (zmenšeno z 20)
    int maTableY = buttonY + 20; // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)
    int dButtonY = maTableY + (currentCellHeight * 4) + 10; // D tlačítko je pod MA tabulkou s mezerou
    int dTableY = dButtonY + 20; // D tabulka začíná 20 pixelů pod D tlačítkem (zvětšeno z 15)

    // Aktualizace pozice GripPointu
    ObjectSetInteger(0, gripPointName, OBJPROP_XDISTANCE, cornerX - 15); // 15 pixelů před tlačítkem MA Tabulka
    ObjectSetInteger(0, gripPointName, OBJPROP_YDISTANCE, buttonY); // Stejná Y-pozice jako tlačítko MA Tabulka

    // Aktualizace pozic tlačítek
    ObjectSetInteger(0, "ToggleMA", OBJPROP_XDISTANCE, cornerX + 0);
    ObjectSetInteger(0, "ToggleMA", OBJPROP_YDISTANCE, buttonY);

    ObjectSetInteger(0, "TFNext", OBJPROP_XDISTANCE, cornerX + 80); // Posunuto blíže (z 100)
    ObjectSetInteger(0, "TFNext", OBJPROP_YDISTANCE, buttonY);

    ObjectSetInteger(0, "TFPrev", OBJPROP_XDISTANCE, cornerX + 155); // Posunuto blíže (z 180)
    ObjectSetInteger(0, "TFPrev", OBJPROP_YDISTANCE, buttonY);

    ObjectSetInteger(0, "ToggleD", OBJPROP_XDISTANCE, cornerX);
    ObjectSetInteger(0, "ToggleD", OBJPROP_YDISTANCE, dButtonY);

    // Aktualizace hodnot a barev
    UpdateMAValues();
    UpdateDValues();
    FindClosestToCloseAndColorMap();

    // Překreslení tabulek na nové pozice
    if (showMATable) {
        DeleteTable("MACell");
        DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, 3, 4, cornerX, maTableY, clrWhite);
    }

    if (showDTable) {
        DeleteTable("DCell");
        DrawTable("DCell", D_labels_X, D_labels_Y, D_Values, D_Colors, D_FontSizes, 2, 3, cornerX, dTableY, clrWhite);
    }
}

/**
 * Hledá hodnoty v tabulkách, které jsou nejblíže aktuální ceně, a zvýrazňuje je.
 * Hodnoty v rozsahu g_HighlightDistance od aktuální ceny jsou označeny zelenou barvou.
 * Hodnota, která je nejblíže aktuální ceně, je zvýrazněna blikající barvou a větším písmem.
 */
void FindClosestToCloseAndColorMap()
{
   double dist = g_HighlightDistance;
   double closePrice = Close[0];
   double bestDiff = dist;
   int bestRow = -1, bestCol = -1;
   bool isMA = true;

   ArrayInitialize(MA_Colors, g_DefaultColor);
   ArrayInitialize(D_Colors, g_DefaultColor);
   ArrayInitialize(MA_FontSizes, g_DefaultFontSize);
   ArrayInitialize(D_FontSizes, g_DefaultFontSize);

   color blinkColor = blinkState ? g_BlinkColor : g_BlinkAltColor;

   int maRows = ArrayRange(MA_Values, 0);
   int maCols = ArrayRange(MA_Values, 1);
   int dRows  = ArrayRange(D_Values, 0);
   int dCols  = ArrayRange(D_Values, 1);

   // --- MA tabulka ---
   for (int ma_i = 0; ma_i < maRows; ma_i++)
   {
      for (int ma_j = 0; ma_j < maCols; ma_j++)
      {
         double val = MA_Values[ma_i][ma_j];
         double diff = MathAbs(val - closePrice);

         if (diff <= dist)
         {
            MA_Colors[ma_i][ma_j] = g_CloseRangeColor;

            if (diff < bestDiff)
            {
               bestDiff = diff;
               bestRow = ma_i;
               bestCol = ma_j;
               isMA = true;
            }
         }
      }
   }

   // --- D tabulka ---
   for (int d_i = 0; d_i < dRows; d_i++)
   {
      for (int d_j = 0; d_j < dCols; d_j++)
      {
         double val = D_Values[d_i][d_j];
         double diff = MathAbs(val - closePrice);

         if (diff <= dist)
         {
            D_Colors[d_i][d_j] = g_CloseRangeColor;

            if (diff < bestDiff)
            {
               bestDiff = diff;
               bestRow = d_i;
               bestCol = d_j;
               isMA = false;
            }
         }
      }
   }

   // --- Zvýraznění nejbližší hodnoty ---
   if (bestRow != -1 && bestCol != -1 && bestDiff < dist)
   {
      g_LastNearestDistance = bestDiff;

      if (isMA)
      {
         MA_Colors[bestRow][bestCol] = blinkColor;
         MA_FontSizes[bestRow][bestCol] = g_HighlightFontSize;

         Print("✅ Nejbližší MA[", bestRow, "][", bestCol, "] = ",
               DoubleToString(MA_Values[bestRow][bestCol], 2),
               ", Close = ", DoubleToString(closePrice, 2),
               ", Diff = ", DoubleToString(bestDiff, 2));
      }
      else
      {
         D_Colors[bestRow][bestCol] = blinkColor;
         D_FontSizes[bestRow][bestCol] = g_HighlightFontSize;

         Print("✅ Nejbližší D[", bestRow, "][", bestCol, "] = ",
               DoubleToString(D_Values[bestRow][bestCol], 2),
               ", Close = ", DoubleToString(closePrice, 2),
               ", Diff = ", DoubleToString(bestDiff, 2));
      }
   }
   else
   {
      g_LastNearestDistance = 0.0;
      Print("ℹ️ Žádná hodnota v rozsahu. Close = ", DoubleToString(closePrice, 2));
   }
}





/**
 * Zpracovává události grafu - přetažení GripPointu, změnu velikosti grafu a kliknutí na tlačítka.
 * @param id Identifikátor události
 * @param lparam Dlouhý parametr události
 * @param dparam Desetinný parametr události
 * @param sparam Textový parametr události
 */
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    // Detekce přetažení GripPointu
    if (id == CHARTEVENT_OBJECT_DRAG && sparam == gripPointName) {
        // Získání nových souřadnic GripPointu
        int gripX = (int)ObjectGetInteger(0, gripPointName, OBJPROP_XDISTANCE);
        int gripY = (int)ObjectGetInteger(0, gripPointName, OBJPROP_YDISTANCE);

        // Aktualizace hlavních souřadnic (cornerX, cornerY)
        // GripPoint je 15 pixelů před tlačítkem MA Tabulka, proto přičteme 15
        cornerX = gripX + 15;
        cornerY = gripY;

        // Aktualizace všech prvků podle nové pozice
        UpdateAllElements();
    }

    // Detekce změny velikosti grafu
    if (id == CHARTEVENT_CHART_CHANGE) {
        int newWidth = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
        int newHeight = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);

        // Pokud se změnila velikost grafu, aktualizujeme pozice
        if (newWidth != chartWidth || newHeight != chartHeight) {
            chartWidth = newWidth;
            chartHeight = newHeight;

            // Automatické přizpůsobení velikosti buněk podle rozlišení
            AdjustCellSizeForResolution();

            // Vždy aktualizujeme pozici tabulky při změně velikosti grafu
            // aby se zabránilo situaci, kdy je tabulka mimo viditelnou oblast
            SetInitialCoordinates();
            UpdateAllElements();
        }
    }

    // Zpracování kliknutí na objekty
    if (id == CHARTEVENT_OBJECT_CLICK) {
        // Přepínání MA tabulky
        if (sparam == "ToggleMA") {
            showMATable = !showMATable; // Přepnutí stavu
            UpdateButtonColor("ToggleMA", showMATable); // Aktualizace barvy tlačítka

            // Výpočet vertikálního odsazení pro tlačítka a tabulky
            int buttonY = cornerY + 10; // Tlačítka jsou výše (zmenšeno z 20)
            int maTableY = buttonY + 20; // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)

            if (showMATable) {
                // Zobrazení tabulky
                DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, 3, 4, cornerX, maTableY, clrWhite);
            } else {
                // Skrytí tabulky
                DeleteTable("MACell");
            }
        }

        // Přepínání D tabulky
        if (sparam == "ToggleD") {
            showDTable = !showDTable; // Přepnutí stavu
            UpdateButtonColor("ToggleD", showDTable); // Aktualizace barvy tlačítka

            // Výpočet vertikálního odsazení pro tlačítka a tabulky
            int buttonY = cornerY + 10; // Tlačítka jsou výše (zmenšeno z 20)
            int maTableY = buttonY + 20; // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)
            int dButtonY = maTableY + (currentCellHeight * 4) + 10; // D tlačítko je pod MA tabulkou s mezerou
            int dTableY = dButtonY + 20; // D tabulka začíná 20 pixelů pod D tlačítkem (zvětšeno z 15)

            if (showDTable) {
                // Zobrazení tabulky
                DrawTable("DCell", D_labels_X, D_labels_Y, D_Values, D_Colors, D_FontSizes, 2, 3, cornerX, dTableY, clrWhite);
            } else {
                // Skrytí tabulky
                DeleteTable("DCell");
            }
        }

        // Posun timeframe dopředu
        if (sparam == "TFNext") {
            currentTFIndex++;
            if (currentTFIndex > 2) currentTFIndex = 0;
            UpdateTFLabels();

            if (showMATable) {
                // Výpočet vertikálního odsazení pro tlačítka a tabulky
                int buttonY = cornerY + 10; // Tlačítka jsou výše (zmenšeno z 20)
                int maTableY = buttonY + 20; // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)

                DeleteTable("MACell");
                UpdateMAValues();
                UpdateDValues();
                DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, 3, 4, cornerX, maTableY, clrWhite);
            }
        }

        // Posun timeframe zpět
        if (sparam == "TFPrev") {
            currentTFIndex--;
            if (currentTFIndex < 0) currentTFIndex = 2;
            UpdateTFLabels();

            if (showMATable) {
                // Výpočet vertikálního odsazení pro tlačítka a tabulky
                int buttonY = cornerY + 10; // Tlačítka jsou výše (zmenšeno z 20)
                int maTableY = buttonY + 20; // MA tabulka začíná 20 pixelů pod tlačítky (zvětšeno z 15)

                DeleteTable("MACell");
                UpdateMAValues();
                UpdateDValues();
                DrawTable("MACell", MA_labels_X, MA_labels_Y, MA_Values, MA_Colors, MA_FontSizes, 3, 4, cornerX, maTableY, clrWhite);
            }
        }
    }
}
