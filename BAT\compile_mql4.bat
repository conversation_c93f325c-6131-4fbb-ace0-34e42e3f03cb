@echo off
setlocal EnableDelayedExpansion

:: Nastavení proměnných
set MT4_INSTANCE_ID=2BF3BABFCAC5D01B05BDC27880F98926
set METAEDITOR="C:\Program Files (x86)\MetaTrader 4 IC Markets 5\metaeditor.exe"
set MQL4_DIR="C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\%MT4_INSTANCE_ID%\MQL4"
set LOG_DIR="C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\%MT4_INSTANCE_ID%\MQL4\Logs\CompileLogs"

:: Vytvoření složky pro logy, pokud neexistuje
echo Kontroluji existenci adresare pro logy: %LOG_DIR%
if not exist %LOG_DIR% (
    echo Vytvarim adresar pro logy: %LOG_DIR%
    mkdir %LOG_DIR%
) else (
    echo Adresar pro logy existuje: %LOG_DIR%
)

:: Zpracování parametrů příkazové řádky
if "%1"=="/all" (
    :: Kompilace všech .mq4 souborů v zadané složce
    set "FOLDER=%~2"
    if "!FOLDER!"=="" (
        set "FOLDER=Experts"
    )
    
    echo Kompiluji vsechny .mq4 soubory ve slozce: %MQL4_DIR:"=%\!FOLDER!
    
    for %%f in (%MQL4_DIR:"=%\!FOLDER!\*.mq4) do (
        echo Kompiluji "%%f"

        :: Nastavení log souboru
        set "LOG_FILE=%LOG_DIR:"=%\%%~nf.log"
        echo Log bude ulozen do: "!LOG_FILE!"

        :: Ujistíme se, že adresář pro log existuje
        for %%i in ("!LOG_FILE!") do set "LOG_DIR_ACTUAL=%%~dpi"
        if not exist "!LOG_DIR_ACTUAL!" (
            echo Vytvarim adresar pro log: "!LOG_DIR_ACTUAL!"
            mkdir "!LOG_DIR_ACTUAL!"
        )

        %METAEDITOR% /compile:"%%f" /log:"!LOG_FILE!"

        :: Kontrola, zda byl vytvořen .ex4 soubor
        set "EX4_FILE=%%~dpnf.ex4"
        if exist "!EX4_FILE!" (
            echo Kompilace "%%f" uspesna, soubor .ex4 byl vytvoren.

            :: Kontrola, zda log soubor existuje
            if exist "!LOG_FILE!" (
                echo Obsah log souboru:
                echo -----------------
                type "!LOG_FILE!"
                echo -----------------
            ) else (
                echo Log soubor nebyl vytvoren: "!LOG_FILE!"
            )
        ) else (
            echo Chyba pri kompilaci "%%f", soubor .ex4 nebyl vytvoren.

            :: Kontrola, zda log soubor existuje
            if exist "!LOG_FILE!" (
                echo Obsah log souboru:
                echo -----------------
                type "!LOG_FILE!"
                echo -----------------
            ) else (
                echo Log soubor nebyl vytvoren: "!LOG_FILE!"
            )
        )
    )
) else if "%1"=="/file" (
    :: Kompilace jednoho konkrétního souboru
    set "FILE_PATH=%~2"
    if "!FILE_PATH!"=="" (
        echo Chyba: Nezadan nazev souboru. Pouzijte napriklad: %0 /file MyExpert.mq4
        goto :end
    )

    :: Kontrola existence souboru
    if exist "!FILE_PATH!" (
        echo Kompiluji "!FILE_PATH!"

        :: Extrahujeme pouze název souboru bez cesty
        for %%i in ("!FILE_PATH!") do set "FILE_NAME_ONLY=%%~ni"

        set "LOG_FILE=%LOG_DIR:"=%\!FILE_NAME_ONLY!.log"
        echo Log bude ulozen do: "!LOG_FILE!"

        :: Ujistíme se, že adresář pro log existuje
        for %%i in ("!LOG_FILE!") do set "LOG_DIR_ACTUAL=%%~dpi"
        if not exist "!LOG_DIR_ACTUAL!" (
            echo Vytvarim adresar pro log: "!LOG_DIR_ACTUAL!"
            mkdir "!LOG_DIR_ACTUAL!"
        )

        %METAEDITOR% /compile:"!FILE_PATH!" /log:"!LOG_FILE!"

        :: Kontrola, zda byl vytvořen .ex4 soubor
        set "EX4_FILE=!FILE_PATH:~0,-4!.ex4"
        if exist "!EX4_FILE!" (
            echo Kompilace "!FILE_PATH!" uspesna, soubor .ex4 byl vytvoren.
            echo.

            :: Kontrola, zda log soubor existuje
            if exist "!LOG_FILE!" (
                echo Obsah log souboru:
                echo -----------------
                type "!LOG_FILE!"
                echo -----------------
            ) else (
                echo Log soubor nebyl vytvoren: "!LOG_FILE!"
            )
        ) else (
            echo Chyba pri kompilaci "!FILE_PATH!", soubor .ex4 nebyl vytvoren.
            echo.

            :: Kontrola, zda log soubor existuje
            if exist "!LOG_FILE!" (
                echo Obsah log souboru:
                echo -----------------
                type "!LOG_FILE!"
                echo -----------------
            ) else (
                echo Log soubor nebyl vytvoren: "!LOG_FILE!"
            )
        )
    ) else (
        echo Soubor "!FILE_PATH!" neexistuje.
    )
) else if "%1"=="/current" (
    :: Kompilace aktuálně otevřeného souboru v MetaEditoru
    echo Kompiluji aktualne otevreny soubor v MetaEditoru
    %METAEDITOR% /compile
) else if "%1"=="/edit" (
    :: Otevření souboru v MetaEditoru
    set "FILE_PATH=%~2"
    if "!FILE_PATH!"=="" (
        echo Chyba: Nezadan nazev souboru. Pouzijte napriklad: %0 /edit MyExpert.mq4
        goto :end
    )

    :: Kontrola existence souboru
    if exist "!FILE_PATH!" (
        echo Otviram "!FILE_PATH!" v MetaEditoru
        %METAEDITOR% "!FILE_PATH!"
    ) else (
        echo Soubor "!FILE_PATH!" neexistuje.
    )
) else (
    :: Zobrazení nápovědy při neplatném nebo chybějícím parametru
    echo Univerzalni nastroj pro kompilaci a ladeni MQL4 kodu
    echo ===================================================
    echo.
    echo Pouziti:
    echo   %0 /all [slozka]            - Zkompiluje vsechny .mq4 soubory v zadane slozce (vychozi: Experts)
    echo   %0 /file ^<cesta_k_souboru^>  - Zkompiluje jeden konkretni .mq4 soubor
    echo   %0 /current                 - Zkompiluje aktualne otevreny soubor v MetaEditoru
    echo   %0 /edit ^<cesta_k_souboru^>  - Otevre soubor v MetaEditoru
    echo.
    echo Priklady:
    echo   %0 /all                     - Zkompiluje vsechny .mq4 soubory ve slozce Experts
    echo   %0 /all Scripts             - Zkompiluje vsechny .mq4 soubory ve slozce Scripts
    echo   %0 /file Experts\MyEA.mq4   - Zkompiluje soubor MyEA.mq4 ve slozce Experts
    echo   %0 /edit Experts\MyEA.mq4   - Otevre soubor MyEA.mq4 v MetaEditoru
    echo.
    echo Poznamka: Vsechny logy kompilace jsou ulozeny v:
    echo %LOG_DIR%
)

:end
echo.
echo Cekam 5 sekund pred ukoncenim...
timeout /t 5 /nobreak > nul
