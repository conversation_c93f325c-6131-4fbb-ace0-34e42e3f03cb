//+------------------------------------------------------------------+
//| GripPoint a Tabulka - Finální verze s GripPointem v rohu R1C1    |
//+------------------------------------------------------------------+
#property strict
#property copyright "Traderpoint"
#property version   "1.00"

// Enum pro volbu rohu
enum ChartCorner
{
   LeftUpper = 0,  // Levý horní roh
   RightUpper = 1, // Pravý horní roh
   LeftLower = 2,  // Levý dolní roh
   RightLower = 3  // Pravý dolní roh
};

// Vstupní parametry
input ChartCorner g_Corner = LeftUpper;  // Umístění v rohu
input int g_TableRows = 5;               // Počet řádků tabulky
input int g_TableCols = 3;               // Počet sloupců tabulky
input int g_CellWidth = 70;              // Šířka buňky
input int g_CellHeight = 18;             // Výška buňky
input color g_GripColor = clrYellow;     // Barva GripPointu
input color g_CellColor = clrWhite;      // Barva buněk
input color g_TextColor = clrBlack;      // Barva textu

// Globální proměnné
int cornerX, cornerY;                    // Pozice GripPointu
string gripPointName = "GripPoint";      // Název GripPointu
int chartWidth, chartHeight;             // Rozměry grafu
int tableX, tableY;                      // Pozice tabulky
bool showTable = true;                   // Zobrazit tabulku
string toggleButtonName = "ToggleButton"; // Název tlačítka pro přepínání

//+------------------------------------------------------------------+
//| Expert initialization function                                    |
//+------------------------------------------------------------------+
int OnInit()
{
   // Zjištění rozměrů grafu
   chartWidth = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
   chartHeight = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);

   // Nastavení výchozích souřadnic podle zvoleného rohu
   SetInitialCoordinates();

   // Vykreslení tabulky
   if(showTable) DrawTable();

   // Vytvoření GripPointu v rohu buňky R1C1
   CreateGripPoint();

   // Vytvoření tlačítka pro zapínání a vypínání tabulky
   CreateToggleButton();

   // Nastavení události pro změnu velikosti grafu
   ChartSetInteger(0, CHART_EVENT_OBJECT_CREATE, true);
   ChartSetInteger(0, CHART_EVENT_OBJECT_DELETE, true);

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                  |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Odstranění všech objektů
   ObjectDelete(0, gripPointName);
   ObjectDelete(0, toggleButtonName);
   DeleteTable();
}

//+------------------------------------------------------------------+
//| Expert tick function                                              |
//+------------------------------------------------------------------+
void OnTick()
{
   // Není potřeba nic dělat při každém ticku
}

//+------------------------------------------------------------------+
//| ChartEvent function                                               |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
   // Detekce přetažení GripPointu
   if(id == CHARTEVENT_OBJECT_DRAG && sparam == gripPointName)
   {
      // Získání nových souřadnic GripPointu
      cornerX = (int)ObjectGetInteger(0, gripPointName, OBJPROP_XDISTANCE);
      cornerY = (int)ObjectGetInteger(0, gripPointName, OBJPROP_YDISTANCE);

      // Výpočet nové pozice tabulky podle pozice GripPointu
      CalculateTablePosition();

      // Překreslení tabulky na nové pozici
      if(showTable) DrawTable();

      // Aktualizace pozice tlačítka
      UpdateToggleButtonPosition();

      // Aktualizace pozice GripPointu, aby byl v rohu buňky R1C1
      UpdateGripPointPosition();
   }

   // Detekce kliknutí na tlačítko
   if(id == CHARTEVENT_OBJECT_CLICK && sparam == toggleButtonName)
   {
      // Přepnutí zobrazení tabulky
      showTable = !showTable;

      // Aktualizace textu tlačítka
      string buttonText = showTable ? "HIDE" : "SHOW";
      ObjectSetString(0, toggleButtonName, OBJPROP_TEXT, buttonText);

      // Aktualizace barvy tlačítka
      color buttonColor = showTable ? clrDarkGreen : clrDarkRed;
      ObjectSetInteger(0, toggleButtonName, OBJPROP_BGCOLOR, buttonColor);

      // Efekt stisku tlačítka
      ObjectSetInteger(0, toggleButtonName, OBJPROP_STATE, true);
      Sleep(100); // Krátká pauza pro viditelný efekt stisku
      ObjectSetInteger(0, toggleButtonName, OBJPROP_STATE, false);

      // Zobrazení nebo skrytí tabulky
      if(showTable)
         DrawTable();
      else
         DeleteTable();
   }

   // Detekce změny velikosti grafu
   if(id == CHARTEVENT_CHART_CHANGE)
   {
      int newWidth = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
      int newHeight = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);

      // Pokud se změnila velikost grafu, aktualizujeme pozice
      if(newWidth != chartWidth || newHeight != chartHeight)
      {
         chartWidth = newWidth;
         chartHeight = newHeight;

         // Překreslení tabulky
         if(showTable) DrawTable();

         // Aktualizace pozice tlačítka
         UpdateToggleButtonPosition();

         // Aktualizace pozice GripPointu, aby byl v rohu buňky R1C1
         UpdateGripPointPosition();
      }
   }
}

//+------------------------------------------------------------------+
//| Nastavení výchozích souřadnic podle zvoleného rohu               |
//+------------------------------------------------------------------+
void SetInitialCoordinates()
{
   // Výchozí offsety od rohů
   int offsetX = 20;
   int offsetY = 20;

   // Nastavení pozice tabulky podle zvoleného rohu
   switch(g_Corner)
   {
      case LeftUpper:
         tableX = offsetX + 5;
         tableY = offsetY + 40; // Posun tabulky o 40 pixelů níže
         break;
      case RightUpper:
         tableX = chartWidth - (g_TableCols*g_CellWidth + offsetX);
         tableY = offsetY + 40; // Posun tabulky o 40 pixelů níže
         break;
      case LeftLower:
         tableX = offsetX + 5;
         tableY = chartHeight - (g_TableRows*g_CellHeight + offsetY);
         break;
      case RightLower:
         tableX = chartWidth - (g_TableCols*g_CellWidth + offsetX);
         tableY = chartHeight - (g_TableRows*g_CellHeight + offsetY);
         break;
   }

   // Výpočet pozice GripPointu (roh buňky R1C1)
   CalculateGripPointPosition();
}

//+------------------------------------------------------------------+
//| Výpočet pozice GripPointu (roh buňky R1C1)                       |
//+------------------------------------------------------------------+
void CalculateGripPointPosition()
{
   // GripPoint bude ve stejné výšce jako tlačítko, s offsetem -15 pixelů na ose X
   cornerX = tableX - 20;
   cornerY = tableY - 22; // Stejná výška jako tlačítko (25 pixelů nad buňkou R1C1)
}

//+------------------------------------------------------------------+
//| Výpočet pozice tabulky podle pozice GripPointu                   |
//+------------------------------------------------------------------+
void CalculateTablePosition()
{
   // Tabulka začíná na pozici GripPointu + offset 15 pixelů na ose X a 25 pixelů na ose Y
   tableX = cornerX + 15;
   tableY = cornerY + 25; // 25 pixelů pod GripPointem
}

//+------------------------------------------------------------------+
//| Aktualizace pozice GripPointu, aby byl v rohu buňky R1C1         |
//+------------------------------------------------------------------+
void UpdateGripPointPosition()
{
   // Výpočet pozice GripPointu (roh buňky R1C1)
   CalculateGripPointPosition();

   // Aktualizace pozice GripPointu
   ObjectSetInteger(0, gripPointName, OBJPROP_XDISTANCE, cornerX);
   ObjectSetInteger(0, gripPointName, OBJPROP_YDISTANCE, cornerY);
}

//+------------------------------------------------------------------+
//| Vytvoření GripPointu                                             |
//+------------------------------------------------------------------+
void CreateGripPoint()
{
   if(ObjectFind(0, gripPointName) >= 0)
      ObjectDelete(0, gripPointName);

   ObjectCreate(0, gripPointName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, gripPointName, OBJPROP_CORNER, CORNER_LEFT_UPPER); // Vždy z levého horního rohu
   ObjectSetInteger(0, gripPointName, OBJPROP_XDISTANCE, cornerX);
   ObjectSetInteger(0, gripPointName, OBJPROP_YDISTANCE, cornerY);
   ObjectSetInteger(0, gripPointName, OBJPROP_XSIZE, 15);
   ObjectSetInteger(0, gripPointName, OBJPROP_YSIZE, 15);
   ObjectSetInteger(0, gripPointName, OBJPROP_BGCOLOR, g_GripColor);
   ObjectSetInteger(0, gripPointName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, gripPointName, OBJPROP_BORDER_COLOR, clrBlack);
   ObjectSetInteger(0, gripPointName, OBJPROP_SELECTABLE, true);
   ObjectSetInteger(0, gripPointName, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, gripPointName, OBJPROP_HIDDEN, false);
   ObjectSetString(0, gripPointName, OBJPROP_TOOLTIP, "Táhněte pro posun tabulky");
}

//+------------------------------------------------------------------+
//| Vykreslení tabulky s uživatelsky definovaným počtem řádků a sloupců |
//+------------------------------------------------------------------+
void DrawTable()
{
   // Nejprve smažeme starou tabulku
   DeleteTable();

   // Vykreslení tabulky s uživatelsky definovaným počtem řádků a sloupců
   for(int row = 0; row < g_TableRows; row++)
   {
      for(int col = 0; col < g_TableCols; col++)
      {
         string cellName = "TableCell_" + IntegerToString(row) + "_" + IntegerToString(col);

         // Výpočet pozice buňky
         int cellX = tableX + col * g_CellWidth;
         int cellY = tableY + row * g_CellHeight;

         // Vytvoření buňky (obdélníkový štítek)
         ObjectCreate(0, cellName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
         ObjectSetInteger(0, cellName, OBJPROP_CORNER, CORNER_LEFT_UPPER); // Vždy z levého horního rohu
         ObjectSetInteger(0, cellName, OBJPROP_XDISTANCE, cellX);
         ObjectSetInteger(0, cellName, OBJPROP_YDISTANCE, cellY);
         ObjectSetInteger(0, cellName, OBJPROP_XSIZE, g_CellWidth);
         ObjectSetInteger(0, cellName, OBJPROP_YSIZE, g_CellHeight);
         ObjectSetInteger(0, cellName, OBJPROP_BGCOLOR, g_CellColor);
         ObjectSetInteger(0, cellName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
         ObjectSetInteger(0, cellName, OBJPROP_BORDER_COLOR, clrBlack);

         // Vytvoření textu v buňce
         string textName = "TableText_" + IntegerToString(row) + "_" + IntegerToString(col);
         string cellText = "R" + IntegerToString(row+1) + "C" + IntegerToString(col+1);

         ObjectCreate(0, textName, OBJ_LABEL, 0, 0, 0);
         ObjectSetInteger(0, textName, OBJPROP_CORNER, CORNER_LEFT_UPPER); // Vždy z levého horního rohu
         ObjectSetInteger(0, textName, OBJPROP_XDISTANCE, cellX + 5);
         ObjectSetInteger(0, textName, OBJPROP_YDISTANCE, cellY + 3);
         ObjectSetString(0, textName, OBJPROP_TEXT, cellText);
         ObjectSetInteger(0, textName, OBJPROP_COLOR, g_TextColor);
         ObjectSetInteger(0, textName, OBJPROP_FONTSIZE, 8);
      }
   }
}

//+------------------------------------------------------------------+
//| Smazání tabulky                                                  |
//+------------------------------------------------------------------+
void DeleteTable()
{
   for(int i = ObjectsTotal(0, -1, -1) - 1; i >= 0; i--)
   {
      string objName = ObjectName(0, i, -1, -1);
      if(StringFind(objName, "TableCell_") == 0 || StringFind(objName, "TableText_") == 0)
         ObjectDelete(0, objName);
   }
}

//+------------------------------------------------------------------+
//| Vytvoření tlačítka pro zapínání a vypínání tabulky               |
//+------------------------------------------------------------------+
void CreateToggleButton()
{
   if(ObjectFind(0, toggleButtonName) >= 0)
      ObjectDelete(0, toggleButtonName);

   // Výpočet pozice tlačítka (přímo nad buňkou R1C1)
   int buttonX = tableX;
   int buttonY = tableY - 25; // 25 pixelů nad buňkou R1C1

   // Vytvoření tlačítka
   ObjectCreate(0, toggleButtonName, OBJ_BUTTON, 0, 0, 0);
   ObjectSetInteger(0, toggleButtonName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, toggleButtonName, OBJPROP_XDISTANCE, buttonX);
   ObjectSetInteger(0, toggleButtonName, OBJPROP_YDISTANCE, buttonY);
   ObjectSetInteger(0, toggleButtonName, OBJPROP_XSIZE, g_CellWidth);
   ObjectSetInteger(0, toggleButtonName, OBJPROP_YSIZE, 20);

   // Nastavení barvy tlačítka podle stavu tabulky
   color buttonColor = showTable ? clrDarkGreen : clrDarkRed;
   ObjectSetInteger(0, toggleButtonName, OBJPROP_BGCOLOR, buttonColor);
   ObjectSetInteger(0, toggleButtonName, OBJPROP_BORDER_COLOR, clrBlack);
   ObjectSetInteger(0, toggleButtonName, OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(0, toggleButtonName, OBJPROP_FONTSIZE, 8);

   // Nastavení textu tlačítka podle stavu tabulky
   string buttonText = showTable ? "HIDE" : "SHOW";
   ObjectSetString(0, toggleButtonName, OBJPROP_TEXT, buttonText);
   ObjectSetString(0, toggleButtonName, OBJPROP_TOOLTIP, "Klikněte pro zobrazení/skrytí tabulky");

   // Přidání efektu stisku tlačítka
   ObjectSetInteger(0, toggleButtonName, OBJPROP_STATE, false);
   ObjectSetInteger(0, toggleButtonName, OBJPROP_ZORDER, 0);
}

//+------------------------------------------------------------------+
//| Aktualizace pozice tlačítka                                      |
//+------------------------------------------------------------------+
void UpdateToggleButtonPosition()
{
   // Výpočet pozice tlačítka (přímo nad buňkou R1C1)
   int buttonX = tableX;
   int buttonY = tableY - 25; // 25 pixelů nad buňkou R1C1

   // Aktualizace pozice tlačítka
   ObjectSetInteger(0, toggleButtonName, OBJPROP_XDISTANCE, buttonX);
   ObjectSetInteger(0, toggleButtonName, OBJPROP_YDISTANCE, buttonY);
}
