# Nástroje pro kompilaci a ladění MQL4 kódu

Tento adresář obsahuje sadu nástrojů pro snadnou kompilaci a ladění MQL4 kódu.

## <PERSON><PERSON><PERSON><PERSON> nástroj

- `compile_mql4.bat` - Univerzální nástroj pro kompilaci a ladění MQL4 kódu

## Zkrácené příkazy

- `compile_current.bat` - Zkompiluje aktuálně otevřený soubor v MetaEditoru
- `compile_file.bat` - Zkompiluje konkrétní soubor (např. `compile_file.bat Experts\MyEA.mq4`)
- `compile_all.bat` - Zkompiluje všechny soubory v adresáři Experts (nebo jiném zadaném adresáři)
- `edit_file.bat` - Otevře soubor v MetaEditoru (např. `edit_file.bat Experts\MyEA.mq4`)

## Použit<PERSON> hlavn<PERSON>ho n<PERSON>

```
compile_mql4.bat /all [složka]           - Zkompiluje všechny .mq4 soubory v zadané složce (výchozí: Experts)
compile_mql4.bat /file <cesta_k_souboru> - Zkompiluje jeden konkrétní .mq4 soubor
compile_mql4.bat /current                - Zkompiluje aktuálně otevřený soubor v MetaEditoru
compile_mql4.bat /edit <cesta_k_souboru> - Otevře soubor v MetaEditoru
```

## Příklady

```
compile_mql4.bat /all                    - Zkompiluje všechny .mq4 soubory ve složce Experts
compile_mql4.bat /all Scripts            - Zkompiluje všechny .mq4 soubory ve složce Scripts
compile_mql4.bat /file Experts\MyEA.mq4  - Zkompiluje soubor MyEA.mq4 ve složce Experts
compile_mql4.bat /edit Experts\MyEA.mq4  - Otevře soubor MyEA.mq4 v MetaEditoru
```

## Logy kompilace

Všechny logy kompilace jsou uloženy v adresáři:
`C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\2BF3BABFCAC5D01B05BDC27880F98926\MQL4\Logs\CompileLogs`

## Tipy pro efektivní práci

1. Umístěte tyto BAT soubory do kořenového adresáře MQL4, aby byly snadno dostupné
2. Pro rychlou kompilaci aktuálně otevřeného souboru stačí spustit `compile_current.bat`
3. Pro kompilaci konkrétního souboru použijte `compile_file.bat cesta_k_souboru.mq4`
4. Pro kompilaci všech souborů v adresáři použijte `compile_all.bat [složka]`
5. Pro otevření souboru v MetaEditoru použijte `edit_file.bat cesta_k_souboru.mq4`
