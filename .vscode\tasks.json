{"version": "2.0.0", "tasks": [{"label": "MQL: Compile Current File", "type": "shell", "command": "${command:mql_tools.compileFile}", "group": {"kind": "build", "isDefault": true}, "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "MQL: Compile Current File with <PERSON><PERSON>t", "type": "shell", "command": "${command:mql_tools.compileScript}", "group": "build", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "MQL: Open in MetaEditor", "type": "shell", "command": "${command:mql_tools.openInMetaEditor}", "group": "none", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "MQL: Open Terminal", "type": "shell", "command": "${command:mql_tools.openTerminal}", "group": "none", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}]}