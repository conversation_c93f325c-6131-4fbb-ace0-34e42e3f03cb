Sub TestSloupecJ()
    ' Test pro ově<PERSON><PERSON><PERSON> hodnot ve sloupci J
    Dim ws As Worksheet
    Set ws = ActiveSheet
    
    Debug.Print "=== TEST HODNOT VE SLOUPCI J ==="
    
    ' Test prvních 10 řádků
    Dim i As Long
    For i = 2 To 11
        Dim valueJ As Variant
        Dim valueK As Variant
        
        valueJ = ws.Cells(i, 10).Value ' Sloupec J
        valueK = ws.Cells(i, 11).Value ' Sloupec K
        
        Debug.Print "Řádek " & i & ": J=" & valueJ & " (typ: " & TypeName(valueJ) & "), K=" & valueK
        
        ' Test IsNumeric
        Debug.Print "  IsNumeric(J): " & IsNumeric(valueJ) & ", IsNumeric(K): " & IsNumeric(valueK)
        
        ' Test Val()
        If IsNumeric(valueJ) Then Debug.Print "  Val(J): " & Val(valueJ)
        If IsNumeric(valueK) Then Debug.Print "  Val(K): " & <PERSON>(valueK)
        
        Debug.Print ""
    Next i
    
    ' Test sčítání
    Debug.Print "=== TEST SČÍTÁNÍ ==="
    Dim sum1 As Double, sum2 As Double
    sum1 = ws.Cells(2, 10).Value + ws.Cells(3, 10).Value
    sum2 = Val(ws.Cells(2, 10).Value) + Val(ws.Cells(3, 10).Value)
    
    Debug.Print "Přímé sčítání: " & sum1
    Debug.Print "Sčítání s Val(): " & sum2
    
    MsgBox "Test dokončen. Výsledky jsou v Immediate Window (Ctrl+G)"
End Sub
