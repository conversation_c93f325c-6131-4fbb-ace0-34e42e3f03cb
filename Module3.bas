Attribute VB_Name = "Module1"
' Pomocn� funkce pro vy�i�t�n� ��sla smlouvy na porovn�n�
Function CleanContractNum(num As String) As String
    num = Trim(CStr(num))
    If InStr(num, "/") > 0 Then
        num = Left(num, InStr(num, "/") - 1)
    End If
    num = Replace(num, ".", "")
    num = Replace(num, " ", "")
    CleanContractNum = num
End Function

' Vytvo�� sumarizaci podle ��sla smlouvy � slu�uje duplicitn� ��dky
Sub VytvorSumarizaci(srcWS As Worksheet)
    ' Sma�e star� list Sumarizace, pokud existuje
    Application.DisplayAlerts = False
    On Error Resume Next
    srcWS.Parent.Sheets("Sumarizace").Delete
    Application.DisplayAlerts = True
    On Error GoTo 0

    ' Zkop�ruje zdrojov� list a p�ejmenuje na Sumarizace
    srcWS.Copy After:=srcWS
    Dim wsSum As Worksheet
    Set wsSum = srcWS.Parent.Sheets(srcWS.Index + 1)
    wsSum.Name = "Sumarizace"

    ' Nastav� rozsah
    Dim startRow As Long: startRow = 12
    Dim lastRow As Long: lastRow = wsSum.Cells(wsSum.Rows.Count, "D").End(xlUp).row
    If lastRow < startRow Then Exit Sub

    ' Sloupce (podle �ablony)
    Const COL_CISLO_PS As Long = 4 ' D
    Const COL_PROVIZE As Long = 19 ' S
    Const COL_ODMENA As Long = 21 ' U
    Const COL_POZNAMKA As Long = 23 ' W

    ' P�evod vzorc� ve sloupci W na hodnoty
    With wsSum
        .Range(.Cells(startRow, COL_POZNAMKA), .Cells(lastRow, COL_POZNAMKA)).Value = _
            .Range(.Cells(startRow, COL_POZNAMKA), .Cells(lastRow, COL_POZNAMKA)).Value
    End With

    ' Slovn�ky pro slu�ov�n�
    Dim dict As Object: Set dict = CreateObject("Scripting.Dictionary")
    Dim countDict As Object: Set countDict = CreateObject("Scripting.Dictionary")
    Dim i As Long, key As String

    For i = startRow To lastRow
        key = Trim(CStr(wsSum.Cells(i, COL_CISLO_PS).Value))
        If key <> "" Then
            If Not dict.Exists(key) Then
                dict.Add key, i
                countDict.Add key, 1
            Else
                Dim targetRow As Long: targetRow = dict(key)
                wsSum.Cells(targetRow, COL_PROVIZE).Value = _
                    val(wsSum.Cells(targetRow, COL_PROVIZE).Value) + val(wsSum.Cells(i, COL_PROVIZE).Value)
                wsSum.Cells(targetRow, COL_ODMENA).Value = _
                    val(wsSum.Cells(targetRow, COL_ODMENA).Value) + val(wsSum.Cells(i, COL_ODMENA).Value)
                countDict(key) = countDict(key) + 1
                wsSum.Rows(i).Delete
                i = i - 1
                lastRow = lastRow - 1
            End If
        End If
    Next i

    ' Zv�razn� slou�en� ��dky a pozn�mku do W
    Dim sumKey As Variant
    For Each sumKey In dict.Keys
        Dim rowIdx As Long: rowIdx = dict(sumKey)
        If countDict(sumKey) > 1 Then
            With wsSum.Range(wsSum.Cells(rowIdx, "B"), wsSum.Cells(rowIdx, "W"))
                .Interior.Color = RGB(255, 230, 153)
            End With
            With wsSum.Cells(rowIdx, COL_POZNAMKA)
                .Value = "Sumarizov�no: " & countDict(sumKey) & " polo�ek"
                .Font.Color = RGB(0, 102, 204)
                .Font.Bold = True
            End With
        End If
    Next sumKey
End Sub

' ==========================================================
' HLAVN� MAKRO: Synchronizuje data smluv mezi dv�ma soubory
' ==========================================================
Sub SyncContractsDataWithDialog()
    If Date > DateSerial(2025, 7, 10) Then
        MsgBox "Platnost tohoto makra vypr�ela k 10. 7. 2025." & vbCrLf & "Kontaktujte spr�vce pro nov� soubor.", vbCritical, "EXPIRED"
        Exit Sub
    End If

    Dim srcWB As Workbook, dstWB As Workbook
    Dim srcWS As Worksheet, dstWS As Worksheet
    Dim srcFile As Variant, dstFile As Variant

    ' V�b�r soubor�
    srcFile = Application.GetOpenFilename("Excel Files (*.xls*), *.xls*", , "Vyber ZDROJOV� soubor (oDM�NY)")
    If srcFile = False Then MsgBox "V�b�r zru�en.": Exit Sub
    dstFile = Application.GetOpenFilename("Excel Files (*.xls*), *.xls*", , "Vyber C�LOV� soubor (dEN�K UZAV�EN�CH SMLUV)")
    If dstFile = False Then MsgBox "V�b�r zru�en.": Exit Sub

    ' Potvrzen� v�b�ru
    Dim infoText As String
    infoText = "Zdrojov� soubor: " & vbTab & Dir(srcFile) & vbCrLf & _
               "C�lov� soubor: " & vbTab & Dir(dstFile) & vbCrLf & vbCrLf & _
               "Stiskni OK pro potvrzen�, nebo Storno pro ukon�en� operace."
    If MsgBox(infoText, vbOKCancel + vbInformation, "Potvrzen� v�b�ru soubor�") = vbCancel Then
        MsgBox "Operace zru�ena.", vbExclamation
        Exit Sub
    End If

    ' Z�lohov�n� soubor�
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    Dim srcBackup As String, dstBackup As String
    srcBackup = Left(srcFile, InStrRev(srcFile, ".") - 1) & "_backup" & Mid(srcFile, InStrRev(srcFile, "."))
    dstBackup = Left(dstFile, InStrRev(dstFile, ".") - 1) & "_backup" & Mid(dstFile, InStrRev(dstFile, "."))
    On Error Resume Next
    fso.CopyFile srcFile, srcBackup, True
    fso.CopyFile dstFile, dstBackup, True
    On Error GoTo 0

    ' Otev�en� se�it�
    Set srcWB = Workbooks.Open(srcFile)
    Set dstWB = Workbooks.Open(dstFile)
    Set srcWS = srcWB.Sheets(1)
    Set dstWS = dstWB.Sheets(1)

    ' Sumarizace zdrojov�ho listu
    Call VytvorSumarizaci(srcWS)
    Set srcWS = srcWB.Sheets("Sumarizace")

    Dim lastSrcRow As Long, lastDstRow As Long
    lastSrcRow = srcWS.Cells(srcWS.Rows.Count, "D").End(xlUp).row
    lastDstRow = dstWS.Cells(dstWS.Rows.Count, "B").End(xlUp).row

    Application.ScreenUpdating = False
    Dim dataStartRow As Long: dataStartRow = 12

    Dim i As Long, contractNumRaw As String, contractNumClean As String
    Dim foundRow As Long, cell As Range, targetRange As Range
    Dim updatedCount As Long, addedCount As Long, skippedCount As Long
    Dim addedContracts As String, updatedContracts As String, skippedContracts As String
    Dim fullUpdateBVals As String
    Dim trgCols As Variant, wasUpdated As Boolean
    Dim M4Text As String, sheetName As String
    M4Text = Replace(CStr(srcWS.Range("M4").Value), "/", "_")
    sheetName = "Zdroj " & M4Text

    ' O�et�en� duplicit n�zv�
    Dim tmpName As String, idx As Integer
    tmpName = sheetName
    idx = 1
    Do While SheetExists(dstWB, tmpName)
        idx = idx + 1
        tmpName = sheetName & " (" & idx & ")"
    Loop
    sheetName = tmpName

    srcWS.Copy After:=dstWB.Sheets(dstWB.Sheets.Count)
    dstWB.Sheets(dstWB.Sheets.Count).Name = sheetName

    ' Ulo�en� pozn�mek sumarizovan�ch ��dk� do slovn�ku
    Dim poznDict As Object: Set poznDict = CreateObject("Scripting.Dictionary")
    For i = dataStartRow To lastSrcRow
        If Not IsError(srcWS.Cells(i, 23).Value) Then
            If Trim(CStr(srcWS.Cells(i, 23).Value)) <> "" Then
                poznDict(CleanContractNum(srcWS.Cells(i, 4).Value)) = srcWS.Cells(i, 23).Value
            End If
        End If
    Next i

    ' HLAVN� SMY�KA P�ENOSU
    For i = dataStartRow To lastSrcRow
        contractNumRaw = Trim(CStr(srcWS.Cells(i, "D").Value))
        contractNumClean = CleanContractNum(contractNumRaw)

        If contractNumClean = "" Then
            skippedCount = skippedCount + 1
            skippedContracts = skippedContracts & "��dek " & i & " (pr�zdn� ��slo smlouvy)|"
            GoTo NextIteration
        End If

        foundRow = 0
        Set targetRange = dstWS.Range("B2:B" & lastDstRow)
        For Each cell In targetRange
            Dim cellValueRaw As String, cellValueClean As String
            cellValueRaw = Trim(CStr(cell.Value))
            cellValueClean = CleanContractNum(cellValueRaw)
            If cellValueClean = contractNumClean Then
                foundRow = cell.row
                Exit For
            End If
        Next cell

        If foundRow = 0 Then
            ' NOV� SMLOUVA � P�ID�N� ��DKU
            lastDstRow = lastDstRow + 1

            Dim lastOrderNum As Long: lastOrderNum = 0
            If lastDstRow > 2 Then
                If IsNumeric(dstWS.Cells(lastDstRow - 1, "A").Value) Then
                    lastOrderNum = dstWS.Cells(lastDstRow - 1, "A").Value
                End If
            End If
            dstWS.Cells(lastDstRow, "A").Value = lastOrderNum + 1
            With dstWS.Cells(lastDstRow, "A").Font
                .Bold = True
                .Color = vbRed
            End With

            dstWS.Cells(lastDstRow, "B").NumberFormat = "@"
            dstWS.Cells(lastDstRow, "B").HorizontalAlignment = xlRight
            dstWS.Cells(lastDstRow, "B").Value = contractNumRaw

            dstWS.Cells(lastDstRow, "E").Value = srcWS.Cells(i, "B").Value
            dstWS.Cells(lastDstRow, "D").Value = srcWS.Cells(i, "J").Value
            dstWS.Cells(lastDstRow, "H").Value = srcWS.Cells(i, "L").Value

            Call EnsureTripleColumns(dstWS, 12, 13, 14, 1)
            dstWS.Cells(lastDstRow, 12).Value = srcWS.Cells(i, "S").Value
            dstWS.Cells(lastDstRow, 13).Value = srcWS.Cells(i, "U").Value
            dstWS.Cells(lastDstRow, 14).Value = M4Text

            ' POZN�MKA SE P�EN��� JEN U NOV� P�IDAN�CH POLO�EK!
            If poznDict.Exists(contractNumClean) Then
                dstWS.Cells(lastDstRow, 11).Value = poznDict(contractNumClean)
            Else
                dstWS.Cells(lastDstRow, 11).Value = ""
            End If

            addedCount = addedCount + 1
            addedContracts = addedContracts & contractNumRaw & "|"
        Else
            ' AKTUALIZACE EXISTUJ�C�HO ��DKU � POZN�MKA Z�ST�V� P�VODN�!
            wasUpdated = False

            If dstWS.Cells(foundRow, 12).Value = "" Then
                dstWS.Cells(foundRow, 12).Value = srcWS.Cells(i, "S").Value
                wasUpdated = True
            End If
            If dstWS.Cells(foundRow, 13).Value = "" Then
                dstWS.Cells(foundRow, 13).Value = srcWS.Cells(i, "U").Value
                wasUpdated = True
            End If
            If dstWS.Cells(foundRow, 14).Value = "" Then
                dstWS.Cells(foundRow, 14).Value = M4Text
                wasUpdated = True
            End If

            trgCols = NextEmptyTripleLMN(dstWS, foundRow)
            If trgCols(0) > 0 Then
                Call EnsureTripleColumns(dstWS, trgCols(0), trgCols(1), trgCols(2), 1)
                If dstWS.Cells(foundRow, trgCols(0)).Value = "" Then
                    dstWS.Cells(foundRow, trgCols(0)).Value = srcWS.Cells(i, "S").Value
                    wasUpdated = True
                End If
                If dstWS.Cells(foundRow, trgCols(1)).Value = "" Then
                    dstWS.Cells(foundRow, trgCols(1)).Value = srcWS.Cells(i, "U").Value
                    wasUpdated = True
                End If
                If dstWS.Cells(foundRow, trgCols(2)).Value = "" Then
                    dstWS.Cells(foundRow, trgCols(2)).Value = M4Text
                    wasUpdated = True
                End If
            End If

            ' POZN�MKA SE NEP�EN��� p�i aktualizaci existuj�c� smlouvy!

            If wasUpdated Then
                updatedCount = updatedCount + 1
                updatedContracts = updatedContracts & contractNumRaw & "|"
                fullUpdateBVals = fullUpdateBVals & dstWS.Cells(foundRow, "B").Value & "|"
                With dstWS.Cells(foundRow, "A").Font
                    .Bold = True
                    .Color = vbBlue
                End With
            Else
                skippedCount = skippedCount + 1
                skippedContracts = skippedContracts & contractNumRaw & " (��dn� voln� m�sto pro z�pis)|"
            End If
        End If
NextIteration:
    Next i

    Application.ScreenUpdating = True

    ' Smaz�n� star�ho reportu, pokud existuje
    Dim wsReport As Worksheet
    On Error Resume Next
    Set wsReport = dstWB.Sheets("Transfer report")
    If Not wsReport Is Nothing Then
        Application.DisplayAlerts = False
        wsReport.Delete
        Application.DisplayAlerts = True
    End If
    On Error GoTo 0

    ' Vytvo�en� nov�ho listu Transfer report s rekapitulac� zm�n
    Set wsReport = dstWB.Sheets.Add(After:=dstWB.Sheets(dstWB.Sheets.Count))
    wsReport.Name = "Transfer report"

    Dim arrAdd() As String, arrUpd() As String, arrSkip() As String, arrUpdFull() As String
    Dim maxLen As Long, j As Long

    If addedContracts <> "" Then arrAdd = Split(addedContracts, "|") Else ReDim arrAdd(0): arrAdd(0) = ""
    If updatedContracts <> "" Then arrUpd = Split(updatedContracts, "|") Else ReDim arrUpd(0): arrUpd(0) = ""
    If skippedContracts <> "" Then arrSkip = Split(skippedContracts, "|") Else ReDim arrSkip(0): arrSkip(0) = ""
    If fullUpdateBVals <> "" Then arrUpdFull = Split(fullUpdateBVals, "|") Else ReDim arrUpdFull(0): arrUpdFull(0) = ""

    maxLen = UBound(arrAdd)
    If UBound(arrUpd) > maxLen Then maxLen = UBound(arrUpd)
    If UBound(arrUpdFull) > maxLen Then maxLen = UBound(arrUpdFull)
    If UBound(arrSkip) > maxLen Then maxLen = UBound(arrSkip)

    Dim countAdd As Long, countUpd As Long, countUpdFull As Long, countSkip As Long
    countAdd = 0: countUpd = 0: countUpdFull = 0: countSkip = 0
    For j = 0 To UBound(arrAdd)
        If Trim(arrAdd(j)) <> "" Then countAdd = countAdd + 1
    Next j
    For j = 0 To UBound(arrUpd)
        If Trim(arrUpd(j)) <> "" Then countUpd = countUpd + 1
    Next j
    For j = 0 To UBound(arrUpdFull)
        If Trim(arrUpdFull(j)) <> "" Then countUpdFull = countUpdFull + 1
    Next j
    For j = 0 To UBound(arrSkip)
        If Trim(arrSkip(j)) <> "" Then countSkip = countSkip + 1
    Next j

    wsReport.Cells(1, 1).Value = "Nov� p�idan� smlouvy - " & countAdd
    wsReport.Cells(1, 2).Value = "Aktualizovan� smlouvy - " & countUpd
    wsReport.Cells(1, 3).Value = "�pln� form�t - " & countUpdFull
    wsReport.Cells(1, 4).Value = "P�esko�en� z�znamy - " & countSkip
    wsReport.Range("A1:D1").Font.Bold = True

    For j = 0 To maxLen
        If j <= UBound(arrAdd) Then wsReport.Cells(j + 2, 1).NumberFormat = "@": wsReport.Cells(j + 2, 1).Value = arrAdd(j)
        If j <= UBound(arrUpd) Then wsReport.Cells(j + 2, 2).NumberFormat = "@": wsReport.Cells(j + 2, 2).Value = arrUpd(j)
        If j <= UBound(arrUpdFull) Then wsReport.Cells(j + 2, 3).NumberFormat = "@": wsReport.Cells(j + 2, 3).Value = arrUpdFull(j)
        If j <= UBound(arrSkip) Then wsReport.Cells(j + 2, 4).NumberFormat = "@": wsReport.Cells(j + 2, 4).Value = arrSkip(j)
        If j <= UBound(arrUpd) And j <= UBound(arrUpdFull) Then
            If Trim(arrUpd(j)) <> "" And Trim(arrUpdFull(j)) <> "" Then
                If arrUpd(j) <> arrUpdFull(j) Then
                    wsReport.Cells(j + 2, 2).Interior.Color = RGB(255, 199, 44)
                    wsReport.Cells(j + 2, 3).Interior.Color = RGB(255, 199, 44)
                End If
            End If
        End If
    Next j

    wsReport.Columns("A:D").AutoFit

    ' Fin�ln� form�tov�n� v�sledn� tabulky v c�lov�m listu
    Call FormatFinalTableLMN(dstWS)

    MsgBox "Synchronizace dokon�ena!" & vbCrLf & "Report najde� na listu 'Transfer report'." & vbCrLf & vbCrLf & "Z�lohy soubor� byly vytvo�eny.", vbInformation, "V�sledky synchronizace"
End Sub

' Kontrola, zda existuje list v se�itu
Function SheetExists(wb As Workbook, sheetName As String) As Boolean
    Dim sh As Worksheet
    On Error Resume Next
    Set sh = wb.Sheets(sheetName)
    SheetExists = Not sh Is Nothing
    On Error GoTo 0
End Function

' Najde volnou trojici sloupc� LMN/OPQ/...
Function NextEmptyTripleLMN(ws As Worksheet, rowIdx As Long) As Variant
    Dim triple(0 To 2) As Long
    Dim tripleStart As Long
    tripleStart = 12 ' L
    Do
        If ws.Cells(rowIdx, tripleStart).Value = "" And _
           ws.Cells(rowIdx, tripleStart + 1).Value = "" And _
           ws.Cells(rowIdx, tripleStart + 2).Value = "" Then
            triple(0) = tripleStart: triple(1) = tripleStart + 1: triple(2) = tripleStart + 2
            NextEmptyTripleLMN = triple
            Exit Function
        End If
        tripleStart = tripleStart + 3
        If tripleStart + 2 > ws.Columns.Count Then Exit Do
    Loop
    NextEmptyTripleLMN = Array(0, 0, 0)
End Function

' Barevn� rozli�en� trojic sloupc�
Sub EnsureTripleColumns(ws As Worksheet, col1 As Variant, col2 As Variant, col3 As Variant, refRow As Variant)
    Dim colorArr(0 To 2) As Long
    colorArr(0) = RGB(255, 230, 153)
    colorArr(1) = RGB(198, 239, 206)
    colorArr(2) = RGB(221, 235, 247)
    ws.Columns(col1).Interior.Color = colorArr(0)
    ws.Columns(col2).Interior.Color = colorArr(1)
    ws.Columns(col3).Interior.Color = colorArr(2)
End Sub

' P�evod ��sla sloupce na p�smeno (nap�. 28 � AB)
Function ColLetter(ByVal colNum As Long) As String
    Dim s As String
    Do
        s = Chr(((colNum - 1) Mod 26) + 65) & s
        colNum = (colNum - 1) \ 26
    Loop While colNum > 0
    ColLetter = s
End Function

' Fin�ln� form�tov�n� v�sledn� tabulky LMN, OPQ, ...
Sub FormatFinalTableLMN(ws As Worksheet)
    Dim lastRow As Long, lastCol As Long, tripleEnd As Long, c As Long
    lastRow = ws.Cells(ws.Rows.Count, "B").End(xlUp).row
    tripleEnd = 12
    Do While WorksheetFunction.CountA(ws.Columns(tripleEnd)) + WorksheetFunction.CountA(ws.Columns(tripleEnd + 1)) + WorksheetFunction.CountA(ws.Columns(tripleEnd + 2)) > 0
        tripleEnd = tripleEnd + 3
        If tripleEnd + 2 > ws.Columns.Count Then Exit Do
    Loop
    tripleEnd = tripleEnd - 1
    lastCol = tripleEnd

    Dim tripleColors(1 To 3) As Long
    tripleColors(1) = RGB(255, 230, 153)
    tripleColors(2) = RGB(198, 239, 206)
    tripleColors(3) = RGB(221, 235, 247)

    For c = 12 To lastCol Step 3
        ws.Columns(c).Interior.Color = tripleColors(1)
        ws.Columns(c + 1).Interior.Color = tripleColors(2)
        ws.Columns(c + 2).Interior.Color = tripleColors(3)
    Next c

    With ws.Range(ws.Cells(1, 1), ws.Cells(lastRow, lastCol)).Borders
        .LineStyle = xlContinuous
        .Weight = xlThin
    End With

    For c = 12 To lastCol Step 3
        ws.Range(ws.Cells(1, c), ws.Cells(lastRow, c)).Borders(xlEdgeLeft).LineStyle = xlContinuous
        ws.Range(ws.Cells(1, c), ws.Cells(lastRow, c)).Borders(xlEdgeLeft).Weight = xlMedium
        ws.Range(ws.Cells(1, c + 2), ws.Cells(lastRow, c + 2)).Borders(xlEdgeRight).LineStyle = xlContinuous
        ws.Range(ws.Cells(1, c + 2), ws.Cells(lastRow, c + 2)).Borders(xlEdgeRight).Weight = xlMedium
    Next c

    With ws.Range(ws.Cells(1, 1), ws.Cells(2, lastCol)).Borders(xlEdgeTop)
        .LineStyle = xlContinuous
        .Weight = xlMedium
    End With
    With ws.Range(ws.Cells(2, 1), ws.Cells(2, lastCol)).Borders(xlEdgeBottom)
        .LineStyle = xlContinuous
        .Weight = xlMedium
    End With

    ws.Activate
    ws.Rows("3:3").Select
    ActiveWindow.FreezePanes = True

    For c = 12 To lastCol Step 3
        ws.Columns(c).ColumnWidth = 10
        ws.Columns(c + 1).ColumnWidth = 10
        ws.Columns(c + 2).ColumnWidth = 15
    Next c

    ws.Cells(lastRow, 1).Select
End Sub



