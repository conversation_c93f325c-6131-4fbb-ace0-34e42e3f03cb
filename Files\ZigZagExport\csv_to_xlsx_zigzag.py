
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import <PERSON><PERSON>, PatternFill
from openpyxl.chart import <PERSON><PERSON><PERSON>, Reference
from openpyxl.utils.dataframe import dataframe_to_rows
from zipfile import ZipFile
import os

base_dir = os.path.dirname(os.path.abspath(__file__))
csv_path = os.path.join(base_dir, "ZigZag_Export.csv")
xlsx_path = os.path.join(base_dir, "ZigZag_Export.xlsx")
zip_path = os.path.join(base_dir, "ZigZag_Export_Package.zip")

if not os.path.exists(csv_path):
    print("❌ Soubor ZigZag_Export.csv nebyl nalezen.")
    exit()

df = pd.read_csv(csv_path, encoding="cp1250")

wb = Workbook()
ws_data = wb.active
ws_data.title = "ZigZag Data"
ws_summary = wb.create_sheet("Souhrn")

header_fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
green_fill = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")
red_fill = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")
bold_font = Font(bold=True)

for r_idx, row in enumerate(dataframe_to_rows(df, index=False, header=True), 1):
    for c_idx, value in enumerate(row, 1):
        cell = ws_data.cell(row=r_idx, column=c_idx, value=value)
        if r_idx == 1:
            cell.fill = header_fill
            cell.font = bold_font
        elif c_idx == 5:
            try:
                val = float(str(value).replace(",", ""))
                if val > 0:
                    cell.fill = green_fill
                elif val < 0:
                    cell.fill = red_fill
            except:
                pass

for col in ws_data.columns:
    max_len = max(len(str(cell.value)) if cell.value else 0 for cell in col)
    ws_data.column_dimensions[col[0].column_letter].width = max_len + 2

chart = LineChart()
chart.title = "Vývoj ZigZag hodnot"
chart.y_axis.title = "Cena"
chart.x_axis.title = "Index"

try:
    values = Reference(ws_data, min_col=3, min_row=2, max_row=ws_data.max_row)
    labels = Reference(ws_data, min_col=2, min_row=2, max_row=ws_data.max_row)
    chart.add_data(values, titles_from_data=False)
    chart.set_categories(labels)
    ws_data.add_chart(chart, "I2")
except:
    print("⚠️ Graf nebyl vytvořen – chyba ve sloupcích")

try:
    df["Rozdíl v pips"] = df["Rozdíl v pips"].astype(str).str.replace(",", "").str.strip()
    df["Rozdíl v pips"] = pd.to_numeric(df["Rozdíl v pips"], errors='coerce')

    plus_sum = df[df["Rozdíl v pips"] > 0]["Rozdíl v pips"].sum()
    minus_sum = df[df["Rozdíl v pips"] < 0]["Rozdíl v pips"].abs().sum()

    max_row = df.loc[df["Rozdíl v pips"].idxmax()].to_dict()
    min_row = df.loc[df["Rozdíl v pips"].idxmin()].to_dict()

    summary = [
        ("Suma +", plus_sum),
        ("Suma -", minus_sum),
        ("Celkový rozdíl", plus_sum - minus_sum),
        ("Počet bodů", len(df)),
        ("Maximální růst", f"{max_row['Datum a čas']} | {max_row['Rozdíl v pips']} bodů | {max_row['Rozdíl %']}"),
        ("Maximální pokles", f"{min_row['Datum a čas']} | {min_row['Rozdíl v pips']} bodů | {min_row['Rozdíl %']}")
    ]

    for i, (label, value) in enumerate(summary, 1):
        ws_summary.cell(row=i, column=1, value=label)
        ws_summary.cell(row=i, column=2, value=value)

except Exception as e:
    ws_summary.cell(row=1, column=1, value="Chyba při výpočtu sumáře")
    ws_summary.cell(row=2, column=1, value=str(e))

wb.save(xlsx_path)
print("✅ Soubor ZigZag_Export.xlsx byl vytvořen.")

with ZipFile(zip_path, 'w') as zipf:
    zipf.write(csv_path, arcname="ZigZag_Export.csv")
    zipf.write(xlsx_path, arcname="ZigZag_Export.xlsx")

print("📦 ZIP archiv ZigZag_Export_Package.zip byl vytvořen.")
