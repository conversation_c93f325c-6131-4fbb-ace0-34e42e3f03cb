import pandas as pd

# Test jak Python čte procentní hodnoty z Excelu
try:
    file_path = 'FT provize old.xlsx'
    df = pd.read_excel(file_path, sheet_name='All-In-One')
    
    print("=== TEST PROCENTNÍCH HODNOT VE SLOUPCI J ===")
    
    # Zobraz prvních 10 hodnot ze sloupce J
    j_column = df['Podíl na provizi']
    print("Prvních 10 hodnot ze sloupce J (Podíl na provizi):")
    for i, value in enumerate(j_column.head(10)):
        print(f"  Řádek {i+2}: {value} (typ: {type(value)})")
    
    print()
    print("Statistiky sloupce J:")
    print(f"  Min: {j_column.min()}")
    print(f"  Max: {j_column.max()}")
    print(f"  Průměr: {j_column.mean():.3f}")
    print(f"  Unikátní hodnoty: {sorted(j_column.unique())}")
    
    # Test sčítání
    print()
    print("=== TEST SČÍTÁNÍ ===")
    test_values = [0.3, 0.4, 0.2]
    print(f"Test hodnoty: {test_values}")
    print(f"Součet: {sum(test_values)}")
    
    # Najdi příklad duplicitní smlouvy
    print()
    print("=== PŘÍKLAD DUPLICITNÍ SMLOUVY ===")
    filtered_df = df[df['Období'] == '01-2025']
    duplicate_contracts = filtered_df['Číslo smlouvy'].value_counts()
    duplicates = duplicate_contracts[duplicate_contracts > 1]
    
    if len(duplicates) > 0:
        example_contract = duplicates.index[0]
        example_records = filtered_df[filtered_df['Číslo smlouvy'] == example_contract]
        
        print(f"Smlouva: {example_contract}")
        print("Záznamy:")
        for idx, row in example_records.iterrows():
            print(f"  Řádek {idx+2}: J={row['Podíl na provizi']}, K={row['Odměna']}")
        
        # Spočítej součty
        sum_j = example_records['Podíl na provizi'].sum()
        sum_k = example_records['Odměna'].sum()
        print(f"Součty: J={sum_j}, K={sum_k}")

except Exception as e:
    print(f"Chyba: {e}")
    import traceback
    traceback.print_exc()
