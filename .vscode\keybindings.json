[{"key": "ctrl+f7", "command": "mql_tools.compileFile", "when": "editorLangId == cpp && resourceExtname =~ /\\.(mq4|mq5)$/"}, {"key": "ctrl+shift+f7", "command": "mql_tools.compileScript", "when": "editorLangId == cpp && resourceExtname =~ /\\.(mq4|mq5)$/"}, {"key": "ctrl+alt+m", "command": "mql_tools.openInMetaEditor", "when": "editorLangId == cpp && resourceExtname =~ /\\.(mq4|mq5|mqh)$/"}, {"key": "ctrl+alt+t", "command": "mql_tools.openTerminal"}]