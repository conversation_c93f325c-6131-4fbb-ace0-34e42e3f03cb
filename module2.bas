Attribute VB_Name = "Module2"

' Pomocná funkce pro čištění <PERSON> (stejná jako v Module1)
Function CleanContractNum2(num As String) As String
    num = Trim(CStr(num))
    If InStr(num, "/") > 0 Then num = Left(num, InStr(num, "/") - 1)
    num = Replace(num, ".", "")
    num = Replace(num, " ", "")
    CleanContractNum2 = num
End Function

' Funkce: Vrátí unikátní seznam období ze sloupce L jako Dictionary
Function UniqueObdobi(srcWS As Worksheet, colObdobi As Long) As Object
    Dim dict As Object: Set dict = CreateObject("Scripting.Dictionary")
    Dim lastRow As Long, i As Long, v As String
    lastRow = srcWS.Cells(srcWS.Rows.Count, colObdobi).End(xlUp).row
    For i = 2 To lastRow
        v = Trim(CStr(srcWS.Cells(i, colObdobi).Value))
        If v <> "" And Not dict.Exists(v) Then dict.Add v, 1
    Next i
    Set UniqueObdobi = dict
End Function

' Výběr období pomocí InputBoxu
Function SelectObdobiFromList_InputBox(srcWS As Worksheet, colObdobi As Long) As String
    Dim dict As Object: Set dict = UniqueObdobi(srcWS, colObdobi)
    If dict.Count = 0 Then
        SelectObdobiFromList_InputBox = ""
        Exit Function
    End If
    Dim seznam As String: seznam = ""
    Dim key
    For Each key In dict.Keys
        seznam = seznam & key & vbCrLf
    Next key
    Dim vyber As String
    vyber = InputBox("Vyber období (zkopíruj nebo napiš jednu z těchto hodnot):" & vbCrLf & vbCrLf & seznam, "Výběr období")
    If vyber = "" Then
        SelectObdobiFromList_InputBox = ""
    Else
        SelectObdobiFromList_InputBox = vyber
    End If
End Function

' Sumarizace: slučování řádků podle čísla smlouvy ve sloupci A
Sub VytvorSumarizaci2(srcWS As Worksheet, vybraneObdobi As String)
    Application.DisplayAlerts = False
    On Error Resume Next
    srcWS.Parent.Sheets("Sumarizace").Delete
    Application.DisplayAlerts = True
    On Error GoTo 0

    ' Vytvoř kopii listu
    srcWS.Copy After:=srcWS
    Dim wsSum As Worksheet
    Set wsSum = srcWS.Parent.Sheets(srcWS.Index + 1)
    wsSum.Name = "Sumarizace"

    Dim startRow As Long: startRow = 2
    Dim lastRow As Long: lastRow = wsSum.Cells(wsSum.Rows.Count, 1).End(xlUp).row ' Sloupec A - číslo smlouvy
    If lastRow < startRow Then Exit Sub

    ' Filtruj podle vybraného období (sloupec L)
    Dim i As Long
    For i = lastRow To startRow Step -1
        If Trim(CStr(wsSum.Cells(i, 12).Value)) <> vybraneObdobi Then ' Sloupec L = 12
            wsSum.Rows(i).Delete
            lastRow = lastRow - 1
        End If
    Next i

    ' Aktualizuj lastRow po filtrování
    lastRow = wsSum.Cells(wsSum.Rows.Count, 1).End(xlUp).row
    If lastRow < startRow Then Exit Sub

    ' Slovník pro sledování smluv a jejich počtu
    Dim dict As Object: Set dict = CreateObject("Scripting.Dictionary")
    Dim countDict As Object: Set countDict = CreateObject("Scripting.Dictionary")
    Dim key As String

    ' Projdi všechny řádky a sumarizuj podle čísla smlouvy (A)
    For i = startRow To lastRow
        key = Trim(CStr(wsSum.Cells(i, 1).Value)) ' Sloupec A - číslo smlouvy
        If key <> "" Then
            If Not dict.Exists(key) Then
                dict.Add key, i
                countDict.Add key, 1
            Else
                ' Najdi cílový řádek pro sumarizaci
                Dim targetRow As Long: targetRow = dict(key)
                
                ' Sečti hodnoty ze sloupce K (s ošetřením prázdných hodnot)
                ' Sloupec K (11) - číselné hodnoty k sumarizaci
                If IsNumeric(wsSum.Cells(i, 11).Value) Then
                    wsSum.Cells(targetRow, 11).Value = Val(wsSum.Cells(targetRow, 11).Value) + Val(wsSum.Cells(i, 11).Value)
                End If
                
                countDict(key) = countDict(key) + 1
                wsSum.Rows(i).Delete
                i = i - 1
                lastRow = lastRow - 1
            End If
        End If
    Next i

    ' Zvýrazni sumarizované řádky a přidej poznámky
    Dim sumKey As Variant
    For Each sumKey In dict.Keys
        Dim rowIdx As Long: rowIdx = dict(sumKey)
        If countDict(sumKey) > 1 Then
            ' Zvýrazni celý řádek žlutou barvou
            With wsSum.Range(wsSum.Cells(rowIdx, 1), wsSum.Cells(rowIdx, wsSum.UsedRange.Columns.Count))
                .Interior.Color = RGB(255, 230, 153)
            End With
            ' Přidej poznámku do sloupce P (16)
            With wsSum.Cells(rowIdx, 16) ' Sloupec P = 16
                .Value = "Sumarizováno: " & countDict(sumKey) & " položek"
                .Font.Color = RGB(0, 102, 204)
                .Font.Bold = True
            End With
        End If
    Next sumKey
End Sub

' HLAVNÍ MAKRO pro zpracování FT provize old.xlsx
' Struktura souboru:
' Sloupec A: Číslo smlouvy (klíč pro sumarizaci)
' Sloupec K: Číselné hodnoty k sumarizaci
' Sloupec L: Období (filtr - zadává se inputem)
' Sloupec P: Poznámka (kam se zapíše sumarizační text)
' LOGIKA: Filtruje podle období (L), sumarizuje podle smlouvy (A), sčítá sloupec K
Sub ProcessFTProvizeOld()
    Dim srcFile As Variant
    Dim srcWB As Workbook
    Dim srcWS As Worksheet

    ' Výběr zdrojového souboru
    srcFile = Application.GetOpenFilename("Excel Files (*.xlsx), *.xlsx", , "Vyber soubor FT provize old.xlsx")
    If srcFile = False Then
        MsgBox "Výběr zrušen."
        Exit Sub
    End If
    
    ' Otevři zdrojový soubor
    Set srcWB = Workbooks.Open(srcFile)
    Set srcWS = srcWB.Sheets(1) ' Vždy první list (All-In-One)
    
    ' Načti období ze sloupce L a nech uživatele vybrat
    Dim vybraneObdobi As String
    vybraneObdobi = SelectObdobiFromList_InputBox(srcWS, 12) ' Sloupec L = 12

    If vybraneObdobi = "" Then
        MsgBox "Výběr období zrušen.", vbExclamation
        srcWB.Close False
        Exit Sub
    End If

    ' Vytvoř sumarizaci pro vybrané období
    Call VytvorSumarizaci2(srcWS, vybraneObdobi)
    
    MsgBox "Sumarizace dokončena!" & vbCrLf & "Výsledky najdeš na listu 'Sumarizace'.", vbInformation, "Výsledky zpracování"
End Sub
