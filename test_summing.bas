Sub TestSummingLogic()
    ' Test sčítání pro sloupce J a K na otevřené tabulce
    Dim ws As Worksheet
    Set ws = ActiveSheet
    
    Debug.Print "=== TEST SČÍTÁNÍ SLOUPCŮ J A K ==="
    Debug.Print "List: " & ws.Name
    
    ' Najdi první duplicitní smlouvu pro test
    Dim i As Long, j <PERSON> Long
    Dim foundDuplicate As Boolean
    Dim testContract As String
    Dim testRows As Collection
    Set testRows = New Collection
    
    ' Hledej duplicitní smlouvu v období 01-2025
    For i = 2 To 50 ' Test prvních 50 řádků
        If ws.Cells(i, 12).Value = "01-2025" Then ' Sloupec L = období
            testContract = ws.Cells(i, 1).Value ' Sloupec A = číslo smlouvy
            
            ' H<PERSON><PERSON> da<PERSON><PERSON> výskyt této smlouvy
            For j = i + 1 To 100
                If ws.Cells(j, 1).Value = testContract And ws.Cells(j, 12).Value = "01-2025" Then
                    foundDuplicate = True
                    testRows.Add i
                    testRows.Add j
                    Exit For
                End If
            Next j
            
            If foundDuplicate Then Exit For
        End If
    Next i
    
    If foundDuplicate Then
        Debug.Print "Nalezena duplicitní smlouva: " & testContract
        Debug.Print "Řádky: " & testRows(1) & " a " & testRows(2)
        
        ' Zobraz hodnoty
        Dim row1 As Long, row2 As Long
        row1 = testRows(1)
        row2 = testRows(2)
        
        Debug.Print "Řádek " & row1 & ":"
        Debug.Print "  J=" & ws.Cells(row1, 10).Value & " (typ: " & TypeName(ws.Cells(row1, 10).Value) & ")"
        Debug.Print "  K=" & ws.Cells(row1, 11).Value & " (typ: " & TypeName(ws.Cells(row1, 11).Value) & ")"
        
        Debug.Print "Řádek " & row2 & ":"
        Debug.Print "  J=" & ws.Cells(row2, 10).Value & " (typ: " & TypeName(ws.Cells(row2, 10).Value) & ")"
        Debug.Print "  K=" & ws.Cells(row2, 11).Value & " (typ: " & TypeName(ws.Cells(row2, 11).Value) & ")"
        
        ' Test sčítání J (nová metoda)
        Dim currentJ As Double, addJ As Double
        currentJ = 0: addJ = 0
        If IsNumeric(ws.Cells(row1, 10).Value) Then currentJ = ws.Cells(row1, 10).Value
        If IsNumeric(ws.Cells(row2, 10).Value) Then addJ = ws.Cells(row2, 10).Value
        Dim sumJ As Double: sumJ = currentJ + addJ
        
        ' Test sčítání K (opravená metoda)
        Dim currentK As Double, addK As Double
        currentK = 0: addK = 0
        If IsNumeric(ws.Cells(row1, 11).Value) Then currentK = Val(ws.Cells(row1, 11).Value)
        If IsNumeric(ws.Cells(row2, 11).Value) Then addK = Val(ws.Cells(row2, 11).Value)
        Dim sumK As Double: sumK = currentK + addK
        
        Debug.Print "Výsledky sčítání:"
        Debug.Print "  J: " & currentJ & " + " & addJ & " = " & sumJ
        Debug.Print "  K: " & currentK & " + " & addK & " = " & sumK
        
        ' Test Val() funkce
        Debug.Print "Test Val() funkce:"
        Debug.Print "  Val(J1): " & Val(ws.Cells(row1, 10).Value)
        Debug.Print "  Val(J2): " & Val(ws.Cells(row2, 10).Value)
        Debug.Print "  Val(K1): " & Val(ws.Cells(row1, 11).Value)
        Debug.Print "  Val(K2): " & Val(ws.Cells(row2, 11).Value)
        
    Else
        Debug.Print "Nebyla nalezena duplicitní smlouva pro test"
    End If
    
    MsgBox "Test dokončen. Výsledky v Immediate Window (Ctrl+G)"
End Sub
