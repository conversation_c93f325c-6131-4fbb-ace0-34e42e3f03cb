//+------------------------------------------------------------------+
//|                                               MaxProfitsEA_Lib.mq4 |
//|                      Copyright 2024, Custom Scripts              |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Custom Scripts"
#property link      ""
#property version   "1.00"
#property strict

// Import knihovny MaxProfitsLib
#include <MaxProfitsLib.mqh>

// Vstupní parametry
input int    MagicNo           = 777;        // Magické číslo pro sledování obchodů
input int    MaxOrdersToTrack  = 5;          // Maximální počet obchodů k sledování
input bool   FileExport        = true;       // Povolit export do souboru
input string FileName          = "MaxProfits.txt"; // Název souboru pro export
input bool   DebugMode         = false;      // Zapnout debug výpisy
input color  ProfitTextColor   = clrWhite;   // Barva textu pro zobrazení profitů
input int    FontSize          = 10;         // Velikost písma pro zobrazení profitů
input string FontName          = "Arial";    // Název písma pro zobrazení profitů

// Nastavení zobrazení
input ENUM_BASE_CORNER DisplayCorner = CORNER_RIGHT_UPPER; // Roh pro zobrazení textových objektů (0=levý horní, 1=pravý horní, 2=levý dolní, 3=pravý dolní)
input int    XOffset           = 150;        // X-ový offset pro textové objekty (pro pravé rohy: vzdálenost od pravého okraje)
input int    YOffset           = 20;         // Y-ový offset pro textové objekty

// Globální instance třídy CMaxProfits
CMaxProfits* maxProfits = NULL;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    // Vytvoření instance třídy CMaxProfits
    maxProfits = new CMaxProfits("MaxProfits", MagicNo, MaxOrdersToTrack,
                                 FileExport, FileName, DebugMode,
                                 ProfitTextColor, FontSize, FontName,
                                 DisplayCorner, XOffset, YOffset);

    if(maxProfits == NULL) {
        Print("Chyba při vytváření instance CMaxProfits!");
        return INIT_FAILED;
    }

    Print("MaxProfitsEA_Lib spuštěn");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // Uvolnění instance třídy CMaxProfits
    if(maxProfits != NULL) {
        delete maxProfits;
        maxProfits = NULL;
    }

    Print("MaxProfitsEA_Lib deinicializován");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    static datetime Last_M1_Open = 0;

    if(maxProfits == NULL) return;

    // Vytvoření editboxu pro MagicNumber (pokud ještě neexistuje)
    maxProfits.CreateMagicNumberEdit();

    // Aktualizace MagicNumber z editboxu
    maxProfits.UpdateMagicNumberFromEdit();

    // Sledování maximálních profitů v reálném čase
    maxProfits.UpdateRealTime();

    // Aktualizace komentáře s informacemi o MaxProfitech
    maxProfits.UpdateComment();

    // Blok pro provedení při každém otevření nové M1 svíčky
    datetime currentM1BarTime = iTime(Symbol(), PERIOD_M1, 0);

    if(Last_M1_Open != currentM1BarTime) {
        Last_M1_Open = currentM1BarTime;

        // Aktualizace MaxProfit na základě High/Low svíčky
        maxProfits.UpdateOnNewCandle();
    }
}
