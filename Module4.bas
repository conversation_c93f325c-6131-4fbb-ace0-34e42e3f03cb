Attribute VB_Name = "Module1"
' Pomocn� funkce pro vy�i�t�n� ��sla smlouvy
Function CleanContractNum(num As String) As String
    num = Trim(CStr(num))
    If InStr(num, "/") > 0 Then num = Left(num, InStr(num, "/") - 1)
    num = Replace(num, ".", "")
    num = Replace(num, " ", "")
    CleanContractNum = num
End Function

' Funkce: Vr�t� unik�tn� seznam m�s�c� �hrady jako Dictionary
Function UniqueMesice(srcWS As Worksheet, colMesicUhrady As Long) As Object
    Dim dict As Object: Set dict = CreateObject("Scripting.Dictionary")
    Dim lastRow As Long, i As Long, v As String
    lastRow = srcWS.Cells(srcWS.Rows.Count, colMesicUhrady).End(xlUp).row
    For i = 2 To lastRow
        v = Trim(CStr(srcWS.Cells(i, colMesicUhrady).Value))
        If v <> "" And Not dict.Exists(v) Then dict.Add v, 1
    Next i
    Set UniqueMesice = dict
End Function

' V�b�r m�s�ce �hrady pomoc� InputBoxu
Function SelectMesicUhradyFromList_InputBox(srcWS As Worksheet, colMesicUhrady As Long) As String
    Dim dict As Object: Set dict = UniqueMesice(srcWS, colMesicUhrady)
    If dict.Count = 0 Then
        SelectMesicUhradyFromList_InputBox = ""
        Exit Function
    End If
    Dim seznam As String: seznam = ""
    Dim key
    For Each key In dict.Keys
        seznam = seznam & key & vbCrLf
    Next key
    Dim vyber As String
    vyber = InputBox("Vyber m�s�c �hrady (zkop�ruj nebo napi� jednu z t�chto hodnot):" & vbCrLf & vbCrLf & seznam, "V�b�r m�s�ce �hrady")
    If vyber = "" Then
        SelectMesicUhradyFromList_InputBox = ""
    Else
        SelectMesicUhradyFromList_InputBox = vyber
    End If
End Function

' Najde prvn� volnou trojici sloupc� pro z�pis (LMN, OPQ, RST�)
Function NextEmptyTripleLMN(ws As Worksheet, rowIdx As Long) As Variant
    Dim triple(0 To 2) As Long
    Dim tripleStart As Long
    tripleStart = 12 ' L
    Do
        If ws.Cells(rowIdx, tripleStart).Value = "" And _
           ws.Cells(rowIdx, tripleStart + 1).Value = "" And _
           ws.Cells(rowIdx, tripleStart + 2).Value = "" Then
            triple(0) = tripleStart: triple(1) = tripleStart + 1: triple(2) = tripleStart + 2
            NextEmptyTripleLMN = triple
            Exit Function
        End If
        tripleStart = tripleStart + 3
        If tripleStart + 2 > ws.Columns.Count Then Exit Do
    Loop
    NextEmptyTripleLMN = Array(0, 0, 0)
End Function

' Barevn� rozli�en� trojic sloupc�
Sub EnsureTripleColumns(ws As Worksheet, col1 As Variant, col2 As Variant, col3 As Variant, refRow As Variant)
    Dim colorArr(0 To 2) As Long
    colorArr(0) = RGB(255, 230, 153)
    colorArr(1) = RGB(198, 239, 206)
    colorArr(2) = RGB(221, 235, 247)
    ws.Columns(col1).Interior.Color = colorArr(0)
    ws.Columns(col2).Interior.Color = colorArr(1)
    ws.Columns(col3).Interior.Color = colorArr(2)
End Sub

' Fin�ln� form�tov�n� v�sledn� tabulky LMN, OPQ, ...
Sub FormatFinalTableLMN(ws As Worksheet)
    Dim lastRow As Long, lastCol As Long, tripleEnd As Long, c As Long
    lastRow = ws.Cells(ws.Rows.Count, "B").End(xlUp).row
    tripleEnd = 12
    Do While WorksheetFunction.CountA(ws.Columns(tripleEnd)) + WorksheetFunction.CountA(ws.Columns(tripleEnd + 1)) + WorksheetFunction.CountA(ws.Columns(tripleEnd + 2)) > 0
        tripleEnd = tripleEnd + 3
        If tripleEnd + 2 > ws.Columns.Count Then Exit Do
    Loop
    tripleEnd = tripleEnd - 1
    lastCol = tripleEnd

    Dim tripleColors(1 To 3) As Long
    tripleColors(1) = RGB(255, 230, 153)
    tripleColors(2) = RGB(198, 239, 206)
    tripleColors(3) = RGB(221, 235, 247)

    For c = 12 To lastCol Step 3
        ws.Columns(c).Interior.Color = tripleColors(1)
        ws.Columns(c + 1).Interior.Color = tripleColors(2)
        ws.Columns(c + 2).Interior.Color = tripleColors(3)
    Next c

    With ws.Range(ws.Cells(1, 1), ws.Cells(lastRow, lastCol)).Borders
        .LineStyle = xlContinuous
        .Weight = xlThin
    End With

    For c = 12 To lastCol Step 3
        ws.Range(ws.Cells(1, c), ws.Cells(lastRow, c)).Borders(xlEdgeLeft).LineStyle = xlContinuous
        ws.Range(ws.Cells(1, c), ws.Cells(lastRow, c)).Borders(xlEdgeLeft).Weight = xlMedium
        ws.Range(ws.Cells(1, c + 2), ws.Cells(lastRow, c + 2)).Borders(xlEdgeRight).LineStyle = xlContinuous
        ws.Range(ws.Cells(1, c + 2), ws.Cells(lastRow, c + 2)).Borders(xlEdgeRight).Weight = xlMedium
    Next c

    With ws.Range(ws.Cells(1, 1), ws.Cells(2, lastCol)).Borders(xlEdgeTop)
        .LineStyle = xlContinuous
        .Weight = xlMedium
    End With
    With ws.Range(ws.Cells(2, 1), ws.Cells(2, lastCol)).Borders(xlEdgeBottom)
        .LineStyle = xlContinuous
        .Weight = xlMedium
    End With

    ws.Activate
    ws.Rows("3:3").Select
    ActiveWindow.FreezePanes = True

    For c = 12 To lastCol Step 3
        ws.Columns(c).ColumnWidth = 10
        ws.Columns(c + 1).ColumnWidth = 10
        ws.Columns(c + 2).ColumnWidth = 15
    Next c

    ws.Cells(lastRow, 1).Select
End Sub

' Sumarizace: slu�ov�n� ��dk� podle ��sla smlouvy + zv�razn�n�, sou�et, pozn�mky atd.
Sub VytvorSumarizaci(srcWS As Worksheet, _
    ByVal fCisloSml As Long, ByVal fProvize As Long, ByVal fOdmena As Long, ByVal fPoznamka As Long)
    Application.DisplayAlerts = False
    On Error Resume Next
    srcWS.Parent.Sheets("Sumarizace").Delete
    Application.DisplayAlerts = True
    On Error GoTo 0

    srcWS.Copy After:=srcWS
    Dim wsSum As Worksheet
    Set wsSum = srcWS.Parent.Sheets(srcWS.Index + 1)
    wsSum.Name = "Sumarizace"

    Dim startRow As Long: startRow = 2
    Dim lastRow As Long: lastRow = wsSum.Cells(wsSum.Rows.Count, fCisloSml).End(xlUp).row
    If lastRow < startRow Then Exit Sub

    With wsSum
        .Range(.Cells(startRow, fPoznamka), .Cells(lastRow, fPoznamka)).Value = _
            .Range(.Cells(startRow, fPoznamka), .Cells(lastRow, fPoznamka)).Value
    End With

    Dim dict As Object: Set dict = CreateObject("Scripting.Dictionary")
    Dim countDict As Object: Set countDict = CreateObject("Scripting.Dictionary")
    Dim i As Long, key As String

    For i = startRow To lastRow
        key = Trim(CStr(wsSum.Cells(i, fCisloSml).Value))
        If key <> "" Then
            If Not dict.Exists(key) Then
                dict.Add key, i
                countDict.Add key, 1
            Else
                Dim targetRow As Long: targetRow = dict(key)

                ' Sečti hodnoty ze sloupců Provize a Odmena (PŘESNÁ METODA Z MODULE2.BAS)
                ' Sloupec Provize - podíly k sumarizaci
                If IsNumeric(wsSum.Cells(i, fProvize).Value) Then
                    wsSum.Cells(targetRow, fProvize).Value = wsSum.Cells(targetRow, fProvize).Value + wsSum.Cells(i, fProvize).Value
                End If
                ' Sloupec Odmena se vypočítá později pomocí vzorce K = I * J
                countDict(key) = countDict(key) + 1
                wsSum.Rows(i).Delete
                i = i - 1
                lastRow = lastRow - 1
            End If
        End If
    Next i

    Dim sumKey As Variant
    For Each sumKey In dict.Keys
        Dim rowIdx As Long: rowIdx = dict(sumKey)
        If countDict(sumKey) > 1 Then
            With wsSum.Range(wsSum.Cells(rowIdx, 1), wsSum.Cells(rowIdx, wsSum.UsedRange.Columns.Count))
                .Interior.Color = RGB(255, 230, 153)
            End With
            With wsSum.Cells(rowIdx, fPoznamka)
                .Value = "Sumarizov�no: " & countDict(sumKey) & " polo�ek"
                .Font.Color = RGB(0, 102, 204)
                .Font.Bold = True
            End With
        End If
    Next sumKey

    ' Po dokončení sumarizace doplň vzorce Odmena = Provize * Podíl (PŘESNÁ METODA Z MODULE2.BAS)
    If fOdmena = 11 Then ' Pokud je Odmena ve sloupci K
        For i = startRow To wsSum.Cells(wsSum.Rows.Count, fCisloSml).End(xlUp).row
            If wsSum.Cells(i, fCisloSml).Value <> "" Then ' Pokud má číslo smlouvy
                ' Nastav vzorec K = I * J (Odmena = Provize * Podíl)
                wsSum.Cells(i, fOdmena).Formula = "=I" & i & "*J" & i
            End If
        Next i
    End If
End Sub

' ----------------- HLAVN� MAKRO -----------------

Sub SyncContractsDataWithDialog()
    Dim formatChoice As Integer
    formatChoice = MsgBox("Vyber form�t zdrojov� tabulky:" & vbCrLf & _
                          "Ano = Form�t 1 (p�vodn�, sloupce D/G/J/N...)" & vbCrLf & _
                          "Ne = Form�t 2 (nov�, sloupce A/B/F/E...)", vbYesNoCancel + vbQuestion, "Volba form�tu")
    If formatChoice = vbCancel Then
        MsgBox "Operace zru�ena."
        Exit Sub
    End If

    Dim srcWB As Workbook, dstWB As Workbook
    Dim srcWS As Worksheet, dstWS As Worksheet
    Dim srcFile As Variant, dstFile As Variant
    srcFile = Application.GetOpenFilename("Excel Files (*.xls*), *.xls*", , "Vyber ZDROJOV� soubor (oDM�NY)")
    If srcFile = False Then MsgBox "V�b�r zru�en.": Exit Sub
    dstFile = Application.GetOpenFilename("Excel Files (*.xls*), *.xls*", , "Vyber C�LOV� soubor (dEN�K UZAV�EN�CH SMLUV)")
    If dstFile = False Then MsgBox "V�b�r zru�en.": Exit Sub

    Set srcWB = Workbooks.Open(srcFile)
    Set dstWB = Workbooks.Open(dstFile)
    Set srcWS = srcWB.Sheets(1)
    Set dstWS = dstWB.Sheets(1)

    ' ==== Nastaven� mapov�n� sloupc� podle posledn� tabulky ====
    Dim SrcCol_CisloSmlouvy As Long, SrcCol_Datum As Long, SrcCol_PocatekPS As Long, SrcCol_Klient As Long
    Dim SrcCol_Produkt As Long, SrcCol_Poznamka As Long, SrcCol_Provize As Long, SrcCol_Odmena As Long
    Dim SrcCol_MesicUhrady As Variant, SrcCol_Pojistovna As Long

    If formatChoice = vbYes Then
        ' Form�t 1 (p�vodn�)
        SrcCol_CisloSmlouvy = 4    ' D
        SrcCol_Datum = 14          ' N
        SrcCol_PocatekPS = 10      ' J
        SrcCol_Klient = 2          ' B
        SrcCol_Pojistovna = 7      ' G
        SrcCol_Produkt = 12        ' L
        SrcCol_Poznamka = 23       ' W
        SrcCol_Provize = 19        ' S
        SrcCol_Odmena = 21         ' U
        SrcCol_MesicUhrady = "M4"  ' Speci�ln� - z bu�ky M4
    Else
        ' Form�t 2 (nov�)
        SrcCol_CisloSmlouvy = 1    ' A
        SrcCol_Datum = 5           ' E
        SrcCol_PocatekPS = 6       ' F
        SrcCol_Klient = 3          ' C
        SrcCol_Pojistovna = 2      ' B
        SrcCol_Produkt = 8         ' H
        SrcCol_Poznamka = 16       ' P
        SrcCol_Provize = 9         ' I
        SrcCol_Odmena = 11         ' K
        SrcCol_MesicUhrady = 12    ' L
    End If

    ' Vytvo� sumarizaci ve zdrojov�m se�it�
    Call VytvorSumarizaci(srcWS, SrcCol_CisloSmlouvy, SrcCol_Provize, SrcCol_Odmena, SrcCol_Poznamka)
    Set srcWS = srcWB.Sheets("Sumarizace")

    ' Pro form�t 2 v�b�r m�s�ce �hrady
    Dim MesicUhradyFiltrovat As String
    If formatChoice = vbNo Then
        MesicUhradyFiltrovat = SelectMesicUhradyFromList_InputBox(srcWS, CLng(SrcCol_MesicUhrady))
        If MesicUhradyFiltrovat = "" Then
            MsgBox "V�b�r m�s�ce �hrady zru�en.", vbExclamation
            Exit Sub
        End If
    End If

    ' P�enos dat - podle mapov�n�
    Dim poznDict As Object: Set poznDict = CreateObject("Scripting.Dictionary")
    Dim lastSrcRow As Long, dataStartRow As Long
    dataStartRow = 2
    lastSrcRow = srcWS.Cells(srcWS.Rows.Count, SrcCol_CisloSmlouvy).End(xlUp).row

    Dim i As Long
    For i = dataStartRow To lastSrcRow
        Dim contractNumClean As String
        contractNumClean = CleanContractNum(srcWS.Cells(i, SrcCol_CisloSmlouvy).Value)
        If Trim(CStr(srcWS.Cells(i, SrcCol_Poznamka).Value)) <> "" Then
            poznDict(contractNumClean) = srcWS.Cells(i, SrcCol_Poznamka).Value
        End If
    Next i

    Dim lastDstRow As Long
    lastDstRow = dstWS.Cells(dstWS.Rows.Count, "B").End(xlUp).row

    Application.ScreenUpdating = False

    Dim foundRow As Long, cell As Range, targetRange As Range
    Dim addedCount As Long, updatedCount As Long, skippedCount As Long
    Dim addedContracts As String, updatedContracts As String, skippedContracts As String
    Dim trgCols As Variant, wasUpdated As Boolean

    For i = dataStartRow To lastSrcRow

        If formatChoice = vbNo Then
            If Trim(CStr(srcWS.Cells(i, SrcCol_MesicUhrady).Value)) <> Trim(MesicUhradyFiltrovat) Then
                skippedCount = skippedCount + 1
                skippedContracts = skippedContracts & "��dek " & i & " (jin� m�s�c �hrady)|"
                GoTo NextIteration
            End If
        End If

        Dim contractNumRaw As String
        contractNumRaw = Trim(CStr(srcWS.Cells(i, SrcCol_CisloSmlouvy).Value))
        contractNumClean = CleanContractNum(contractNumRaw)
        If contractNumClean = "" Then
            skippedCount = skippedCount + 1
            skippedContracts = skippedContracts & "��dek " & i & " (pr�zdn� ��slo smlouvy)|"
            GoTo NextIteration
        End If

        foundRow = 0
        Set targetRange = dstWS.Range("B2:B" & lastDstRow)
        For Each cell In targetRange
            Dim cellValueRaw As String, cellValueClean As String
            cellValueRaw = Trim(CStr(cell.Value))
            cellValueClean = CleanContractNum(cellValueRaw)
            If cellValueClean = contractNumClean Then
                foundRow = cell.row
                Exit For
            End If
        Next cell

        If foundRow = 0 Then
            lastDstRow = lastDstRow + 1
            Dim lastOrderNum As Long: lastOrderNum = 0
            If lastDstRow > 2 Then
                If IsNumeric(dstWS.Cells(lastDstRow - 1, "A").Value) Then
                    lastOrderNum = dstWS.Cells(lastDstRow - 1, "A").Value
                End If
            End If
            dstWS.Cells(lastDstRow, "A").Value = lastOrderNum + 1
            With dstWS.Cells(lastDstRow, "A").Font
                .Bold = True
                .Color = vbRed
            End With

            dstWS.Cells(lastDstRow, "B").Value = contractNumRaw
            dstWS.Cells(lastDstRow, "C").Value = srcWS.Cells(i, SrcCol_Datum).Value
            dstWS.Cells(lastDstRow, "D").Value = srcWS.Cells(i, SrcCol_PocatekPS).Value
            dstWS.Cells(lastDstRow, "E").Value = srcWS.Cells(i, SrcCol_Klient).Value
            dstWS.Cells(lastDstRow, "F").Value = srcWS.Cells(i, SrcCol_Pojistovna).Value
            dstWS.Cells(lastDstRow, "H").Value = srcWS.Cells(i, SrcCol_Produkt).Value

            If poznDict.Exists(contractNumClean) Then
                dstWS.Cells(lastDstRow, "K").Value = poznDict(contractNumClean)
            Else
                dstWS.Cells(lastDstRow, "K").Value = ""
            End If
            dstWS.Cells(lastDstRow, "K").Interior.Color = RGB(255, 230, 153)

            Call EnsureTripleColumns(dstWS, 12, 13, 14, 1)
            dstWS.Cells(lastDstRow, 12).Value = srcWS.Cells(i, SrcCol_Provize).Value
            dstWS.Cells(lastDstRow, 13).Value = srcWS.Cells(i, SrcCol_Odmena).Value
            If formatChoice = vbYes Then
                dstWS.Cells(lastDstRow, 14).Value = srcWS.Parent.Sheets(srcWS.Name).Range(SrcCol_MesicUhrady).Value
            Else
                dstWS.Cells(lastDstRow, 14).Value = srcWS.Cells(i, SrcCol_MesicUhrady).Value
            End If

            addedCount = addedCount + 1
            addedContracts = addedContracts & contractNumRaw & "|"
        Else
            wasUpdated = False
            If dstWS.Cells(foundRow, 12).Value = "" Then
                dstWS.Cells(foundRow, 12).Value = srcWS.Cells(i, SrcCol_Provize).Value
                wasUpdated = True
            End If
            If dstWS.Cells(foundRow, 13).Value = "" Then
                dstWS.Cells(foundRow, 13).Value = srcWS.Cells(i, SrcCol_Odmena).Value
                wasUpdated = True
            End If
            If dstWS.Cells(foundRow, 14).Value = "" Then
                If formatChoice = vbYes Then
                    dstWS.Cells(foundRow, 14).Value = srcWS.Parent.Sheets(srcWS.Name).Range(SrcCol_MesicUhrady).Value
                Else
                    dstWS.Cells(foundRow, 14).Value = srcWS.Cells(i, SrcCol_MesicUhrady).Value
                End If
                wasUpdated = True
            End If

            trgCols = NextEmptyTripleLMN(dstWS, foundRow)
            If trgCols(0) > 0 Then
                Call EnsureTripleColumns(dstWS, trgCols(0), trgCols(1), trgCols(2), 1)
                If dstWS.Cells(foundRow, trgCols(0)).Value = "" Then
                    dstWS.Cells(foundRow, trgCols(0)).Value = srcWS.Cells(i, SrcCol_Provize).Value
                    wasUpdated = True
                End If
                If dstWS.Cells(foundRow, trgCols(1)).Value = "" Then
                    dstWS.Cells(foundRow, trgCols(1)).Value = srcWS.Cells(i, SrcCol_Odmena).Value
                    wasUpdated = True
                End If
                If dstWS.Cells(foundRow, trgCols(2)).Value = "" Then
                    If formatChoice = vbYes Then
                        dstWS.Cells(foundRow, trgCols(2)).Value = srcWS.Parent.Sheets(srcWS.Name).Range(SrcCol_MesicUhrady).Value
                    Else
                        dstWS.Cells(foundRow, trgCols(2)).Value = srcWS.Cells(i, SrcCol_MesicUhrady).Value
                    End If
                    wasUpdated = True
                End If
            End If

            dstWS.Cells(foundRow, "K").Interior.Color = RGB(255, 230, 153)
            If wasUpdated Then
                updatedCount = updatedCount + 1
                updatedContracts = updatedContracts & contractNumRaw & "|"
                With dstWS.Cells(foundRow, "A").Font
                    .Bold = True
                    .Color = vbBlue
                End With
            Else
                skippedCount = skippedCount + 1
                skippedContracts = skippedContracts & contractNumRaw & " (��dn� voln� m�sto pro z�pis)|"
            End If
        End If
NextIteration:
    Next i

    Application.ScreenUpdating = True

    ' --------- TRANSFER REPORT v c�lov�m souboru ---------
    Dim wsReport As Worksheet
    On Error Resume Next
    Set wsReport = dstWB.Sheets("Transfer report")
    If Not wsReport Is Nothing Then
        Application.DisplayAlerts = False
        wsReport.Delete
        Application.DisplayAlerts = True
    End If
    On Error GoTo 0

    Set wsReport = dstWB.Sheets.Add(After:=dstWB.Sheets(dstWB.Sheets.Count))
    wsReport.Name = "Transfer report"

    Dim arrAdd() As String, arrUpd() As String, arrSkip() As String
    Dim maxLen As Long, j As Long

    If addedContracts <> "" Then arrAdd = Split(addedContracts, "|") Else ReDim arrAdd(0): arrAdd(0) = ""
    If updatedContracts <> "" Then arrUpd = Split(updatedContracts, "|") Else ReDim arrUpd(0): arrUpd(0) = ""
    If skippedContracts <> "" Then arrSkip = Split(skippedContracts, "|") Else ReDim arrSkip(0): arrSkip(0) = ""

    maxLen = UBound(arrAdd)
    If UBound(arrUpd) > maxLen Then maxLen = UBound(arrUpd)
    If UBound(arrSkip) > maxLen Then maxLen = UBound(arrSkip)

    wsReport.Cells(1, 1).Value = "Nov� p�idan� smlouvy"
    wsReport.Cells(1, 2).Value = "Aktualizovan� smlouvy"
    wsReport.Cells(1, 3).Value = "P�esko�en� z�znamy"
    wsReport.Range("A1:C1").Font.Bold = True

    For j = 0 To maxLen
        If j <= UBound(arrAdd) Then wsReport.Cells(j + 2, 1).Value = arrAdd(j)
        If j <= UBound(arrUpd) Then wsReport.Cells(j + 2, 2).Value = arrUpd(j)
        If j <= UBound(arrSkip) Then wsReport.Cells(j + 2, 3).Value = arrSkip(j)
    Next j

    wsReport.Columns("A:C").AutoFit

    Call FormatFinalTableLMN(dstWS)

    MsgBox "Synchronizace dokon�ena!" & vbCrLf & "Report najde� na listu 'Transfer report'.", vbInformation, "V�sledky synchronizace"
End Sub




