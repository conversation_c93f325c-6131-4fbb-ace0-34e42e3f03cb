//+------------------------------------------------------------------+
//|                                               MaxProfitTracker.mq4 |
//|  <PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON> s MagicNumber přes editbox + formát datumu     |
//+------------------------------------------------------------------+
#property strict

#define MP_MAX_PROFIT     0
#define MP_TICKET         1
#define MP_TIME           2
#define MP_OPEN_PRICE     3
#define MP_TYPE           4
#define MP_STATUS         5
#define MP_CLOSE_PRICE    6
#define MP_CLOSED_PL      7

double MaxProfit[5][8];
bool TicketAlreadySaved[5];
datetime EAStartTime;
datetime LastCheckedTime = 0;

string FileName = "MaxProfit.txt";
int MagicNumber = 777;
string EditName = "MagicNumberEdit";

// Funkce pro formátování data jako DD.MM.RRRR HH:MM
string GetFormattedDate(datetime dt)
{
    MqlDateTime t;
    TimeToStruct(dt, t);
    return StringFormat("%02d.%02d.%04d %02d:%02d", t.day, t.mon, t.year, t.hour, t.min);
}

//+------------------------------------------------------------------+
//| Inicializace EA                                                  |
//+------------------------------------------------------------------+
int OnInit()
{
    EAStartTime = TimeCurrent();
    LastCheckedTime = Time[0];

    for(int i = 0; i < 5; i++) {
        for(int j = 0; j < 8; j++)
            MaxProfit[i][j] = 0.0;
        TicketAlreadySaved[i] = false;
    }

    CreateMagicNumberEdit();
    Comment("MaxProfitTracker EA inicializován.");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Vytvoření editboxu pro MagicNumber                               |
//+------------------------------------------------------------------+
void CreateMagicNumberEdit()
{
    if(ObjectFind(0, EditName) != 0)
    {
        ObjectCreate(0, EditName, OBJ_EDIT, 0, 0, 0);
        ObjectSetInteger(0, EditName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, EditName, OBJPROP_XDISTANCE, 10);
        ObjectSetInteger(0, EditName, OBJPROP_YDISTANCE, 10);
        ObjectSetInteger(0, EditName, OBJPROP_FONTSIZE, 10);
        ObjectSetInteger(0, EditName, OBJPROP_XSIZE, 100);
        ObjectSetInteger(0, EditName, OBJPROP_YSIZE, 20);
        ObjectSetString(0, EditName, OBJPROP_TEXT, IntegerToString(MagicNumber));
    }
}

//+------------------------------------------------------------------+
//| Funkce: Získání zavírací ceny z historie                         |
//+------------------------------------------------------------------+
double OrderClosePriceByTicket(int ticket)
{
    for(int i = OrdersHistoryTotal()-1; i >= 0; i--) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_HISTORY)) {
            if(OrderTicket() == ticket)
                return OrderClosePrice();
        }
    }
    return 0.0;
}

//+------------------------------------------------------------------+
//| Funkce: Zápis obchodu do souboru                                 |
//+------------------------------------------------------------------+
void WriteTradeToFile(int index)
{
    int handle = FileOpen(FileName, FILE_WRITE|FILE_TXT|FILE_READ|FILE_ANSI|FILE_SHARE_READ);
    if (handle != INVALID_HANDLE)
    {
        FileSeek(handle, 0, SEEK_END);
        string typeStr = (MaxProfit[index][MP_TYPE] == OP_BUY) ? "BUY" : "SELL";
        string statusStr = (MaxProfit[index][MP_STATUS] == 0.0) ? "OPEN" : "CLOSED";

        string line;
        if (MaxProfit[index][MP_STATUS] == 0.0) {
            line = StringFormat("#%.0f - %s - %s - %.*f - MaxProfit: %.1f bodů - %s\n",
                MaxProfit[index][MP_TICKET],
                GetFormattedDate((datetime)MaxProfit[index][MP_TIME]),
                typeStr, Digits, MaxProfit[index][MP_OPEN_PRICE],
                MaxProfit[index][MP_MAX_PROFIT],
                statusStr);
        } else {
            line = StringFormat("#%.0f - %s - %s - %.*f - MaxProfit: %.1f bodů - %s - Close: %.*f - PL: %.1f bodů\n",
                MaxProfit[index][MP_TICKET],
                GetFormattedDate((datetime)MaxProfit[index][MP_TIME]),
                typeStr, Digits, MaxProfit[index][MP_OPEN_PRICE],
                MaxProfit[index][MP_MAX_PROFIT],
                statusStr, Digits, MaxProfit[index][MP_CLOSE_PRICE],
                MaxProfit[index][MP_CLOSED_PL]);
        }

        FileWriteString(handle, line);
        FileClose(handle);
    }
}

//+------------------------------------------------------------------+
//| Funkce: Aktualizace MagicNumber z editboxu                       |
//+------------------------------------------------------------------+
void UpdateMagicNumberFromEdit()
{
    if (ObjectFind(0, EditName) == 0) {
        string inputText = ObjectGetString(0, EditName, OBJPROP_TEXT);
        int newMagic = StringToInteger(inputText);
        if (newMagic != MagicNumber && newMagic > 0) {
            MagicNumber = newMagic;
            Print("MagicNumber změněn na: ", MagicNumber);
        }
    }
}

//+------------------------------------------------------------------+
//| Funkce: Aktualizace obchodů a sledování profitu                  |
//+------------------------------------------------------------------+
void UpdateMaxProfits()
{
    double barHigh = High[1];
    double barLow = Low[1];

    for (int j = 0; j < 5; j++) {
        if (MaxProfit[j][MP_TYPE] != -1.0) {
            bool tradeFound = false;
            for (int i = 0; i < OrdersTotal(); i++) {
                if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
                    if (OrderTicket() == int(MaxProfit[j][MP_TICKET]) && OrderMagicNumber() == MagicNumber) {
                        tradeFound = true;
                        break;
                    }
                }
            }
            if (!tradeFound && MaxProfit[j][MP_STATUS] == 0.0) {
                MaxProfit[j][MP_STATUS] = 1.0;
                MaxProfit[j][MP_CLOSE_PRICE] = OrderClosePriceByTicket(int(MaxProfit[j][MP_TICKET]));

                double diff = (MaxProfit[j][MP_TYPE] == OP_BUY) ? 
                               (MaxProfit[j][MP_CLOSE_PRICE] - MaxProfit[j][MP_OPEN_PRICE]) : 
                               (MaxProfit[j][MP_OPEN_PRICE] - MaxProfit[j][MP_CLOSE_PRICE]);

                double pointsSize = (Point == 0.01 || Point == 0.1) ? 1.0 : 
                                    (Point == 0.001 || Point == 0.0001) ? 10.0 : 1.0;

                MaxProfit[j][MP_CLOSED_PL] = NormalizeDouble(diff / pointsSize, 1);

                WriteTradeToFile(j);
            }
        }
    }

    for(int i = 0; i < OrdersTotal(); i++) {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            if (OrderSymbol() != Symbol() || OrderMagicNumber() != MagicNumber) continue;
            if (OrderOpenTime() < EAStartTime) continue;

            int index = -1;
            for(int j = 0; j < 5; j++) {
                if (MaxProfit[j][MP_TICKET] == (double)OrderTicket()) {
                    index = j;
                    break;
                }
            }

            if (index == -1) {
                for(int j = 0; j < 5; j++) {
                    if (MaxProfit[j][MP_TYPE] == -1.0) {
                        index = j;
                        MaxProfit[j][MP_TICKET] = OrderTicket();
                        MaxProfit[j][MP_TIME] = OrderOpenTime();
                        MaxProfit[j][MP_OPEN_PRICE] = OrderOpenPrice();
                        MaxProfit[j][MP_TYPE] = OrderType();
                        MaxProfit[j][MP_STATUS] = 0.0;
                        TicketAlreadySaved[j] = false;
                        break;
                    }
                }
            }

            if (index != -1) {
                double diff = (MaxProfit[index][MP_TYPE] == OP_BUY) ? 
                              (barHigh - MaxProfit[index][MP_OPEN_PRICE]) : 
                              (MaxProfit[index][MP_OPEN_PRICE] - barLow);

                double pointsSize = (Point == 0.01 || Point == 0.1) ? 1.0 : 
                                    (Point == 0.001 || Point == 0.0001) ? 10.0 : 1.0;

                double profitInUnits = NormalizeDouble(diff / pointsSize, 1);

                if (profitInUnits > MaxProfit[index][MP_MAX_PROFIT]) {
                    MaxProfit[index][MP_MAX_PROFIT] = profitInUnits;
                }

                if (!TicketAlreadySaved[index]) {
                    WriteTradeToFile(index);
                    TicketAlreadySaved[index] = true;
                }
            }
        }
    }

    string output = "";
    bool anyTradeFound = false;
    for(int i = 0; i < 5; i++) {
        if (MaxProfit[i][MP_TYPE] != -1.0) {
            anyTradeFound = true;
            string typeStr = (MaxProfit[i][MP_TYPE] == OP_BUY) ? "BUY" : "SELL";
            string statusStr = (MaxProfit[i][MP_STATUS] == 0.0) ? "OPEN" : 
                StringFormat("CLOSED (%.1f bodů)", MaxProfit[i][MP_CLOSED_PL]);

            output += StringFormat("#%.0f - %s - %s - %.*f - MaxProfit: %.1f bodů - %s\n",
                MaxProfit[i][MP_TICKET],
                GetFormattedDate((datetime)MaxProfit[i][MP_TIME]),
                typeStr,
                Digits, MaxProfit[i][MP_OPEN_PRICE],
                MaxProfit[i][MP_MAX_PROFIT],
                statusStr);
        }
    }

    if (!anyTradeFound)
        Comment("Žádné obchody k zobrazení.");
    else
        Comment(output);
}

//+------------------------------------------------------------------+
//| Hlavní OnTick                                                    |
//+------------------------------------------------------------------+
void OnTick()
{
    if (Time[0] != LastCheckedTime)
    {
        LastCheckedTime = Time[0];
        UpdateMagicNumberFromEdit();
        UpdateMaxProfits();
    }
}
