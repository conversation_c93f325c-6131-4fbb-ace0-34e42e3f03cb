# PowerShell skript pro spuštění VBA modulu na FT provize old.xlsx

try {
    Write-Host "=== SPUŠTĚNÍ VBA MODULU NA FT PROVIZE OLD.XLSX ==="
    Write-Host "Otevírám Excel..."
    
    # Vytvoř Excel aplikaci
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $true  # Zobraz Excel pro sledování procesu
    $excel.DisplayAlerts = $false  # Vypni upozornění
    
    # Otevři soubor FT provize old.xlsx
    $workbookPath = "$PWD\FT provize old.xlsx"
    Write-Host "Otevírám soubor: $workbookPath"
    $workbook = $excel.Workbooks.Open($workbookPath)
    
    # Načti VBA modul
    $modulePath = "$PWD\module2.bas"
    Write-Host "Načítám VBA modul: $modulePath"
    
    # Přidej modul do workbooku
    $vbaProject = $workbook.VBProject
    $vbaModule = $vbaProject.VBComponents.Add(1)  # 1 = vbext_ct_StdModule
    
    # Načti obsah modulu
    $moduleContent = Get-Content $modulePath -Raw
    $vbaModule.CodeModule.AddFromString($moduleContent)
    
    Write-Host "VBA modul načten. Spouštím makro ProcessFTProvizeOld()..."
    
    # Spusť makro
    $excel.Run("ProcessFTProvizeOld")
    
    Write-Host "Makro dokončeno!"
    Write-Host "Excel zůstává otevřený pro kontrolu výsledků."
    Write-Host "Pro ukončení stiskněte Enter..."
    Read-Host
    
    # Zavři Excel
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
    Write-Host "Hotovo!"
    
} catch {
    Write-Host "Chyba: $($_.Exception.Message)"
    if ($excel) {
        $excel.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    }
}
