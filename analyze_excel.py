import pandas as pd
import sys
import os

try:
    # Cesta k souboru v aktuálním adresáři
    file_path = 'FT provize old.xlsx'
    
    print(f"Hledám soubor: {file_path}")
    print(f"Aktuální adresář: {os.getcwd()}")
    print(f"Soubory v adresáři: {os.listdir('.')}")
    
    # Zkontroluj, zda soubor existuje
    if not os.path.exists(file_path):
        print(f'Soubor neexistuje: {file_path}')
        sys.exit(1)
    
    print("Načítám Excel soubor...")
    # Načti Excel soubor
    xl_file = pd.ExcelFile(file_path)
    print('=== ANALÝZA SOUBORU: FT provize old.xlsx ===')
    print(f'Počet listů: {len(xl_file.sheet_names)}')
    print(f'Názvy listů: {xl_file.sheet_names}')
    print()
    
    # Analyzuj první list
    if xl_file.sheet_names:
        first_sheet = xl_file.sheet_names[0]
        print(f"Načítám list: {first_sheet}")
        df = pd.read_excel(file_path, sheet_name=first_sheet)
        
        print(f'=== ANALÝZA LISTU: {first_sheet} ===')
        print(f'Rozměry: {df.shape[0]} řádků x {df.shape[1]} sloupců')
        print()
        
        print('Názvy sloupců:')
        for i, col in enumerate(df.columns):
            col_letter = chr(65 + i)  # A=65, B=66, atd.
            print(f'  {col_letter} ({i+1}): {col}')
        print()
        
        print('Prvních 3 řádků:')
        print(df.head(3).to_string())
        print()
        
        # Analyzuj klíčové sloupce A, J, K, L, M
        key_columns = [0, 9, 10, 11, 12]  # A=0, J=9, K=10, L=11, M=12
        print('=== ANALÝZA KLÍČOVÝCH SLOUPCŮ ===')
        
        for col_idx in key_columns:
            if col_idx < len(df.columns):
                col_letter = chr(65 + col_idx)
                col_name = df.columns[col_idx]
                print(f'Sloupec {col_letter}: {col_name}')
                print(f'  Typ dat: {df.iloc[:, col_idx].dtype}')
                print(f'  Počet neprázdných: {df.iloc[:, col_idx].notna().sum()}')
                
                # Ukázka hodnot
                sample_values = df.iloc[:, col_idx].dropna().head(3).tolist()
                print(f'  Ukázka hodnot: {sample_values}')
                
                # Pro sloupce L a M (možná období) zobraz unikátní hodnoty
                if col_letter in ['L', 'M']:
                    unique_vals = df.iloc[:, col_idx].dropna().unique()
                    print(f'  Unikátní hodnoty ({len(unique_vals)}): {list(unique_vals[:10])}')
                print()

except Exception as e:
    print(f'Chyba při analýze: {e}')
    import traceback
    traceback.print_exc()
