# Rekapitulace všech podmínek pro vstup do obchodu

Pro vstup do obchodu musí být splněny **všechny** následující podmínky:

## 1. <PERSON><PERSON><PERSON><PERSON><PERSON> (společné pro BUY i SELL)
- **ZZ vzdálenost** musí být větší než vybraný ATR limit (zelený, oranžový nebo červený podle nastavení MinZZDistForTrade)
- **Nejbližš<PERSON> hodnota** musí být v rozsahu pro obchodování (MathAbs(LastNearestDistance) <= HighlightDistance)
- **Obchodní hodiny** - aktuální čas musí být v povoleném rozmezí obchodních hodin (pokud je UseTradeHours=true)
- **BandCheck** - pokud je BandCheck=true, musí být splněna podmínka pro obálky (viz níže)

## 2. Podmínka BandCheck (pokud je aktivní)
- Pro **BUY**: <PERSON>na mus<PERSON> být pod spodní obálk<PERSON> (lastClose < LowerEnvelope)
- Pro **SELL**: Cena musí být nad horní obálkou (lastClose > UpperEnvelope)

## 3. Specifické podmínky pro typ obchodu

### Pro BUY obchody:
- Základní podmínka: lastClose < bestValue (potenciální BUY)
- **Market BUY**: Pokud je aktuální cena Ask pod MA (currentAsk < bestValue)
  - A zároveň nemáme aktivní BUY pozici (!PositionExists(OP_BUY))
- **Buy Limit**: Pokud je aktuální cena Ask nad nebo na úrovni MA (currentAsk >= bestValue)
  - A zároveň nemáme aktivní Buy Limit pokyn nebo se cena výrazně změnila (!IsPendingOrderValid(pendingBuyTicket) || MathAbs(pendingBuyPrice - bestValue) > Point * 10)

### Pro SELL obchody:
- Základní podmínka: lastClose > bestValue (potenciální SELL)
- **Market SELL**: Pokud je aktuální cena Bid nad MA (currentBid > bestValue)
  - A zároveň nemáme aktivní SELL pozici (!PositionExists(OP_SELL))
- **Sell Limit**: Pokud je aktuální cena Bid pod nebo na úrovni MA (currentBid <= bestValue)
  - A zároveň nemáme aktivní Sell Limit pokyn nebo se cena výrazně změnila (!IsPendingOrderValid(pendingSellTicket) || MathAbs(pendingSellPrice - bestValue) > Point * 10)

## 4. Parametry obchodu
- **Stop Loss**: ATR14_D1 / 10
- **Take Profit**: 2 × Stop Loss

## 5. Zrušení čekajících pokynů
Čekající pokyny jsou zrušeny, pokud:
- Není splněna některá z výše uvedených podmínek
- Jsme mimo obchodní hodiny
- Hodnota není v obchodním rozsahu
- ZZ vzdálenost není dostatečná
- Podmínka BandCheck není splněna
