
//+------------------------------------------------------------------+
//| Expert Advisor: Trend Test + Visual                              |
//+------------------------------------------------------------------+
#property strict

bool TrendWithReversal;
bool IsBull;
double TrendLength;
double WOEntryPriceBack;

int TrendStartBar = 0;
int TrendEndBar = 0;
int TrendTestedUntilBar = 0;

void IdentifyTrend(
   bool IsSharpTrend,
   bool IsSameColour,
   bool &TrendWithReversal,
   bool &IsBull,
   double &TrendLength,
   double &WOEntryPriceBack,
   int MaxBarsToCheck);

int OnInit()
  {
   Print("TrendTest Visual EA inicializován.");
   return(INIT_SUCCEEDED);
  }

void OnTick()
  {
   // Volání analýzy trendu
   IdentifyTrend(true, true, TrendWithReversal, IsBull, TrendLength, WOEntryPriceBack, 20);

   // Vymazat staré objekty
   ObjectsDeleteAll(0, OBJ_TREND);
   ObjectsDeleteAll(0, OBJ_ARROW);
   ObjectsDeleteAll(0, OBJ_HLINE);

   // Vykreslit trendovou čáru
   string trendName = "TrendLine";
   datetime startTime = Time[TrendStartBar];
   datetime endTime = Time[TrendEndBar];
   double startPrice = IsBull ? High[TrendStartBar] : Low[TrendStartBar];
   double endPrice = IsBull ? High[TrendEndBar] : Low[TrendEndBar];

   ObjectCreate(0, trendName, OBJ_TREND, 0, startTime, startPrice, endTime, endPrice);
   ObjectSetInteger(0, trendName, OBJPROP_COLOR, clrBlue);
   ObjectSetInteger(0, trendName, OBJPROP_WIDTH, 2);

   // Šipka na reverzní svíčku
   if (TrendWithReversal)
   {
      string arrowName = "ReversalArrow";
      double arrowPrice = IsBull ? Low[1] - 10 * Point : High[1] + 10 * Point;
      ObjectCreate(0, arrowName, OBJ_ARROW, 0, Time[1], arrowPrice);
      ObjectSetInteger(0, arrowName, OBJPROP_COLOR, clrRed);
      ObjectSetInteger(0, arrowName, OBJPROP_ARROWCODE, SYMBOL_ARROWUP);
      if (!IsBull)
         ObjectSetInteger(0, arrowName, OBJPROP_ARROWCODE, SYMBOL_ARROWDOWN);
   }

   // Horizontální linie na WOEntryPriceBack
   string lineName = "WOEntryPriceLine";
   ObjectCreate(0, lineName, OBJ_HLINE, 0, 0, WOEntryPriceBack);
   ObjectSetInteger(0, lineName, OBJPROP_COLOR, clrOrange);
   ObjectSetInteger(0, lineName, OBJPROP_WIDTH, 1);

   // Výpis požadované hlášky
   string trendDirection = IsBull ? "stoupající" : "klesající";
   string reversalStatus = TrendWithReversal ? "zakončen reverzní svíčkou" : "nezakončen reverzní svíčkou";

   string info = StringFormat("Hlavní trend byl testován mezi svíčkami (2-%d). Nalezen trend %s, %s.",
                              TrendTestedUntilBar, trendDirection, reversalStatus);

   Comment(info);
   Print(info);
  }

void IdentifyTrend(
   bool IsSharpTrend,
   bool IsSameColour,
   bool &TrendWithReversal,
   bool &IsBull,
   double &TrendLength,
   double &WOEntryPriceBack,
   int MaxBarsToCheck)
{
   int trendStartBar = 2;
   IsBull = false;
   TrendLength = 0;
   WOEntryPriceBack = 0;
   TrendWithReversal = false;
   TrendStartBar = -1;
   TrendEndBar = -1;

   int firstBar = -1;
   int lastBar = -1;
   bool sameColourValid = true;
   bool sharpTrendValid = true;

   int bar = trendStartBar;

   while (bar <= MaxBarsToCheck)
   {
      double prevClose = Close[bar + 1];
      double currClose = Close[bar];

      if (firstBar == -1)
      {
         if (currClose > prevClose)
            IsBull = true;
         else if (currClose < prevClose)
            IsBull = false;
         else
         {
            bar++;
            continue;
         }
         firstBar = bar + 1;
         lastBar = bar;
      }
      else
      {
         if (IsSharpTrend)
         {
            if (IsBull)
            {
               if (!(High[bar] > High[bar + 1] && Low[bar] > Low[bar + 1]))
               {
                  sharpTrendValid = false;
                  break;
               }
            }
            else
            {
               if (!(High[bar] < High[bar + 1] && Low[bar] < Low[bar + 1]))
               {
                  sharpTrendValid = false;
                  break;
               }
            }
         }

         if (IsSameColour)
         {
            bool prevBull = Close[bar + 1] > Open[bar + 1];
            bool currBull = Close[bar] > Open[bar];
            if (prevBull != currBull)
            {
               sameColourValid = false;
               break;
            }
         }

         lastBar = bar;
      }
      bar++;
   }

   if (firstBar == -1 || !sharpTrendValid || !sameColourValid)
   {
      TrendWithReversal = false;
      IsBull = false;
      TrendLength = 0;
      WOEntryPriceBack = 0;
      TrendTestedUntilBar = bar - 1;
      return;
   }

   TrendStartBar = firstBar;
   TrendEndBar = lastBar;

   double trendHigh = High[firstBar];
   double trendLow = Low[firstBar];
   double lastTrendHigh = High[lastBar];
   double lastTrendLow = Low[lastBar];

   TrendLength = MathAbs(trendHigh - lastTrendLow);

   bool bar1Bull = Close[1] > Open[1];
   if (IsBull && !bar1Bull && High[1] < High[2] && Low[1] < Low[2])
   {
      TrendWithReversal = true;
   }
   else if (!IsBull && bar1Bull && High[1] > High[2] && Low[1] > Low[2])
   {
      TrendWithReversal = true;
   }
   else
   {
      TrendWithReversal = false;
   }

   double extremumValue;
   if (IsBull)
   {
      extremumValue = Low[lastBar];
      for (int i = lastBar; i <= firstBar; i++)
      {
         if (Low[i] < extremumValue)
            extremumValue = Low[i];
      }
   }
   else
   {
      extremumValue = High[lastBar];
      for (int i = lastBar; i <= firstBar; i++)
      {
         if (High[i] > extremumValue)
            extremumValue = High[i];
      }
   }

   double entryCandleHigh = 0;
   double entryCandleLow = 0;

   for (int i = lastBar; i <= firstBar; i++)
   {
      if (IsBull && Low[i] == extremumValue)
      {
         entryCandleHigh = High[i];
         entryCandleLow = Low[i];
         break;
      }
      else if (!IsBull && High[i] == extremumValue)
      {
         entryCandleHigh = High[i];
         entryCandleLow = Low[i];
         break;
      }
   }

   WOEntryPriceBack = entryCandleHigh - (entryCandleHigh-entryCandleLow) / 2.0;
   TrendTestedUntilBar = bar - 1;
}
