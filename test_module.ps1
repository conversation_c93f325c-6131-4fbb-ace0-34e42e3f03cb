# Jednodu<PERSON><PERSON><PERSON> test VBA modulu

try {
    Write-Host "=== TEST VBA MODULU ==="
    
    # Otevři Excel
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $true
    $excel.DisplayAlerts = $false
    
    # Otevři soubor
    $workbookPath = "$PWD\FT provize old.xlsx"
    Write-Host "Otevírám: $workbookPath"
    
    if (Test-Path $workbookPath) {
        $workbook = $excel.Workbooks.Open($workbookPath)
        Write-Host "Soubor otevřen úspěšně!"
        Write-Host "Listy v souboru:"
        foreach ($sheet in $workbook.Worksheets) {
            Write-Host "  - $($sheet.Name)"
        }
        
        # Zkontroluj první list
        $firstSheet = $workbook.Worksheets.Item(1)
        Write-Host "První list: $($firstSheet.Name)"
        Write-Host "Počet řádků s daty: $($firstSheet.UsedRange.Rows.Count)"
        Write-Host "Počet sloupců: $($firstSheet.UsedRange.Columns.Count)"
        
        # Zkontroluj sloupec L (období)
        Write-Host "Sloupec L (období) - ukázka hodnot:"
        for ($i = 2; $i -le 5; $i++) {
            $value = $firstSheet.Cells.Item($i, 12).Text
            Write-Host "  Řádek $i : $value"
        }
        
        Write-Host "Excel je otevřený. Můžete nyní ručně spustit makro nebo importovat modul."
        Write-Host "Pro pokračování stiskněte Enter..."
        Read-Host
        
        $workbook.Close($false)
    } else {
        Write-Host "Soubor nenalezen: $workbookPath"
    }
    
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
} catch {
    Write-Host "Chyba: $($_.Exception.Message)"
    if ($excel) {
        $excel.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    }
}
