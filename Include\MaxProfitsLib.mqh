//+------------------------------------------------------------------+
//|                                                 MaxProfitsLib.mqh |
//|                      Copyright 2024, Custom Scripts              |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Custom Scripts"
#property link      ""
#property strict

// Definice indexů pro pole MaxProfit
#define MP_MAX_PROFIT  0  // Maximální profit
#define MP_TICKET      1  // <PERSON><PERSON><PERSON> obchodu (ticket)
#define MP_TIME        2  // Čas otevření obchodu (jako timestamp)
#define MP_OPEN_PRICE  3  // Otevírací cena obchodu
#define MP_TYPE        4  // Typ obchodu (0=BUY, 1=SELL)

// Pou<PERSON><PERSON><PERSON><PERSON> přímo vestavěný enum ENUM_BASE_CORNER pro rohy
// ENUM_BASE_CORNER:
//   CORNER_LEFT_UPPER = 0    - Levý horní roh
//   CORNER_RIGHT_UPPER = 1   - Pravý horní roh
//   CORNER_LEFT_LOWER = 2    - Levý dolní roh
//   CORNER_RIGHT_LOWER = 3   - Pravý dolní roh

// Třída pro sledování maximálních profitů
class CMaxProfits {
private:
    string   m_prefix;              // Prefix pro objekty na grafu
    int      m_magicNo;             // Magické číslo pro sledování obchodů
    int      m_maxOrders;           // Maximální počet obchodů k sledování
    bool     m_fileExport;          // Povolit export do souboru
    string   m_fileName;            // Název souboru pro export
    bool     m_debugMode;           // Zapnout debug výpisy
    color    m_textColor;           // Barva textu pro zobrazení profitů
    int      m_fontSize;            // Velikost písma pro zobrazení profitů
    string   m_fontName;            // Název písma pro zobrazení profitů
    int      m_displayCorner;       // Roh pro zobrazení textových objektů
    int      m_xOffset;             // X-ový offset pro textové objekty
    int      m_yOffset;             // Y-ový offset pro textové objekty

    double   m_maxProfit[][5];      // 2D pole pro ukládání informací o obchodech
    double   m_lastSavedProfit[];   // Pole pro ukládání posledních zapsaných hodnot MaxProfit
    int      m_sessionOrderCount;   // Počet obchodů v aktuální session
    datetime m_startTime;           // Čas spuštění

public:
    // Konstruktor
    CMaxProfits(string prefix="MaxProfits", int magicNo=777, int maxOrders=5,
                bool fileExport=true, string fileName="MaxProfits.txt",
                bool debugMode=false, color textColor=clrWhite,
                int fontSize=10, string fontName="Arial",
                ENUM_BASE_CORNER displayCorner=CORNER_LEFT_UPPER,
                int xOffset=10, int yOffset=20) {
        m_prefix = prefix;
        m_magicNo = magicNo;
        m_maxOrders = maxOrders;
        m_fileExport = fileExport;
        m_fileName = fileName;
        m_debugMode = debugMode;
        m_textColor = textColor;
        m_fontSize = fontSize;
        m_fontName = fontName;
        m_displayCorner = displayCorner;
        m_xOffset = xOffset;
        m_yOffset = yOffset;
        m_sessionOrderCount = 0;

        // Inicializace polí
        ArrayResize(m_maxProfit, m_maxOrders);
        ArrayResize(m_lastSavedProfit, m_maxOrders);

        // Inicializace pole MaxProfit
        for(int i = 0; i < m_maxOrders; i++) {
            m_maxProfit[i][MP_MAX_PROFIT] = 0.0;  // Maximální profit
            m_maxProfit[i][MP_TICKET] = 0.0;      // Číslo obchodu (ticket)
            m_maxProfit[i][MP_TIME] = 0.0;        // Čas otevření obchodu (jako timestamp)
            m_maxProfit[i][MP_OPEN_PRICE] = 0.0;  // Otevírací cena obchodu
            m_maxProfit[i][MP_TYPE] = -1.0;       // Typ obchodu (-1=neinicializovaný, 0=BUY, 1=SELL)

            // Inicializace pole LastSavedProfit
            m_lastSavedProfit[i] = 0.0;
        }

        // Nastavení času spuštění
        m_startTime = TimeCurrent();

        if(m_debugMode) {
            Print("MaxProfits inicializován v čase: ", TimeToString(m_startTime));
        }
    }

    // Destruktor
    ~CMaxProfits() {
        // Odstranění všech objektů s prefixem
        RemoveAllObjects();
    }

    // Metoda pro odstranění všech objektů
    void RemoveAllObjects() {
        for(int i = ObjectsTotal() - 1; i >= 0; i--) {
            string name = ObjectName(0, i);
            // Odstraníme objekty s prefixem nebo objekty s "_MagicNumberEdit" v názvu
            if(StringFind(name, m_prefix) == 0 || StringFind(name, "_MagicNumberEdit") >= 0 || StringFind(name, "_MagicNumberLabel") >= 0) {
                ObjectDelete(0, name);
            }
        }

        // Explicitně odstraníme všechny objekty MaxProfit
        for(int i = 0; i < m_maxOrders; i++) {
            string objName = m_prefix + "_MaxProfit_" + IntegerToString(i);
            if(ObjectFind(0, objName) >= 0) {
                ObjectDelete(0, objName);
            }
        }
    }

    // Metoda pro vytvoření editboxu pro MagicNumber
    void CreateMagicNumberEdit() {
        string editName = m_prefix + "_MagicNumberEdit";

        // Nejprve odstraníme všechny existující editboxy s podobnými názvy
        for(int i = ObjectsTotal() - 1; i >= 0; i--) {
            string name = ObjectName(0, i);
            if(StringFind(name, "_MagicNumberEdit") >= 0) {
                ObjectDelete(0, name);
            }
        }

        // Vytvoříme nový editbox
        ObjectCreate(0, editName, OBJ_EDIT, 0, 0, 0);

        // Nastavení pozice podle rohu
        ObjectSetInteger(0, editName, OBJPROP_CORNER, m_displayCorner);

        // Nastavení pozice podle rohu
        int xPos = 20; // Menší odsazení pro levé rohy (20 pixelů)
        int yPos = m_yOffset + 50; // Posuneme níže pod texty s MaxProfity

        // Pro pravé rohy použijeme mnohem větší odsazení
        if(m_displayCorner == CORNER_RIGHT_UPPER || m_displayCorner == CORNER_RIGHT_LOWER) {
            xPos = 500; // Mnohem větší odsazení pro pravé rohy

            // Získáme šířku grafu pro lepší umístění
            int chartWidth = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
            if(chartWidth > 0) {
                // Umístíme editbox 150 pixelů od pravého okraje
                xPos = 150;
            }
        }

        ObjectSetInteger(0, editName, OBJPROP_XDISTANCE, xPos);
        ObjectSetInteger(0, editName, OBJPROP_YDISTANCE, yPos);
        ObjectSetInteger(0, editName, OBJPROP_FONTSIZE, m_fontSize);
        ObjectSetInteger(0, editName, OBJPROP_XSIZE, 100);
        ObjectSetInteger(0, editName, OBJPROP_YSIZE, 20);
        ObjectSetString(0, editName, OBJPROP_TEXT, IntegerToString(m_magicNo));
        ObjectSetInteger(0, editName, OBJPROP_COLOR, m_textColor);
        ObjectSetInteger(0, editName, OBJPROP_BGCOLOR, clrNavy);
        ObjectSetInteger(0, editName, OBJPROP_BORDER_COLOR, clrWhite);

        // Přidáme popisek nad editbox
        string labelName = m_prefix + "_MagicNumberLabel";
        ObjectCreate(0, labelName, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(0, labelName, OBJPROP_CORNER, m_displayCorner);
        ObjectSetInteger(0, labelName, OBJPROP_XDISTANCE, xPos);
        ObjectSetInteger(0, labelName, OBJPROP_YDISTANCE, yPos - 20);
        ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, m_fontSize);
        ObjectSetInteger(0, labelName, OBJPROP_COLOR, m_textColor);
        ObjectSetString(0, labelName, OBJPROP_TEXT, "Magic Number:");
    }

    // Metoda pro aktualizaci MagicNumber z editboxu
    void UpdateMagicNumberFromEdit() {
        string editName = m_prefix + "_MagicNumberEdit";

        if(ObjectFind(0, editName) >= 0) {
            string inputText = ObjectGetString(0, editName, OBJPROP_TEXT);
            int newMagic = StringToInteger(inputText);

            if(newMagic != m_magicNo && newMagic > 0) {
                m_magicNo = newMagic;

                if(m_debugMode) {
                    Print("MagicNumber změněn na: ", m_magicNo);
                }

                // Resetujeme sledované obchody při změně Magic Number
                m_sessionOrderCount = 0;

                for(int i = 0; i < m_maxOrders; i++) {
                    m_maxProfit[i][MP_MAX_PROFIT] = 0.0;
                    m_maxProfit[i][MP_TICKET] = 0.0;
                    m_maxProfit[i][MP_TIME] = 0.0;
                    m_maxProfit[i][MP_OPEN_PRICE] = 0.0;
                    m_maxProfit[i][MP_TYPE] = -1.0;
                    m_lastSavedProfit[i] = 0.0;
                }
            }
        }
    }

    // Metoda pro aktualizaci maximálních profitů v reálném čase
    void UpdateRealTime() {
        // Nejprve aktualizujeme seznam obchodů (přidáme nové obchody)
        TrackSessionOrders();

        // Procházíme všechny obchody v poli MaxProfit
        for(int i = 0; i < m_sessionOrderCount; i++) {
            int ticket = (int)m_maxProfit[i][MP_TICKET];
            double openPrice = m_maxProfit[i][MP_OPEN_PRICE];
            int orderType = (int)m_maxProfit[i][MP_TYPE];

            // Pokud nemáme platný ticket nebo otevírací cenu, přeskočíme
            if(ticket <= 0 || openPrice <= 0.0) continue;

            // Kontrola, zda je obchod stále otevřený
            bool isOrderOpen = false;

            // Nejprve zkusíme najít obchod v otevřených pozicích
            if(OrderSelect(ticket, SELECT_BY_TICKET)) {
                if(OrderCloseTime() == 0) { // Obchod je stále otevřený
                    isOrderOpen = true;
                }
            }

            // Pokud obchod není otevřený, přeskočíme ho
            if(!isOrderOpen) {
                if(m_debugMode) {
                    Print("Obchod #", ticket, " je již uzavřený, přeskakuji aktualizaci MaxProfit");
                }
                continue;
            }

            // Získáme aktuální cenu
            double currentBid = MarketInfo(Symbol(), MODE_BID);
            double currentAsk = MarketInfo(Symbol(), MODE_ASK);

            // Vypočítáme aktuální profit v bodech
            double currentProfit = 0.0;

            if(orderType == OP_BUY) {
                // Pro BUY obchod je aktuální profit rozdíl mezi Bid a otevírací cenou
                currentProfit = (currentBid - openPrice) / Point;
            } else if(orderType == OP_SELL) {
                // Pro SELL obchod je aktuální profit rozdíl mezi otevírací cenou a Ask
                currentProfit = (openPrice - currentAsk) / Point;
            }

            // Aktualizujeme maximální profit, pokud je aktuální profit větší
            if(currentProfit > m_maxProfit[i][MP_MAX_PROFIT]) {
                m_maxProfit[i][MP_MAX_PROFIT] = currentProfit;

                if(m_debugMode) {
                    string typeStr = (orderType == OP_BUY) ? "BUY" : "SELL";
                    Print("Aktualizován maximální profit v reálném čase pro ticket #", ticket,
                          ": ", m_maxProfit[i][MP_MAX_PROFIT],
                          " bodů (Bid=", currentBid, ", Ask=", currentAsk, ", OpenPrice=", openPrice, ", Type=", typeStr, ")");
                }
            }
        }
    }

    // Metoda pro aktualizaci maximálních profitů na základě High/Low svíčky
    void UpdateOnNewCandle() {
        // Získáme High a Low poslední svíčky
        double high = High[1]; // Hodnota High předchozí svíčky
        double low = Low[1];   // Hodnota Low předchozí svíčky

        // Procházíme všechny obchody v poli MaxProfit
        for(int i = 0; i < m_sessionOrderCount; i++) {
            int ticket = (int)m_maxProfit[i][MP_TICKET];
            double openPrice = m_maxProfit[i][MP_OPEN_PRICE];
            int orderType = (int)m_maxProfit[i][MP_TYPE];

            // Pokud nemáme platný ticket nebo otevírací cenu, přeskočíme
            if(ticket <= 0 || openPrice <= 0.0) continue;

            // Kontrola, zda je obchod stále otevřený
            bool isOrderOpen = false;

            // Nejprve zkusíme najít obchod v otevřených pozicích
            if(OrderSelect(ticket, SELECT_BY_TICKET)) {
                if(OrderCloseTime() == 0) { // Obchod je stále otevřený
                    isOrderOpen = true;
                }
            }

            // Pokud obchod není otevřený, přeskočíme ho
            if(!isOrderOpen) {
                if(m_debugMode) {
                    Print("Obchod #", ticket, " je již uzavřený, přeskakuji aktualizaci MaxProfit na základě High/Low svíčky");
                }
                continue;
            }

            // Použití pomocné funkce pro výpočet potenciálního profitu
            double potentialProfit = CalculateOrderProfit(openPrice, orderType, high, low);

            // Aktualizujeme maximální profit, pokud je potenciální profit větší
            if(potentialProfit > m_maxProfit[i][MP_MAX_PROFIT]) {
                m_maxProfit[i][MP_MAX_PROFIT] = potentialProfit;

                if(m_debugMode) {
                    string typeStr = (orderType == OP_BUY) ? "BUY" : "SELL";
                    Print("Aktualizován maximální profit pro ticket #", ticket,
                          " na základě High/Low svíčky: ", m_maxProfit[i][MP_MAX_PROFIT],
                          " bodů (High=", high, ", Low=", low, ", OpenPrice=", openPrice, ", Type=", typeStr, ")");
                }
            }
        }

        // Zápis MaxProfitů do souboru po aktualizaci
        if(m_fileExport) {
            WriteToFile();
        }
    }

    // Metoda pro zápis maximálních profitů do souboru
    void WriteToFile() {
        // Kontrola, zda máme nějaké nové nebo změněné záznamy
        bool hasNewOrChangedRecords = false;

        for(int i = 0; i < m_sessionOrderCount; i++) {
            int ticket = (int)m_maxProfit[i][MP_TICKET];
            double profit = m_maxProfit[i][MP_MAX_PROFIT];

            // Kontrola, zda je záznam nový nebo změněný
            if(ticket > 0 && profit > 0.0 && profit != m_lastSavedProfit[i]) {
                hasNewOrChangedRecords = true;
                break;
            }
        }

        // Pokud nemáme žádné nové nebo změněné záznamy, ukončíme funkci
        if(!hasNewOrChangedRecords) {
            return;
        }

        // Otevření souboru pro přidání textu (FILE_WRITE = přepsat, FILE_READ|FILE_WRITE = přidat)
        int fileHandle = FileOpen(m_fileName, FILE_READ|FILE_WRITE|FILE_TXT);

        // Kontrola, zda se soubor podařilo otevřít
        if(fileHandle != INVALID_HANDLE) {
            // Přesun na konec souboru
            FileSeek(fileHandle, 0, SEEK_END);

            // Aktuální datum a čas pro záznam
            string currentDateTime = TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES);

            // Zápis hlavičky s aktuálním datem a časem
            FileWrite(fileHandle, "--- Záznam MaxProfitů z " + currentDateTime + " ---");

            // Procházení všech obchodů v poli MaxProfit
            for(int i = 0; i < m_sessionOrderCount; i++) {
                int ticket = (int)m_maxProfit[i][MP_TICKET];
                double profit = m_maxProfit[i][MP_MAX_PROFIT];

                // Zapisujeme pouze obchody, které mají platný ticket, nenulový profit a jsou nové nebo změněné
                if(ticket > 0 && profit > 0.0 && profit != m_lastSavedProfit[i]) {
                    // Formátování profitu v bodech
                    string profitText = DoubleToString(profit, 1);

                    // Formátování data a času ve formátu DD.MM.RRRR HH:MM
                    datetime orderTime = (datetime)m_maxProfit[i][MP_TIME];
                    string dateStr = StringFormat("%02d.%02d.%04d %02d:%02d",
                                                 TimeDay(orderTime),
                                                 TimeMonth(orderTime),
                                                 TimeYear(orderTime),
                                                 TimeHour(orderTime),
                                                 TimeMinute(orderTime));

                    // Získání otevírací ceny a typu obchodu
                    double openPrice = m_maxProfit[i][MP_OPEN_PRICE];
                    int orderType = (int)m_maxProfit[i][MP_TYPE];
                    string typeStr = (orderType == OP_BUY) ? "BUY" : "SELL";

                    // Formátování řádku ve stejném formátu jako v komentářích
                    string line = StringFormat("#%d - %s - %s - %s - MaxProfit: %s bodů",
                                              ticket,
                                              dateStr,
                                              typeStr,
                                              DoubleToString(openPrice, Digits), // Formátování otevírací ceny na správný počet desetinných míst
                                              profitText);

                    // Zápis řádku do souboru
                    FileWrite(fileHandle, line);

                    // Aktualizace posledního zapsaného profitu
                    m_lastSavedProfit[i] = profit;
                }
            }

            // Přidání prázdného řádku pro oddělení záznamů
            FileWrite(fileHandle, "");

            // Zavření souboru
            FileClose(fileHandle);

            if(m_debugMode) {
                Print("Nové MaxProfity byly zapsány do souboru ", m_fileName);
            }
        } else {
            Print("Chyba při otevírání souboru ", m_fileName, ": ", GetLastError());
        }
    }

    // Metoda pro aktualizaci komentáře s informacemi o MaxProfitech
    void UpdateComment() {
        string commentText = "";

        // Přidání informací o obchodech od spuštění
        commentText += StringFormat("MaxProfity obchodů s Magic Number %d od spuštění:\n", m_magicNo);

        if(m_sessionOrderCount > 0) {
            // Máme nějaké obchody od spuštění
            for(int i = 0; i < m_sessionOrderCount; i++) {
                // Formátování profitu v bodech
                string profitText = DoubleToString(m_maxProfit[i][MP_MAX_PROFIT], 1);

                // Formátování data a času ve formátu DD.MM.RRRR HH:MM
                datetime orderTime = (datetime)m_maxProfit[i][MP_TIME];
                string dateStr = StringFormat("%02d.%02d.%04d %02d:%02d",
                                             TimeDay(orderTime),
                                             TimeMonth(orderTime),
                                             TimeYear(orderTime),
                                             TimeHour(orderTime),
                                             TimeMinute(orderTime));

                // Získání otevírací ceny a typu obchodu
                double openPrice = m_maxProfit[i][MP_OPEN_PRICE];
                int orderType = (int)m_maxProfit[i][MP_TYPE];
                string typeStr = (orderType == OP_BUY) ? "BUY" : "SELL";

                // Přidání informace o obchodu ve formátu "číslo obchodu - Datum a čas otevření - Typ obchodu - OpenPrice - MaxProfit v bodech"
                commentText += StringFormat("#%d - %s - %s - %s - MaxProfit: %s bodů\n",
                                           (int)m_maxProfit[i][MP_TICKET],
                                           dateStr,
                                           typeStr,
                                           DoubleToString(openPrice, Digits), // Formátování otevírací ceny na správný počet desetinných míst
                                           profitText);

                // Odstraníme textové objekty, pokud existují
                string objName = m_prefix + "_MaxProfit_" + IntegerToString(i);
                if(ObjectFind(0, objName) >= 0) {
                    ObjectDelete(0, objName);
                }
            }
        } else {
            // Nemáme žádné obchody od spuštění, zobrazíme nulové hodnoty
            string zeroText = DoubleToString(0.0, Digits); // Formátování nuly podle počtu desetinných míst instrumentu
            commentText += "Žádné obchody - " + zeroText + "b\n";
        }

        // Zobrazení komentáře
        Comment(commentText);
    }

    // Metoda pro sledování obchodů od spuštění
    void TrackSessionOrders() {
        // Procházení všech otevřených pozic
        int total = OrdersTotal();

        // Procházení všech otevřených pozic
        for(int i = 0; i < total; i++) {
            if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
                // Sledujeme pouze obchody na aktuálním symbolu s naším Magic Number
                if(OrderSymbol() == Symbol() && OrderMagicNumber() == m_magicNo) {
                    // Pouze market obchody (BUY/SELL)
                    if(OrderType() <= OP_SELL) {
                        // Kontrola, zda byl obchod otevřen po spuštění
                        if(OrderOpenTime() >= m_startTime) {
                            // Výpočet aktuálního profitu včetně swapů a komisí
                            double currentProfit = (OrderProfit() + OrderSwap() + OrderCommission()) / 10.0; // Dělíme 10 pro správnou hodnotu
                            int ticket = OrderTicket();

                            // Kontrola, zda je obchod již v seznamu
                            if(!IsOrderInArray(ticket) && m_sessionOrderCount < m_maxOrders) {
                                // Pokud obchod není v seznamu a máme místo, přidáme ho
                                datetime orderTime = OrderOpenTime();
                                double openPrice = NormalizeDouble(OrderOpenPrice(), Digits); // Normalizace na počet desetinných míst Digits
                                int orderType = OrderType();

                                AddOrderToArray(ticket, orderTime, openPrice, orderType, currentProfit);
                            }
                        }
                    }
                }
            }
        }

        // Procházení uzavřených obchodů v historii
        int historyTotal = OrdersHistoryTotal();

        for(int i = 0; i < historyTotal; i++) {
            if(OrderSelect(i, SELECT_BY_POS, MODE_HISTORY)) {
                // Sledujeme pouze obchody na aktuálním symbolu s naším Magic Number
                if(OrderSymbol() == Symbol() && OrderMagicNumber() == m_magicNo) {
                    // Pouze market obchody (BUY/SELL)
                    if(OrderType() <= OP_SELL) {
                        // Kontrola, zda byl obchod otevřen po spuštění
                        if(OrderOpenTime() >= m_startTime) {
                            int ticket = OrderTicket();
                            double profit = (OrderProfit() + OrderSwap() + OrderCommission()) / 10.0; // Dělíme 10 pro správnou hodnotu

                            // Kontrola, zda je obchod již v seznamu
                            if(!IsOrderInArray(ticket)) {
                                // Pokud obchod není v seznamu, přidáme ho
                                datetime orderTime = OrderOpenTime();
                                double openPrice = NormalizeDouble(OrderOpenPrice(), Digits); // Normalizace na počet desetinných míst Digits
                                int orderType = OrderType();

                                if(m_sessionOrderCount >= m_maxOrders) {
                                    // Posun starších obchodů
                                    for(int j = 0; j < m_maxOrders-1; j++) {
                                        for(int k = 0; k < 5; k++) {
                                            m_maxProfit[j][k] = m_maxProfit[j+1][k];
                                        }
                                    }

                                    // Přidání nového obchodu na poslední pozici
                                    AddOrderToArray(ticket, orderTime, openPrice, orderType, profit, m_maxOrders-1);
                                } else {
                                    // Přidání nového obchodu na konec pole
                                    AddOrderToArray(ticket, orderTime, openPrice, orderType, profit);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Pomocná metoda pro kontrolu, zda je obchod již v seznamu
    bool IsOrderInArray(int ticket) {
        for(int j = 0; j < m_sessionOrderCount; j++) {
            if((int)m_maxProfit[j][MP_TICKET] == ticket) {
                return true;
            }
        }
        return false;
    }

    // Pomocná metoda pro přidání obchodu do pole MaxProfit
    void AddOrderToArray(int ticket, datetime orderTime, double openPrice, int orderType, double currentProfit = 0.0, int index = -1) {
        // Pokud index není zadán, použijeme m_sessionOrderCount a zvýšíme ho
        int arrayIndex = (index >= 0) ? index : m_sessionOrderCount;

        m_maxProfit[arrayIndex][MP_MAX_PROFIT] = 0.0;
        m_maxProfit[arrayIndex][MP_TICKET] = (double)ticket;
        m_maxProfit[arrayIndex][MP_TIME] = (double)orderTime;
        m_maxProfit[arrayIndex][MP_OPEN_PRICE] = openPrice;
        m_maxProfit[arrayIndex][MP_TYPE] = (double)orderType;

        if(index < 0) m_sessionOrderCount++;

        if(m_debugMode) {
            string typeStr = (orderType == OP_BUY) ? "BUY" : "SELL";
            string orderStatus = (index < 0) ? "nový" : "uzavřený";
            Print("Přidán ", orderStatus, " obchod do session: Ticket=", ticket,
                  ", Profit=", currentProfit,
                  ", Čas=", TimeToString(orderTime),
                  ", OpenPrice=", openPrice,
                  ", Type=", typeStr);
        }
    }

    // Pomocná metoda pro výpočet profitu obchodu
    double CalculateOrderProfit(double openPrice, int orderType, double high, double low) {
        if(orderType == OP_BUY) {
            return (high - openPrice) / Point;
        } else if(orderType == OP_SELL) {
            return (openPrice - low) / Point;
        }
        return 0.0;
    }

    // Metoda pro získání počtu sledovaných obchodů
    int GetOrderCount() {
        return m_sessionOrderCount;
    }

    // Metoda pro získání maximálního profitu konkrétního obchodu
    double GetMaxProfit(int index) {
        if(index >= 0 && index < m_sessionOrderCount) {
            return m_maxProfit[index][MP_MAX_PROFIT];
        }
        return 0.0;
    }

    // Metoda pro získání ticketu konkrétního obchodu
    int GetTicket(int index) {
        if(index >= 0 && index < m_sessionOrderCount) {
            return (int)m_maxProfit[index][MP_TICKET];
        }
        return 0;
    }

    // Metoda pro získání typu konkrétního obchodu
    int GetOrderType(int index) {
        if(index >= 0 && index < m_sessionOrderCount) {
            return (int)m_maxProfit[index][MP_TYPE];
        }
        return -1;
    }

    // Metoda pro získání času otevření konkrétního obchodu
    datetime GetOpenTime(int index) {
        if(index >= 0 && index < m_sessionOrderCount) {
            return (datetime)m_maxProfit[index][MP_TIME];
        }
        return 0;
    }

    // Metoda pro získání otevírací ceny konkrétního obchodu
    double GetOpenPrice(int index) {
        if(index >= 0 && index < m_sessionOrderCount) {
            return m_maxProfit[index][MP_OPEN_PRICE];
        }
        return 0.0;
    }
};
